package com.kanzhun.marry.me.info.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.foundation.api.bean.VoiceAnswerBean
import com.kanzhun.foundation.api.bean.VoiceExtInfoBean
import com.kanzhun.foundation.api.callback.SendLikeBean
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.bean.RecommendInfoResponse
import com.kanzhun.foundation.model.PersonalAlbumResponse
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.info.activity.preview.item.UserPreviewItemBean
import com.kanzhun.marry.me.info.activity.preview.item.UserPreviewItemType
import com.kanzhun.utils.GsonUtils
import com.kanzhun.utils.L
import com.kanzhun.utils.T
import com.kanzhun.utils.base.LList
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine
import com.kanzhun.http.response.BaseResponse
import io.reactivex.rxjava3.disposables.CompositeDisposable

class MeUserInfoViewModel : BaseViewModel() {
    var inComeUserId: String = ""//只能用于进入预览页的网路请求，如果需要用预览页uid 需要使用接口返回的 要不扫码会为空

    var profileMetaModel: MutableLiveData<ProfileMetaModel> = MutableLiveData()
    var listScrollUpDown : MutableLiveData<Boolean> = MutableLiveData(false) // Up: false; Down: true
    var previewPersonalAlbumBeanLiveData: MutableLiveData<PersonalAlbumResponse> = MutableLiveData()

    var profileList: MutableLiveData<List<UserPreviewItemBean>> = MutableLiveData()

    var thirdId:String? = ""
    var thirdType:String? = ""
    var sourceType:String? = ""
    var securityId:String? = ""
    var sendLikedToFinish:Boolean = false
    var sendLikeBean = SendLikeBean()
    
    private val compositeDisposable = CompositeDisposable()

    fun getMSendLikeBean():SendLikeBean{
        return sendLikeBean
    }

    fun getUserInfo() {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                showLoading()
                // 创建MeApi实例
                val meApi = RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java)
                
                // 创建异步任务但处理可能的异常
                val recommendDeferred = async(Dispatchers.IO) {
                    try {
                        suspendCoroutine<BaseResponse<RecommendInfoResponse>> { continuation ->
                            val disposable = meApi.requestProfileRecommend(thirdId, thirdType, securityId)
                                .subscribe(
                                    { result -> continuation.resume(result) },
                                    { error -> continuation.resumeWithException(error) }
                                )
                            compositeDisposable.add(disposable)
                        }
                    } catch (e: Exception) {
                        L.e("ProfileRecommend", "Error fetching recommendation: ${e.message}")
                        null // 如果推荐API失败，返回null
                    }
                }
                
                val profileDeferred = async(Dispatchers.IO) {
                    try {
                    suspendCoroutine<BaseResponse<ProfileMetaModel>> { continuation ->
                        val disposable = meApi.requestProfileInfo(inComeUserId, thirdId, thirdType, sourceType, securityId)
                            .subscribe(
                                { result -> continuation.resume(result) },
                                { error -> continuation.resumeWithException(error) }
                            )
                        compositeDisposable.add(disposable)
                    }
                    } catch (e: Exception) {
                        L.e("requestProfileInfo", "Error fetching requestProfileInfo: ${e.message}")
                        null // 如果推荐API失败，返回null
                    }
                }
                
                // 等待个人信息请求完成 - 这是必须成功的请求
                val profileResponse = profileDeferred.await()
                if (profileResponse?.code == 0) {
                    val data = profileResponse.data
                    
                    // 安全地获取推荐数据，处理可能的null情况
                    val recommendInfo = try {
                        val recommendResponse = recommendDeferred.await()
                        if (recommendResponse != null && recommendResponse.code == 0) recommendResponse.data else null
                    } catch (e: Exception) {
                        L.e("ProfileRecommend", "Error awaiting recommendation: ${e.message}")
                        null
                    }
                    
                    processProfileData(data, recommendInfo)
                } else {
                    if (profileResponse?.code != QR_TIME_OUT) {
                        T.ss(profileResponse?.msg.toString())
                    }
                    showError(profileResponse?.code, profileResponse?.msg)
                }
            } catch (e: Exception) {
                L.e("getUserInfo", "Error: ${e.message}")
                showError(-1, e.message ?: "Unknown error")
            }
        }
    }

    private fun processProfileData(data: ProfileMetaModel?, mRecommendInfoResponse:RecommendInfoResponse?) {
        val list = mutableListOf<UserPreviewItemBean>()
        if (data != null) {
            if (data.blockPreview()) { //已经封禁、锁定、情侣模式等原因
                list.add(UserPreviewItemBean(data, UserPreviewItemType.ERROR, "阻断页面", false, block = data.blockPreview()))
            } else {
                // 请求相册数据
                val meApi = RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java)
                val baseResponseObservable2 = meApi.requestAlbumGetPersonalAlbum(data.securityId, true)
                HttpExecutor.execute<PersonalAlbumResponse>(
                    baseResponseObservable2,
                    object : BaseRequestCallback<PersonalAlbumResponse?>() {
                        override fun onSuccess(data: PersonalAlbumResponse?) {
                            data?.let {
                                previewPersonalAlbumBeanLiveData.value = it
                            }
                        }

                        override fun dealFail(reason: ErrorReason?) {}
                    }
                )

                list.add(UserPreviewItemBean(data, UserPreviewItemType.BASE_INFO, "基础信息", recommendInfoResponse = mRecommendInfoResponse))
                if (data.certInfo != null && data.certInfo.certTagList != null && data.certInfo.certTagList.size > 0) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.CERT_TAGS, "认证列表"))
                }
                if (data.sameActivity != null) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.OFFICE_ACTIVITY, "活动"))
                }
                if (!data.baseInfo.intro.isNullOrBlank() || !data.moments.isNullOrEmpty() || !data.userTagList.isNullOrEmpty() || LList.getCount(data.introImgList) > 0) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.ABOUT_US, R.string.me_about_me.toResourceString()))
                }

                var picCount = 0
                //ab面会影响其他顺序
                if (!data.ABFaceList.isNullOrEmpty()) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.AB_FACE, "我的AB面"))
                } else {
                    if (LList.getCount(data.storyList) > picCount) {
                        list.add(UserPreviewItemBean(data, UserPreviewItemType.STORY_NEW, "我的生活照片", childItemPosition = picCount++))
                    }
                }

                if (!data.baseInfo?.interest.isNullOrBlank() || !data.userInterestList.isNullOrEmpty() || LList.getCount(data.interestImgList) > 0) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.INTEREST, "我的兴趣爱好"))
                }
                //婚恋状况、家庭介绍
                if (!data.baseInfo?.maritalStatusInfo.isNullOrBlank()
                    || !data.baseInfo?.childbearingPreferenceInfo.isNullOrBlank()
                    || !data.baseInfo?.loveGoalInfo.isNullOrBlank()
                    || !data.baseInfo?.familyDesc.isNullOrBlank()
                    || LList.getCount(data.familyImgList) > 0
                ) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.MERRY_INFO, "婚恋状况、家庭介绍"))
                }

                if (LList.getCount(data.storyList) > picCount) {
                    list.add(
                        UserPreviewItemBean(
                            data,
                            UserPreviewItemType.STORY_NEW,
                            "生活照片",
                            childItemPosition = picCount++
                        )
                    )
                }

                //单身原因、我的理想型
                if (!data.baseInfo?.idealPartnerDesc.isNullOrBlank() || !data.baseInfo?.singleReason.isNullOrEmpty()
                ) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.MERRY_INFO_NEW, "单身原因、我的理想型"))
                }

                if (LList.getCount(data.storyList) > picCount) {
                    list.add(
                        UserPreviewItemBean(
                            data,
                            UserPreviewItemType.STORY_NEW,
                            "生活照片",
                            childItemPosition = picCount++
                        )
                    )
                }
                var answer = 0
                if (LList.getCount(data.textAnswerList) > answer) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.QA_ITEM, "我的问答", childItemPosition = answer++))
                }

                if (LList.getCount(data.storyList) > picCount) {
                    list.add(
                        UserPreviewItemBean(
                            data,
                            UserPreviewItemType.STORY_NEW,
                            "生活照片",
                            childItemPosition = picCount++
                        )
                    )
                }

                if (!data.audioAnswerList.isNullOrEmpty()) {
                    val answerMeta = data.audioAnswerList[0]
                    val answerBean = GsonUtils.copy(answerMeta, VoiceAnswerBean::class.java) as VoiceAnswerBean
                    answerBean.voiceExtInfoBean = GsonUtils.getGson().fromJson(answerMeta.extendInfo, VoiceExtInfoBean::class.java)
                    data.localVoiceAnswerBean = answerBean
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.VOICE, "我的声音"))
                }

                if (LList.getCount(data.textAnswerList) > answer) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.QA_ITEM, "我的问答", childItemPosition = answer++))
                }

                if (LList.getCount(data.storyList) > picCount) {
                    list.add(
                        UserPreviewItemBean(
                            data,
                            UserPreviewItemType.STORY_NEW,
                            "生活照片",
                            childItemPosition = picCount++
                        )
                    )
                }

                if (LList.getCount(data.storyList) > picCount) {
                    list.add(
                        UserPreviewItemBean(
                            data,
                            UserPreviewItemType.STORY_NEW,
                            "生活照片",
                            childItemPosition = picCount++
                        )
                    )
                }

                if (LList.getCount(data.storyList) > picCount) {
                    list.add(
                        UserPreviewItemBean(
                            data,
                            UserPreviewItemType.STORY_NEW,
                            "生活照片",
                            childItemPosition = picCount
                        )
                    )
                }

                if (LList.getCount(data.textAnswerList) > answer) {
                    for (i in answer until data.textAnswerList.size) {
                        list.add(UserPreviewItemBean(data, UserPreviewItemType.QA_ITEM, "我的问答", childItemPosition = i))
                    }
                }

                if (data.openScreenInfo?.status == 1 || data.loveAttitude?.status == 1 || data.romanticMode?.status == 1) {
                    list.add(UserPreviewItemBean(data, UserPreviewItemType.CHARACTER, "恋爱性格"))
                }
                list.add(UserPreviewItemBean(data, UserPreviewItemType.REPORT, "", false))
            }
        }
        
        data?.let {
            profileMetaModel.value = it
        }
        profileList.value = list
        showContent()
    }

    fun addTag(bean: ProfileInfoModel.Label,scene:Int) {
        val baseResponseObservable = RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java).addSingleTag(scene, bean.tagId)
        HttpExecutor.execute<Any>(baseResponseObservable, object : BaseRequestCallback<Any?>(true) {
            override fun onSuccess(data: Any?) {
                T.ss("添加标签成功")
                getUserInfo()
            }

            override fun dealFail(reason: ErrorReason?) {
            }

        })
    }

    override fun onCleared() {
        super.onCleared()
        compositeDisposable.clear()
    }
}
