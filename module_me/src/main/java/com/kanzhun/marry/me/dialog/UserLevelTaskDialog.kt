package com.kanzhun.marry.me.dialog

import android.content.Context
import android.view.Gravity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ProtocolHelper.Companion.parseProtocol
import com.kanzhun.foundation.adapter.MultiTypeBindingAdapter
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.databinding.CommonDialogBottomUserTaskBinding
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.api.bean.MineNoviceTaskResponse
import com.kanzhun.marry.me.databinding.MeItemUserTaskBinding
import com.kanzhun.utils.T

/**
 * 新手引导阻断弹框
 */
class UserLevelTaskDialog(val context: Context) {
    var adapter : MultiTypeBindingAdapter<MineNoviceTaskResponse.LevelList.Task, ViewBinding>? = null
    var mCommonViewBindingDialog: CommonViewBindingDialog<CommonDialogBottomUserTaskBinding>? = null
    companion object{
        fun showDialog(context: Context){
            UserLevelTaskDialog(context).show()
        }
    }
    fun show() {
        val observable = RetrofitManager.getInstance().createApi(
            MeApi::class.java
        ).queryMineNoviceTask()
        HttpExecutor.execute(observable, object : BaseRequestCallback<MineNoviceTaskResponse>() {
            override fun onSuccess(data: MineNoviceTaskResponse) {
                val response: MineNoviceTaskResponse = data
                mCommonViewBindingDialog = CommonViewBindingDialog(context,
                    mCancelable = true,
                    mCanceledOnTouchOutside = true,
                    mGravity = Gravity.BOTTOM, //这里可以设置对话框显示的位置
                    mPaddingLeft = 0,
                    mPaddingRight = 0,
                    mAnimationStyle = R.style.common_window_bottom_to_top_anim,
                    onInflateCallback = { inflater, dialog ->
                        val binding = CommonDialogBottomUserTaskBinding.inflate(inflater)
                        binding.apply {
                            init(response.locateLevel,response)
                            idIcon1.onClick {
                                adapter?.replaceData(response.levelList[0].taskList)
                                init(1,response)
                            }
                            idIcon2.onClick {
                                adapter?.replaceData(response.levelList[1].taskList)
                                init(2,response)
                            }
                            idIcon3.onClick {
                                adapter?.replaceData(response.levelList[2].taskList)
                                init(3,response)
                            }
                            adapter = multiTypeBindingAdapter(dialog)
                            idRecyclerView.adapter = adapter
                            idRecyclerView.layoutManager = LinearLayoutManager(context)
                            adapter?.replaceData(response.levelList[response.locateLevel-1].taskList)
                            tvCancel.onClick { dialog.dismiss() }

                        }
                        binding
                    })
                mCommonViewBindingDialog?.show()

            }
            override fun dealFail(reason: ErrorReason) {}
        })


    }

    fun isShow():Boolean{
        if(mCommonViewBindingDialog == null) return false
        return mCommonViewBindingDialog!!.isShowing
    }

    fun dismiss(){
        mCommonViewBindingDialog?.dismiss()
    }


    private fun CommonDialogBottomUserTaskBinding.init(click:Int,response:MineNoviceTaskResponse) {
        if (response.userLevel == 1) {
            idIcon1.setImageResource(R.drawable.icon_task_doing)
            idIcon2.setImageResource(R.drawable.icon_task_todo)
            idIcon3.setImageResource(R.drawable.icon_task_todo)
        } else if (response.userLevel == 2) {
            idIcon1.setImageResource(R.drawable.icon_task_finish)
            idIcon3.setImageResource(R.drawable.icon_task_todo)
            idLine1.setImageResource(R.drawable.icon_task_poly_line)
            idIcon2.setImageResource(R.drawable.icon_task_doing)
        } else {
            idIcon1.setImageResource(R.drawable.icon_task_finish)
            idIcon2.setImageResource(R.drawable.icon_task_finish)
            idLine1.setImageResource(R.drawable.icon_task_poly_line)
            idLine2.setImageResource(R.drawable.icon_task_poly_line)
            idIcon3.setImageResource(R.drawable.icon_task_doing)
        }
        if(click == 1){
            idContent.text = response.levelList[0].levelEquity
            idTitle.text = "完成以下任务即可解锁LV1"
            idIcon1.setImageResource(R.drawable.icon_task_selecting)
        }else if(click == 2){
            idContent.text = response.levelList[1].levelEquity
            idTitle.text = "完成以下任务即可解锁LV2"
            idIcon2.setImageResource(R.drawable.icon_task_selecting)
        }else{
            idContent.text = response.levelList[2].levelEquity
            idTitle.text = "完成以下任务即可解锁LV3"
            idIcon3.setImageResource(R.drawable.icon_task_selecting)
        }
    }

    private fun multiTypeBindingAdapter(dialog:CommonViewBindingDialog<CommonDialogBottomUserTaskBinding>) =
        buildMultiTypeAdapterByType {
            layout(MeItemUserTaskBinding::inflate) { _, bean: MineNoviceTaskResponse.LevelList.Task ->
                binding.apply {
                    idMeTaskIcon.load(bean.icon)
                    idTaskTitle.text = bean.title
                    idTaskBtn.text = bean.button.text
                    idTaskBtn2.text = bean.button.text
                    idTaskContent.text = bean.content

                    idTaskBtn.onClick {
                        if(bean.status == 0){
                            parseProtocol(bean.button.url)
                            dialog.dismiss()
                        }else if(bean.status == 1){
                            T.ss("资料审核中，请耐心等待")
                        }
                    }
                    idTaskBtn2.onClick {
                        if(bean.status == 0){
                            parseProtocol(bean.button.url)
                            dialog.dismiss()
                        }else if(bean.status == 1){
                            T.ss("资料审核中，请耐心等待")
                        }
                    }
                    //0-未提交，1-进行中；2-已完成
                    if(bean.status == 0){
                        idTaskBtn2.gone()
                        idTaskBtn.visible()
                        idTaskBtn.setBackgroundColor(R.color.common_color_292929.toResourceColor())
                    }else if(bean.status == 1){
                        idTaskBtn.gone()
                        idTaskBtn2.visible()
                        idTaskBtn.setBackgroundColor(R.color.common_color_D5D5D5.toResourceColor())
                        idTaskBtn2.setBackgroundColor(R.color.common_color_EBEBEB.toResourceColor())
                        idTaskBtn2.setTextColor(R.color.common_color_5E5E5E.toResourceColor())
                    }else if(bean.status == 2){
                        idTaskBtn.gone()
                        idTaskBtn2.visible()
                        idTaskBtn.setBackgroundColor(R.color.common_color_D5D5D5.toResourceColor())
                        idTaskBtn2.setBackgroundColor(R.color.common_color_334F90FF.toResourceColor())
                        idTaskBtn2.setTextColor(R.color.common_color_005EFF.toResourceColor())
                    }
                }
            }
        }


}