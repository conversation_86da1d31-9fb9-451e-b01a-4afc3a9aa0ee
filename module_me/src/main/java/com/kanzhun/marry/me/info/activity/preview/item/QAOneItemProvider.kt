package com.kanzhun.marry.me.info.activity.preview.item

import android.view.View
import com.kanzhun.common.kotlin.ext.blurry
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.unBlurry
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.foundation.api.callback.SendLikeBeanItemData
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.api.model.ProfileInfoModel.QuestionAnswer
import com.kanzhun.foundation.api.model.clickEnable
import com.kanzhun.foundation.api.model.isLock
import com.kanzhun.foundation.api.model.lockTxt
import com.kanzhun.foundation.api.model.pointStr
import com.kanzhun.foundation.kotlin.me.getAnswerOptionString
import com.kanzhun.foundation.model.profile.ext.isMyself
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.views.PreviewLikeView
import com.kanzhun.imageviewer.ext.ImageViewerData
import com.kanzhun.imageviewer.ext.storyViewer
import com.kanzhun.marry.me.databinding.MePreviewUserQaSectionOneBinding
import com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallbackImp
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel
import com.kanzhun.utils.L
import com.kanzhun.utils.base.LList
import com.qmuiteam.qmui.util.QMUIDisplayHelper

class QAOneItemProvider(val click: MeInfoEditFragmentCallbackImp, val viewModel: MeUserInfoViewModel) : BaseItemProvider<UserPreviewItemBean, MePreviewUserQaSectionOneBinding>() {
    override fun onBindItem(binding: MePreviewUserQaSectionOneBinding, item: UserPreviewItemBean) {
        binding.run {
            if(LList.getCount(item.data.textAnswerList) > item.childItemPosition){
                val it = item.data.textAnswerList[item.childItemPosition]
                tvQuestion.text = it.question
                tvAnswer.text = it.answer
                val option = it.getAnswerOptionString()
                if(option.isEmpty()){
                    tvOptions.gone()
                }else{
                    tvOptions.visible()
                    tvOptions.text = option
                }
                if(it.answer.isNullOrEmpty()){
                    tvAnswer.gone()
                }else{
                    tvAnswer.visible()
                }

                idLike.setData(item.data.baseInfo.hideThumb,it.thumbInfo,false,item.data.userId,
                    PreviewLikeView.LIKE_TYPE.TEXT_ANSWER,it.id,viewModel.getMSendLikeBean(),SendLikeBeanItemData(questionAnswer = it))

                it.imgs?.forEachIndexed { index, item ->
                    when(index){
                        0 -> {
                            binding.idSimpleDraweeView0.visible()
                            binding.idSimpleDraweeView1.gone()
                            binding.idSimpleDraweeView2.gone()
                            showPicture(binding.idSimpleDraweeView0,item.width,item.height,item.photo)
                            binding.idSimpleDraweeView0.onClick{
                                binding.idSimpleDraweeView0.storyViewer(listOf( item).toImageViewList(binding.idSimpleDraweeView0), 0)
                            }

                        }
                        1 -> {
                            binding.idSimpleDraweeView1.visible()
                            binding.idSimpleDraweeView2.gone()
                            showPicture(binding.idSimpleDraweeView1,item.width,item.height,item.photo)
                            binding.idSimpleDraweeView1.onClick{
                                binding.idSimpleDraweeView1.storyViewer(listOf( item).toImageViewList(binding.idSimpleDraweeView1), 0)
                            }

                        }
                        2 -> {
                            binding.idSimpleDraweeView2.visible()
                            showPicture(binding.idSimpleDraweeView2,item.width,item.height,item.photo)
                            binding.idSimpleDraweeView2.onClick{
                                binding.idSimpleDraweeView2.storyViewer(listOf( item).toImageViewList(binding.idSimpleDraweeView2), 0)
                            }
                        }
                    }
                }
                if(it.imgs.isNullOrEmpty()){
                    binding.idSimpleDraweeView0.gone()
                    binding.idSimpleDraweeView1.gone()
                    binding.idSimpleDraweeView2.gone()
                }

                val isMyself = item.data.userId.isMyself()
                val loginUserCertStatusItem = item.data.loginUserCertStatus?.textAnswer
                if(!isMyself && loginUserCertStatusItem != null && loginUserCertStatusItem!!.isLock()){
                    val content = if (loginUserCertStatusItem?.clickEnable() == true) {
                        "添加我的问答 解锁查看对方资料"
                    } else {
                        "你的资料正在审核中 通过后可解锁查看"
                    }
                    idContentLinearLayout.blurry(idBlurryLayout
                        , loginUserCertStatusItem!!.lockTxt("去添加"),content,loginUserCertStatusItem!!.clickEnable()){
//                        ProtocolHelper.parseProtocol(loginUserCertStatusItem!!.url)
                        click?.clickAddAnswer()
                        reportPoint("user-detail-locked-module-click"){
                            type = "我的问答"
                        }
                    }
                    reportPoint("user-detail-locked-module-expo"){
                        status = loginUserCertStatusItem!!.pointStr()
                        type = "我的问答"
                    }
                    idLike.gone()
                }else{
                    idContentLinearLayout.unBlurry(idBlurryLayout)
                    idLike.visible()
                }
            }
        }
    }

    private fun showPicture(view: OImageView?,dWidth:Int,dHeight:Int,url:String) {
        val width = QMUIDisplayHelper.getScreenWidth(context) - 24.dpI
        if(dWidth != 0 && dHeight != 0){
            var f = dWidth.toFloat() / dHeight.toFloat()
            if(f >= 3.0f/1.0f){
                //3:1
                val lp = view?.layoutParams
                var pHeight = width / (3.0f/1.0f)
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            }  else if(f <= 3.0f/4.0f){
                //预览时最大按3:4展示
                val lp = view?.layoutParams
                var pHeight = width / (3.0f/4.0f)
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            }else if(f >= 4.0f/3.0f){
                //预览时最大按3:4展示
                val lp = view?.layoutParams
                var pHeight = width / (4.0f/3.0f)
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            } else {
                val lp = view?.layoutParams
                var pHeight = width / f
                if(pHeight.toInt() > 560.dpI){
                    pHeight = 560.dp
                }
                if(pHeight.toInt() < 160.dpI){
                    pHeight = 160.dp
                }
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            }

        }else{
            var f = 3.0f / 4.0f
            val lp = view?.layoutParams
            var pHeight = width / f
            lp?.height = pHeight.toInt()
            view?.layoutParams = lp
        }
        //照片故事
        view?.visible()
        view?.load(url)
    }

    private fun List<ProfileInfoModel.Story?>.toImageViewList(view: View?): List<ImageViewerData> {
        return mapIndexed { index, it ->
            if (it?.video.isNullOrBlank()) {
                ImageViewerData(it?.tinyPhoto ?:"", it?.photo ?:"", view, false, it?.text?:"")
            } else {
                ImageViewerData(it?.tinyPhoto?:"", StringUtil.getPlayVideoUrl(it?.video), view, true, it?.text?:"")

            }
        }
    }

    private fun getRealAnswerList(textAnswerList: List<QuestionAnswer>, item: UserPreviewItemBean): List<QuestionAnswer> {
        return   textAnswerList.subList(item.childItemPosition, item.childItemPosition+1)
    }

}