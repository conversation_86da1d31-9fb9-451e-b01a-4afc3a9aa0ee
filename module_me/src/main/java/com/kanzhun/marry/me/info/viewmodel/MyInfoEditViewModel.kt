package com.kanzhun.marry.me.info.viewmodel

import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.ArrayMap
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableInt
import androidx.lifecycle.MutableLiveData
import com.google.gson.Gson
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.callback.VideoUploadRequestCallback
import com.kanzhun.foundation.api.model.ImageUploadModel
import com.kanzhun.foundation.api.model.VideoUploadModel
import com.kanzhun.foundation.bean.BaseStoryEditItem
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.bean.EmptyStoryItem
import com.kanzhun.foundation.bean.ImageReloadBean
import com.kanzhun.foundation.bean.PicStoryItem
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.PersonalAlbumResponse
import com.kanzhun.foundation.model.profile.AboutMeSuggestModel
import com.kanzhun.foundation.model.profile.ProfileCertModel
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.model.profile.StoryModel
import com.kanzhun.foundation.model.profile.StorySuggestV2Item
import com.kanzhun.foundation.model.profile.UserTipsBean
import com.kanzhun.foundation.model.profile.UserTipsBeanV2
import com.kanzhun.foundation.utils.ProfileHelper
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.foundation.utils.UploadFileUtil
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.api.BaseApiService
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.download.callback.DownLoadListener
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.upload.UploadRequestCallback
import com.kanzhun.marry.me.api.KMeApi
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.info.bean.MyUserInfoResponse
import com.kanzhun.marry.me.info.bean.PicStoryEditItem
import com.kanzhun.marry.me.info.bean.VideoStoryEditItem
import com.kanzhun.marry.me.info.bean.VideoStoryItem
import com.kanzhun.marry.me.info.bean.adapterbean.MyABBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyAboutMeBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyActivityPicBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyAlbumBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyAnswerBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyInfoBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyInfoEmptyBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyIntroductionBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyLikeBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyLoveCharacterBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyTitleBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyVerifyBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyVoiceBean
import com.kanzhun.utils.L
import com.kanzhun.utils.base.LList
import com.techwolf.lib.tlog.TLog
import io.reactivex.rxjava3.disposables.Disposable
import java.io.File
import java.util.LinkedList
import java.util.concurrent.ConcurrentHashMap

open class MyInfoEditViewModel : BaseViewModel() {

    var listData: MutableLiveData<MyUserInfoResponse> = MutableLiveData()
    var userTipsBean: MutableLiveData<UserTipsBean> = MutableLiveData()
    var userTipsBeanV2: MutableLiveData<UserTipsBeanV2> = MutableLiveData()
    var updateSuggestVisible: MutableLiveData<Boolean> = MutableLiveData()
    var count = ObservableInt(0)
    var progress = ObservableInt(0)
    var canShowStoryDelete = ObservableBoolean(true)

    var requestCount = ObservableInt(0)
    val storySuggestList: MutableList<StorySuggestV2Item> = LinkedList()
    var showSuggest: Boolean = true
    var previewPersonalAlbumBeanLiveData: MutableLiveData<PersonalAlbumResponse> = MutableLiveData()
    var isNew = false

    fun requestUserInfo(isNew:Boolean = false) {
        this.isNew = isNew
        requestCount.set(4)


        //1、查询用户个人页编辑态信息
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestProfileMeta(AccountHelper.getInstance().account.userId)
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<ProfileMetaModel>() {
                override fun onStart(disposable: Disposable?) {
                    super.onStart(disposable)
                    progress.set(0)
                }

                override fun onSuccess(data: ProfileMetaModel) {
                    ProfileHelper.getInstance().profileMetaModel = data
                }

                override fun dealFail(reason: ErrorReason?) {
                    showError(text = reason?.errReason)
                }

                override fun onComplete() {
                    requestCount.set(requestCount.get() - 1)
                }

            })

        //2、个人信息页提示条
        if(isNew){
            val userTipsRequest = RetrofitManager.getInstance().createApi(
                KMeApi::class.java
            ).requestUserTipsV2()

            HttpExecutor.execute(
                userTipsRequest,
                object : BaseRequestCallback<UserTipsBeanV2>() {
                    override fun onStart(disposable: Disposable?) {
                        super.onStart(disposable)
                    }

                    override fun onSuccess(data: UserTipsBeanV2) {
                        ProfileHelper.getInstance().userTipsBean = data.noticeTips
                        ProfileHelper.getInstance().mUserTipsBeanV2 = data
                    }

                    override fun dealFail(reason: ErrorReason?) {

                    }

                    override fun onComplete() {
                        requestCount.set(requestCount.get() - 1)
                    }

                })
        }else{
            val userTipsRequest = RetrofitManager.getInstance().createApi(
                KMeApi::class.java
            ).requestUserTips()

            HttpExecutor.execute(
                userTipsRequest,
                object : BaseRequestCallback<UserTipsBean>() {
                    override fun onStart(disposable: Disposable?) {
                        super.onStart(disposable)
                    }

                    override fun onSuccess(data: UserTipsBean) {
                        ProfileHelper.getInstance().userTipsBean = data
                    }

                    override fun dealFail(reason: ErrorReason?) {

                    }

                    override fun onComplete() {
                        requestCount.set(requestCount.get() - 1)
                    }

                })
        }


        //3、查询关于我推荐更新文案
        val suggestRequest = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).getAboutMeSuggest()

        HttpExecutor.execute(
            suggestRequest,
            object : BaseRequestCallback<AboutMeSuggestModel>() {
                override fun onStart(disposable: Disposable?) {
                    super.onStart(disposable)
                }

                override fun onSuccess(data: AboutMeSuggestModel) {
                    if(isNew){
                        storySuggestList.addAll( data.storySuggestV2)
                    }else{
                        if (!LList.isEmpty(data.storySuggest)) {
                            storySuggestList.addAll( data.storySuggest.map { str -> StorySuggestV2Item().apply { text = str } })
                        }
                    }
                    TLog.print(
                        "zl_log",
                        "MyInfoEditViewModel: storySuggest=%s",
                        Gson().toJson(data.storySuggest)
                    )
                }

                override fun dealFail(reason: ErrorReason?) {

                }

                override fun onComplete() {
                    requestCount.set(requestCount.get() - 1)
                }

            })

        val baseResponseObservable2 =
            RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java)
                .requestAlbumGetPersonalAlbum("",false)
        HttpExecutor.execute<PersonalAlbumResponse>(
            baseResponseObservable2,
            object : BaseRequestCallback<PersonalAlbumResponse?>() {
                override fun onSuccess(data: PersonalAlbumResponse?) {
                    data?.let {
                        previewPersonalAlbumBeanLiveData.value = it
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                }

                override fun onComplete() {
                    requestCount.set(requestCount.get() - 1)
                }

            }
        )
    }

    fun handleBatchData() {
        userTipsBean.postValue(ProfileHelper.getInstance().userTipsBean)
        userTipsBeanV2.postValue(ProfileHelper.getInstance().mUserTipsBeanV2)
        listData.postValue(
            create(
                ProfileHelper.getInstance().profileMetaModel,
                ProfileHelper.getInstance().userTipsBean,
                isNew,
                ProfileHelper.getInstance().mUserTipsBeanV2,
            )
        )
    }


    fun updateProgress() {
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestProfileMeta(AccountHelper.getInstance().account.userId)
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<ProfileMetaModel>() {
                override fun onStart(disposable: Disposable?) {
                    super.onStart(disposable)
                    progress.set(0)
                }

                override fun onSuccess(data: ProfileMetaModel) {
                    ProfileHelper.getInstance().profileMetaModel = data
                    progress.set(data.userInfoProcess)
                    requestUserTips(data, false)
                }

                override fun dealFail(reason: ErrorReason?) {
                }

            })
    }

    private fun requestUserTips(profileMetaModel: ProfileMetaModel, updateAll: Boolean) {
        if (isNew){
            val userTipsRequest = RetrofitManager.getInstance().createApi(
                KMeApi::class.java
            ).requestUserTipsV2()

            HttpExecutor.execute(
                userTipsRequest,
                object : BaseRequestCallback<UserTipsBeanV2>() {

                    override fun onSuccess(data: UserTipsBeanV2) {
                        ProfileHelper.getInstance().userTipsBean = data.noticeTips
                        ProfileHelper.getInstance().mUserTipsBeanV2 = data
                        ProfileHelper.getInstance().profileMetaModel = profileMetaModel
                        data.noticeTips?.let {
                            userTipsBean.postValue(it)
                            userTipsBeanV2.postValue(data)
                            if (updateAll) {
                                listData.postValue(create(profileMetaModel, data.noticeTips,isNew,data))
                            }
                        }
                    }

                    override fun dealFail(reason: ErrorReason?) {

                    }

                })
        }else{
            val userTipsRequest = RetrofitManager.getInstance().createApi(
                KMeApi::class.java
            ).requestUserTips()

            HttpExecutor.execute(
                userTipsRequest,
                object : BaseRequestCallback<UserTipsBean>() {

                    override fun onSuccess(data: UserTipsBean) {
                        ProfileHelper.getInstance().userTipsBean = data
                        ProfileHelper.getInstance().profileMetaModel = profileMetaModel
                        userTipsBean.postValue(data)
                        if (updateAll) {
                            listData.postValue(create(profileMetaModel, data,false,null))
                        }
                    }

                    override fun dealFail(reason: ErrorReason?) {

                    }

                })
        }

    }

    fun deleteStory(data: BaseStoryShowItem) {
        disposeRequest(disposableCache.remove(data))
        countUpload[data] = true
        val params: MutableMap<String, Any> = java.util.HashMap()
        params["id"] = data.id
        HttpExecutor.requestSimplePost(
            URLConfig.URL_USER_DEL_STORY,
            params,
            object : SimpleRequestCallback(true) {

                override fun onSuccess() {
                    deleteStoryLocalData(data)
                    storyDelete(data)
                }

                override fun dealFail(reason: ErrorReason) {
                    if (!data.addFailed.get() && data.upLoadPercent.get() < 100) {
                        val list = storySelectLiveData.value
                        if (!LList.isEmpty(list)) {
                            val newList: MutableList<BaseStoryShowItem> = java.util.ArrayList()
                            for (i in list!!.indices) {
                                val storyItem = list[i]
                                val id = storyItem.id
                                if (TextUtils.equals(id, data.id)) {
                                    val add = added[id]
                                    if (add != null) {
                                        newList.add(add)
                                        continue
                                    }
                                    val rej = rejected[storyItem.id]
                                    if (rej != null) {
                                        newList.add(rej)
                                        continue
                                    }
                                }
                                newList.add(storyItem)
                            }
                            updateThem(newList)
                        }
                    }
                }
            })
    }

    protected fun storyDelete(story: BaseStoryShowItem) {
        val id = story.id
        if (!TextUtils.isEmpty(id)) {
            rejected.remove(id)
            added.remove(id)
        }
    }

    private fun disposeRequest(disposable: Disposable?) {
        if (disposable != null && !disposable.isDisposed) {
            disposable.dispose()
        }
    }

    fun deleteStory(id: String) {
        val params: MutableMap<String, Any> = java.util.HashMap()
        params["id"] = id
        HttpExecutor.requestSimplePost(
            URLConfig.URL_USER_DEL_STORY,
            params,
            object : SimpleRequestCallback() {
                override fun onSuccess() {}
                override fun dealFail(reason: ErrorReason) {}
            })
    }

    fun deleteStoryLocalData(data: BaseStoryShowItem) {
        disposeRequest(disposableCache.remove(data))
        countUpload[data] = true
        val list = storySelectLiveData.value
        if (!LList.isEmpty(list)) {
            val newList: MutableList<BaseStoryShowItem> = java.util.ArrayList()
            for (i in list!!.indices) {
                val storyItem = list[i]
                if (storyItem === data) {
                    continue
                }
                newList.add(storyItem)
            }
            updateThem(newList)
        }
    }


    fun upLoadAddStoryFile(list: List<BaseStoryEditItem>, block: () -> Unit) {
        TLog.print(
            "zl_log",
            "1",
        );
        countUpload.clear()
        if (!LList.isEmpty<BaseStoryEditItem>(list)) {
            showLoading()
            ExecutorFactory.execLocalTask() {
//                setShowProgressBar()
                val baseStoryShowItems: MutableList<BaseStoryShowItem> =
                    ArrayList()
                val cache: List<BaseStoryShowItem>? = storySelectLiveData.getValue()
                if (!LList.isEmpty<BaseStoryShowItem>(cache)) {
                    baseStoryShowItems.addAll(cache!!)
                }
                TLog.print(
                    "zl_log",
                    "2",
                );
                for (i in list.indices) {
                    val storyEditItem = list[i]
                    if (storyEditItem is VideoStoryEditItem) {
                        val videoEdit = storyEditItem
                        val videoStory = VideoStoryItem()
                        videoStory.id = videoEdit.id
                        videoStory.file = videoEdit.file
                        videoStory.size = videoEdit.size
                        videoStory.storyText = videoEdit.changedStoryText
                        videoStory.thumbnailUri = videoEdit.thumbnailUri
                        videoStory.duration = videoEdit.duration
                        videoStory.setDownLoadPercent(100)
                        TLog.print(
                            "zl_log",
                            "MyInfoEditViewModel: setDownLoadPercent(100) line=%s",
                            374
                        );
                        baseStoryShowItems.add(videoStory)
                        upLoadIVideo(videoStory)
                    } else if (storyEditItem is PicStoryEditItem) {
                        val picEdit = storyEditItem
                        val picStory = PicStoryItem()
                        picStory.id = picEdit.id
                        val cropView = picEdit.cropView
                        if (cropView != null) {
                            val uri = cropView.cropImage
                            if (uri != null) {
                                picStory.file = uri
                            } else {
                                picStory.file = picEdit.file
                            }
                        } else {
                            picStory.file = picEdit.file
                            picStory.size = picEdit.size
                        }
                        picStory.storyText = picEdit.changedStoryText
                        picStory.setDownLoadPercent(100)
                        baseStoryShowItems.add(picStory)
                        upLoadImage(picStory)
                    }
                }
                TLog.print(
                    "zl_log",
                    "4",
                );

                ExecutorFactory.runOnUiThread() {
                    updateThem(baseStoryShowItems)
                    showContent()
                    block()
                }
                TLog.print(
                    "zl_log",
                    "5",
                );

//                hideShowProgressBar()
            }
        }
    }

    private fun updateEmptyAdd(baseStoryShowItems: MutableList<BaseStoryShowItem>) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            baseStoryShowItems.removeIf { it is EmptyStoryItem }
        } else {
            val each = baseStoryShowItems.iterator()
            while (each.hasNext()) {
                val next = each.next()
                if (next is EmptyStoryItem) {
                    each.remove()
                }
            }
        }
        if (baseStoryShowItems.size < 6) {
            count.set(baseStoryShowItems.size)
            /**
             * TODO @zhoulei
             * 更新添加按钮
             */
            val emptyCount = 6 - baseStoryShowItems.size

            val firstList =
                if (showSuggest) getAndUpdateList(emptyCount, storySuggestList) else mutableListOf()

            for (i in 0 until emptyCount) {
                val storyStr = if (showSuggest) LList.getElement(firstList, i).text else ""
                val storyIcon = if (showSuggest) LList.getElement(firstList, i).icon else ""
                baseStoryShowItems.add(EmptyStoryItem(storyStr,storyIcon))
            }
//            for (i in 1..emptyCount) {
//                baseStoryShowItems.add(EmptyStoryItem())
//            }
//            baseStoryShowItems.add(EmptyStoryItem())
        } else {
            count.set(6)
        }
        updateSuggestVisible.value = true
    }

//    private fun updateProfileEmptyAdd(baseStoryShowItems: MutableList<BaseStoryShowItem>) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            baseStoryShowItems.removeIf { it is EmptyStoryItem }
//        } else {
//            val each = baseStoryShowItems.iterator()
//            while (each.hasNext()) {
//                val next = each.next()
//                if (next is EmptyStoryItem) {
//                    each.remove()
//                }
//            }
//        }
//        if (baseStoryShowItems.size < ImageChooserConstant.MAX_COUNT) {
//            val emptyCount = ImageChooserConstant.MAX_COUNT - baseStoryShowItems.size
//            for (i in 0 until emptyCount) {
//                baseStoryShowItems.add(EmptyStoryItem())
//            }
//        }
//    }

    /**
     * 点击之后，引导文案排序，以便下次展示新的引导文案
     */
    fun getAndUpdateList(count: Int, list: MutableList<StorySuggestV2Item>): MutableList<StorySuggestV2Item> {
        val firstList: MutableList<StorySuggestV2Item> = LinkedList()
        val remainingElements: MutableList<StorySuggestV2Item> = LinkedList()
        if (LList.getCount(list) >= count) {
            firstList.addAll(list.take(count))
            remainingElements.addAll(list.drop(count))
            list.clear()
            list.addAll(remainingElements)
            list.addAll(firstList)
        }
        return firstList
    }

    private val mainHandler = Handler(Looper.getMainLooper())

    fun setCanShowStoryDeleteByData(storyItems: List<BaseStoryShowItem>) {
        if (LList.isEmpty<BaseStoryShowItem>(storyItems)) {
            canShowStoryDelete.set(true)
        } else {
            canShowStoryDelete.set(!(count.get() == 1 && !TextUtils.isEmpty(storyItems[0].id)))
        }

    }


    private val disposableCache = HashMap<BaseStoryShowItem, Disposable>()
    private val countUpload = ConcurrentHashMap<BaseStoryShowItem, Boolean>()

    /**
     * 是否有我的生活图片正在上传
     */
    fun isStoryUploading(): Boolean {
        for ((_, value) in countUpload) {
            if (!value) {
                return true
            }
        }
        return false
    }

    /**
     * 结束我的生活图片上传
     */
    fun disposeStoryRequest() {
        for (value in disposableCache.values) {
            disposeRequest(value)
        }
    }

    fun upLoadIVideo(videoStoryItem: VideoStoryItem) {
        UploadFileUtil.uploadVideo(
            UploadFileUtil.PROFILE,
            videoStoryItem.file,
            object : VideoUploadRequestCallback<VideoUploadModel>() {
                override fun onImageStart(disposable: Disposable) {
                    super.onImageStart(disposable)
                    disposableCache.put(videoStoryItem, disposable)
                }

                override fun onStart(disposable: Disposable) {
                    super.onStart(disposable)
                    disposableCache.put(videoStoryItem, disposable)
                }

                override fun onProgress(percent: Int) {
                    videoStoryItem.setUpLoadPercent(percent)
                }

                override fun onThumbnailSuccess(imageUploadModel: ImageUploadModel) {
                    videoStoryItem.thumbnailToken = imageUploadModel.token
                    videoStoryItem.thumbnail = imageUploadModel.originImageUrl
                }

                override fun onSuccess(data: VideoUploadModel) {
                    mainHandler.post {
                        videoStoryItem.token = data.token
                        videoStoryItem.url = data.url
                        val hasId = !TextUtils.isEmpty(videoStoryItem.id)
                        if (hasId) {
                            upDateVideoStory(videoStoryItem)
                        } else {
                            addVideoStory(videoStoryItem)
                        }
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                    videoStoryItem.setAddFailed(true)
                    disposableCache.remove(videoStoryItem)
                }
            })
    }

    fun addVideoStory(videoStoryItem: VideoStoryItem) {
        val map = ArrayMap<String, Any>()
        map["type"] = 3
        map["photo"] =
            if (TextUtils.isEmpty(videoStoryItem.thumbnailToken)) videoStoryItem.thumbnail else videoStoryItem.thumbnailToken
        map["video"] =
            if (TextUtils.isEmpty(videoStoryItem.token)) videoStoryItem.url else videoStoryItem.token
        map["text"] = StringUtil.trimEAndN(videoStoryItem.storyText)
        val observable = RetrofitManager.getInstance().createApi(
            MeApi::class.java
        ).requestAddStory(map)
        HttpExecutor.execute<StoryModel>(observable, object : BaseRequestCallback<StoryModel>(true) {
            override fun onStart(disposable: Disposable) {
                super.onStart(disposable)
                disposableCache[videoStoryItem] = disposable
            }

            override fun onSuccess(data: StoryModel) {
                videoStoryItem.setUpLoadPercent(100)
                videoStoryItem.id = data.id
                videoStoryItem.setAddFailed(false)
                videoStoryItem.setUploaded(true)
                setCanShowStoryDeleteByData(storySelectLiveData.getValue()!!)
                sortExistStory()
                storyUpData(videoStoryItem)
            }

            override fun dealFail(reason: ErrorReason) {
                videoStoryItem.setAddFailed(true)
                sortExistStory()
            }

            override fun onComplete() {
                super.onComplete()
                disposableCache.remove(videoStoryItem)
            }
        })
    }

    fun upDateVideoStory(videoStoryItem: VideoStoryItem) {
        val params: MutableMap<String, Any> = java.util.HashMap()
        params["id"] = videoStoryItem.id
        params["type"] = videoStoryItem.type
        params["photo"] =
            if (TextUtils.isEmpty(videoStoryItem.thumbnailToken)) videoStoryItem.thumbnail else videoStoryItem.thumbnailToken
        params["video"] =
            if (TextUtils.isEmpty(videoStoryItem.token)) videoStoryItem.url else videoStoryItem.token
        params["text"] = StringUtil.trimEAndN(videoStoryItem.storyText)
        upDateStory(params, videoStoryItem)
    }

    private fun upDateStory(params: Map<String, Any>, finalStoryItem: BaseStoryShowItem) {
        val observable = RetrofitManager.getInstance().createApi(
            BaseApiService::class.java
        ).executePost(URLConfig.URL_USER_UPDATE_STORY, params)
        HttpExecutor.requestSimple(observable, object : SimpleRequestCallback(true) {
            override fun onStart(disposable: Disposable) {
                super.onStart(disposable)
                disposableCache[finalStoryItem] = disposable
                countUpload.put(finalStoryItem, false)
            }

            override fun onSuccess() {
                finalStoryItem.setUpLoadPercent(100)
                finalStoryItem.setAddFailed(false)
                finalStoryItem.setUploaded(true)
                setCanShowStoryDeleteByData(storySelectLiveData.getValue()!!)
                storyUpData(finalStoryItem)
            }

            override fun dealFail(reason: ErrorReason) {
                if (reason.errCode == 1060) { //提交次数到了 不能再提交了
                    finalStoryItem.setUpLoadPercent(100)
                    finalStoryItem.setUploaded(false)
                } else {
                    finalStoryItem.setAddFailed(true)
                }
            }

            override fun onComplete() {
                super.onComplete()
                countUpload.put(finalStoryItem, true)
                disposableCache.remove(finalStoryItem)
            }
        })
    }

    protected fun storyUpData(story: BaseStoryShowItem) {
        var isOver = true
        countUpload.map {
            isOver = it.value && isOver
        }
        if (isOver) {
            updateProgress()
        }
        val id = story.id
        if (!TextUtils.isEmpty(id)) {
            rejected.remove(id)
            added.put(id, story)
        }
        requestProfileCert(story)
    }

    fun upLoadImage(picStoryItem: PicStoryItem) {
        UploadFileUtil.uploadImage(
            UploadFileUtil.PROFILE,
            picStoryItem.file,
            object : UploadRequestCallback<ImageUploadModel>() {
                override fun onStart(disposable: Disposable) {
                    super.onStart(disposable)
                    countUpload.put(picStoryItem, false)
                    disposableCache.put(picStoryItem, disposable)
                }

                override fun onProgress(percent: Int) {
                    super.onProgress(percent)
                    picStoryItem.setUpLoadPercent(percent)
                }

                override fun onSuccess(data: ImageUploadModel) {
                    picStoryItem.token = data.token
                    picStoryItem.url = data.originImageUrl
                    val hasId = !TextUtils.isEmpty(picStoryItem.id)
                    if (hasId) {
                        upDatePicStory(picStoryItem)
                    } else {
                        addPicStory(picStoryItem)
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                    picStoryItem.setAddFailed(true)

                }

                override fun onComplete() {
                    super.onComplete()
                    disposableCache.remove(picStoryItem)
                    countUpload.put(picStoryItem, true)
                }
            })
    }

    fun addPicStory(picStoryItem: PicStoryItem) {
        val map = ArrayMap<String, Any>()
        map["type"] = 1
        map["photo"] =
            if (TextUtils.isEmpty(picStoryItem.token)) picStoryItem.url else picStoryItem.token
        map["text"] = StringUtil.trimEAndN(picStoryItem.storyText)
        val observable = RetrofitManager.getInstance().createApi(
            MeApi::class.java
        ).requestAddStory(map)
        HttpExecutor.execute<StoryModel>(observable, object : BaseRequestCallback<StoryModel>(true) {
            override fun onStart(disposable: Disposable) {
                super.onStart(disposable)
                disposableCache[picStoryItem] = disposable
                countUpload.put(picStoryItem, false)
            }

            override fun onSuccess(data: StoryModel) {
                picStoryItem.setUpLoadPercent(100)
                picStoryItem.id = data.id
                picStoryItem.setAddFailed(false)
                picStoryItem.setUploaded(true)
                setCanShowStoryDeleteByData(storySelectLiveData.getValue()!!)
                sortExistStory()
                storyUpData(picStoryItem)
            }

            override fun dealFail(reason: ErrorReason) {
                picStoryItem.setAddFailed(true)
                sortExistStory()
            }

            override fun onComplete() {
                super.onComplete()
                countUpload.put(picStoryItem, true)
                disposableCache.remove(picStoryItem)
            }
        })
    }

    protected var added = java.util.HashMap<String, BaseStoryShowItem>() //添加成功

    protected var rejected = java.util.HashMap<String, BaseStoryShowItem>() //审核失败


    /**
     * 查询故事的审核结果
     *
     * @param story
     */
    fun requestProfileCert(story: BaseStoryShowItem) {
        val profileCert = RetrofitManager.getInstance().createApi(
            MeApi::class.java
        ).getProfileCert(story.id, 50)
        HttpExecutor.execute(profileCert, object : BaseRequestCallback<ProfileCertModel>() {
            override fun onSuccess(data: ProfileCertModel) {
                if (data != null) {
                    story.setCertStatus(data.status)
                    if (data.storyRejectInfo != null) {
                        story.textReject = data.storyRejectInfo.textReject
                        story.setPhotoReject(data.storyRejectInfo.mediaReject)
                    }
                }
            }

            override fun dealFail(reason: ErrorReason) {}
            override fun onComplete() {}
        })
    }

    public fun sortUserStory(stringBuilder: java.lang.StringBuilder) {
        val params: MutableMap<String, Any> = java.util.HashMap()
        params["ids"] = stringBuilder.toString()
        HttpExecutor.requestSimplePost(
            URLConfig.URL_USER_SORT_STORY,
            params,
            object : SimpleRequestCallback() {
                override fun onSuccess() {}
                override fun dealFail(reason: ErrorReason) {}
            })
    }

    fun sortExistStory() {
        val sortList = storySelectLiveData.value
        if (LList.isEmpty(sortList)) {
            return
        }
        val stringBuilder = StringBuilder()
        for (sort in sortList!!) {
            val id = sort.id
            if (TextUtils.isEmpty(id) && !sort.addFailed.get()) {
                return
            }
            if (!TextUtils.isEmpty(id)) {
                stringBuilder.append(id)
                stringBuilder.append(",")
            }
        }
        if (TextUtils.isEmpty(stringBuilder)) {
            return
        }
        stringBuilder.deleteCharAt(stringBuilder.length - 1)
        sortUserStory(stringBuilder)
    }

    fun upDatePicStory(picStoryItem: PicStoryItem) {
        val params: MutableMap<String, Any> = java.util.HashMap()
        params["id"] = picStoryItem.id
        params["type"] = 1
        params["photo"] =
            if (TextUtils.isEmpty(picStoryItem.token)) picStoryItem.url else picStoryItem.token
        params["text"] = StringUtil.trimEAndN(picStoryItem.storyText)
        upDateStory(params, picStoryItem)
    }


    protected var imageReloadLiveData = MutableLiveData<ImageReloadBean>()
    fun downLoadItemFile(item: BaseStoryShowItem, oImageView: OImageView?) {
        HttpExecutor.downLoadFile(item.url, object : DownLoadListener<File>() {
            override fun onSuccess(file: File) {
                item.file = Uri.fromFile(file)
                TLog.print("zl_log", "MyInfoEditViewModel: file=%s", file);
                item.setDownLoadPercent(100)
                TLog.print("zl_log", "MyInfoEditViewModel: setDownLoadPercent(100) line=%s", 769);
                imageReloadLiveData.postValue(
                    ImageReloadBean(
                        oImageView,
                        item.file
                    )
                )
            }

            override fun onFail(e: Throwable) {
                item.setDownLoadFailed(true)
            }

            override fun onProgress(percent: Int) {
                if (percent < 100) {
                    item.setDownLoadPercent(percent)
                }
            }
        })
    }


    fun upLoadStoryReEditFile(list: List<BaseStoryEditItem>) {
        countUpload.clear()
        ExecutorFactory.execLocalTask {
            val cache: List<BaseStoryShowItem> = storySelectLiveData.getValue()!!
            if (!LList.isEmpty<BaseStoryEditItem>(list) && !LList.isEmpty<BaseStoryShowItem>(cache)) {
//                setShowProgressBar()
                val baseStoryShowItems: MutableList<BaseStoryShowItem> =
                    ArrayList()
                for (i in cache.indices) {
                    val story = cache[i]
                    var storyEditItem: BaseStoryEditItem? = null
                    for (j in list.indices) {
                        val editItem = list[j]
                        if (TextUtils.equals(story.id, editItem.id)) {
                            if (editItem.isFileChanged || !TextUtils.equals(
                                    editItem.changedStoryText,
                                    editItem.storyText
                                ) || editItem is PicStoryEditItem && editItem.cropView != null && editItem.cropView.isCropRectChanged
                            ) {
                                storyEditItem = editItem
                            }
                            break
                        }
                    }
                    if (storyEditItem != null) {
                        if (storyEditItem is VideoStoryEditItem) {
                            val edit = storyEditItem
                            val show = VideoStoryItem()
                            show.id = edit.id
                            show.file = edit.file
                            show.size = edit.size
                            show.thumbnailUri = edit.thumbnailUri
                            show.duration = edit.duration
                            show.setDownLoadPercent(100)
                            TLog.print(
                                "zl_log",
                                "MyInfoEditViewModel: setDownLoadPercent(100) line=%s",
                                823
                            );
                            show.storyText = edit.changedStoryText
                            baseStoryShowItems.add(show)
                            if (edit.isFileChanged) {
                                upLoadIVideo(show)
                            } else if (!TextUtils.equals(
                                    edit.storyText,
                                    edit.changedStoryText
                                )
                            ) {
                                show.token = story.token
                                show.url = story.url
                                if (story is VideoStoryItem) {
                                    val videoStory = story
                                    show.type = videoStory.type
                                    show.thumbnailToken = videoStory.thumbnailToken
                                    show.thumbnail = videoStory.thumbnail
                                    upDateVideoStory(show)
                                }
                            }
                        } else if (storyEditItem is PicStoryEditItem) {
                            val edit = storyEditItem
                            val pic = PicStoryItem()
                            pic.id = edit.id
                            pic.setDownLoadPercent(100)
                            TLog.print(
                                "zl_log",
                                "MyInfoEditViewModel: setDownLoadPercent(100) line=%s",
                                847
                            );
                            pic.storyText = edit.changedStoryText
                            baseStoryShowItems.add(pic)
                            val cropView = edit.cropView
                            if (edit.isFileChanged || cropView != null && cropView.isCropRectChanged) {
                                val uri = cropView?.cropImage
                                if (uri != null) {
                                    pic.file = uri
                                } else {
                                    pic.file = edit.file
                                }
                                upLoadImage(pic)
                            } else if (!TextUtils.equals(
                                    edit.storyText,
                                    edit.changedStoryText
                                )
                            ) {
                                pic.file = edit.file
                                pic.size = edit.size
                                pic.token = story.token
                                pic.url = story.url
                                upDatePicStory(pic)
                            }
                        }
                    } else {
                        baseStoryShowItems.add(story)
                    }
                }
                updateThem(baseStoryShowItems)
//                hideShowProgressBar()
            }
        }
    }

    fun updateThem(baseStoryShowItems: MutableList<BaseStoryShowItem>) {
        mainHandler.post(Runnable {
            updateEmptyAdd(baseStoryShowItems)
            setCanShowStoryDeleteByData(baseStoryShowItems)
            storySelectLiveData.postValue(baseStoryShowItems)
            updateProgress()
        })
    }

    var selectFiles: List<Uri> = listOf()

    var storySelectLiveData = MutableLiveData<List<BaseStoryShowItem>>()
    var introImgLiveData = MutableLiveData<List<BaseStoryShowItem>>()
    var interestImgLiveData = MutableLiveData<List<BaseStoryShowItem>>()
    var familyImgLiveData = MutableLiveData<List<BaseStoryShowItem>>()

    var reEditSelectId: String = "" //重新编辑的时候，默认选中的id


    fun create(data: ProfileMetaModel, userTipsBean: UserTipsBean?,isNew:Boolean = false,mUserTipsBeanV2:UserTipsBeanV2? = null): MyUserInfoResponse {
        val result = MyUserInfoResponse()

        result.userInfoProcess = data.userInfoProcess
        result.canPreview = data.canPreview
        result.canNotPreviewReason = data.canNotPreviewReason
        result.userTipsBean = userTipsBean
        result.userTipsBeanV2 = mUserTipsBeanV2

        result.list = mutableListOf()
        result.list.add(MyTitleBean(data))
        result.list.add(MyVerifyBean(data))
        result.list.add(MyInfoBean(data))
        result.list.add(MyIntroductionBean(data))
        result.list.add(MyActivityPicBean(data,this))
        result.list.add(MyAlbumBean(data, this))
        result.list.add(MyLikeBean(data))
        result.list.add(MyAboutMeBean(data))
//        result.list.add(MyLabelBean(data))
        result.list.add(MyABBean(data))
        result.list.add(MyAnswerBean(data))
        result.list.add(MyVoiceBean(data))
        result.list.add(MyLoveCharacterBean(data))
        result.list.add(MyInfoEmptyBean())


        return result
    }

    fun sortStory(sorts: List<*>) {
        if (LList.isEmpty(sorts)) {
            return
        }
        val stringBuilder = StringBuilder()
        val sortList: MutableList<BaseStoryShowItem> = java.util.ArrayList()
        for (sort in sorts) {
            if (sort is BaseStoryShowItem) {
                val storyShowItem = sort
                sortList.add(storyShowItem)
                if (!TextUtils.isEmpty(storyShowItem.id)) {
                    stringBuilder.append(storyShowItem.id)
                    stringBuilder.append(",")
                }
            }
        }
        storySelectLiveData.postValue(sortList)
        if (TextUtils.isEmpty(stringBuilder)) {
            return
        }
        stringBuilder.deleteCharAt(stringBuilder.length - 1)
        sortUserStory(stringBuilder)
    }

}
