package com.kanzhun.marry.me.info.views;

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.foundation.model.profile.OpenScreen
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityCharactorScoreLayoutItemViewBinding

class CharactorLayoutView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {


    val binding: MeActivityCharactorScoreLayoutItemViewBinding

    init {
        val rootView = LayoutInflater.from(context)
            .inflate(R.layout.me_activity_charactor_score_layout_item_view, this, false)
        binding = MeActivityCharactorScoreLayoutItemViewBinding.bind(rootView)
        addView(rootView)
    }

    fun setData(mbtiScore: OpenScreen.MbtiScore,bar:String){
        try {
            binding.idGroup.post{
                binding.idIcon.load(bar)
                val layoutParams = binding.idGroup.layoutParams as MarginLayoutParams
                val imageWidth = 66.dp
                var leftMargin = (measuredWidth * mbtiScore.score / mbtiScore.maxScore - imageWidth/2.0f).toInt()
                if(leftMargin < 0){
                    leftMargin = 0
                }
                if(leftMargin + imageWidth - measuredWidth > 0){
                    leftMargin = (measuredWidth - imageWidth).toInt()
                }
                if(leftMargin != layoutParams.leftMargin){
                    layoutParams.leftMargin = leftMargin
                    binding.idGroup.layoutParams = layoutParams
                }
            }

        }catch (e: Exception){

        }

    }
}