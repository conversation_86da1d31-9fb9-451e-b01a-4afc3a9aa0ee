package com.kanzhun.marry.me.info.activity.preview.performance

import android.app.Activity
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.base.AllBaseActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.constant.LivedataKeyMatching
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.model.constant.RelationStatus
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.marry.me.databinding.MeActivityUserInfoPreviewBinding
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel
import com.techwolf.lib.tlog.TLog

/**
 * 发送喜欢相关功能
 */
class PreviewSendLikePerformance(
    val binding: MeActivityUserInfoPreviewBinding,
    val activity: AllBaseActivity,
    val pageSource: PageSource,//获取来的渠道
    val meUserInfoViewModel: MeUserInfoViewModel,
) : AbsPerformance() {
    private var mProfileMetaModel: ProfileMetaModel? = null

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        activity.liveEventBusObserve<MatchingLikeModel?>(LivedataKeyMatching.MATCH_SEND_LIKE_SUCCESS) {
            if (it == null) {
                return@liveEventBusObserve
            }
            it.pageSource = PageSource.ME_USER_INFO_PREVIEW_ACTIVITY
            val hasShowToast = it.hasShowToast ?: true
            mProfileMetaModel.run {
                MatchingPageRouter.handleSendLikeResult(activity, it)
            }
            mProfileMetaModel?.run {
                canSendLike = 0
                relationStatus =
                    if (it.likeEachOther?.myInfo != null && it.likeEachOther?.otherInfo != null) {
                        RelationStatus.FRIEND.value
                    } else {
                        RelationStatus.I_LIKE.value
                    }
                setData(this, hasShowToast)
            }

        }

    }

    fun setData(profileMetaModel: ProfileMetaModel, hasShowToast: Boolean = true) {
        mProfileMetaModel = profileMetaModel
        //如果账号已经被封禁
        if (profileMetaModel.blockPreview()) {
            binding.btSendLike.gone()
            return
        }
        if (pageSource == PageSource.F2_SEEN_ME_CHILD_FRAGMENT) {
            binding.btSendLike.pageSource = PageSource.F2_SEEN_ME_CHILD_FRAGMENT
        } else if (pageSource == PageSource.F1_RECOMMEND_CHILD_FRAGMENT) {
            binding.btSendLike.pageSource = PageSource.F1_ME_USER_INFO_PREVIEW_ACTIVITY
        } else if (pageSource == PageSource.CHAT) {
            binding.btSendLike.pageSource = PageSource.CHAT_TO_PREVIEW_ACTIVITY
        }else {
            binding.btSendLike.pageSource = pageSource
        }


        binding.btSendLike.setLikeByStatus(
            profileMetaModel.userId,
            profileMetaModel.baseInfo?.nickName,
            profileMetaModel.baseInfo?.avatar, profileMetaModel.canSendLike,
            profileMetaModel.relationStatus, !hasShowToast, profileMetaModel.securityId,
            sendLikedToFinish = meUserInfoViewModel.sendLikedToFinish,
            useAi = true
        )

        meUserInfoViewModel.getMSendLikeBean().canSend = mProfileMetaModel?.canSendLike
        meUserInfoViewModel.getMSendLikeBean().relationStatus = mProfileMetaModel?.relationStatus
        meUserInfoViewModel.getMSendLikeBean().securityId = mProfileMetaModel?.securityId
        meUserInfoViewModel.getMSendLikeBean().userName = mProfileMetaModel?.baseInfo?.nickName
        meUserInfoViewModel.getMSendLikeBean().blockPreview = profileMetaModel.blockPreview()
    }
}