package com.kanzhun.marry.me.info.viewmodel;

import static com.kanzhun.common.kotlin.ext.LiveEventBusExtKt.sendBooleanLiveEvent;
import static com.kanzhun.foundation.api.KFoundationApiKt.SCENE_IDEAL;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.Nullable;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.ai.AgcFeedbackDialog;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.profile.GuideItemBean;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.utils.L;
import com.kanzhun.utils.T;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class MeUpdateIntroViewModel extends FoundationViewModel {

    public ObservableInt indexObservable = new ObservableInt();
    public ObservableInt countObservable = new ObservableInt();
    public ObservableBoolean skipObservable = new ObservableBoolean();

    public MutableLiveData<Boolean> successLiveData = new MutableLiveData<>();
    private String preIntroText;
    public ObservableField<String> introTextObservable = new ObservableField<>();
    public ObservableField<String> errorTextObservable = new ObservableField<>();
    public String preErrorDesc;
    private MutableLiveData<Integer> errorCodeLiveData = new MutableLiveData<>();

    public MeUpdateIntroViewModel(Application application) {
        super(application);
    }

    public String getPreIntroText(){
        return Objects.requireNonNullElse(preIntroText, "");
    }

    public void initData() {
        errorTextObservable.set("");
        GuideItemBean targetGuideItem = ProfileHelper.getInstance().getTargetGuideItem();
        if (targetGuideItem != null && indexObservable.get() > 0) {
            skipObservable.set(targetGuideItem.isCanSkip());
        }
    }

    public void introContentUpdate() {
        if (TextUtils.equals(getPreIntroText(), introTextObservable.get())) {
            successLiveData.setValue(true);
            return;
        }
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("intro", getTextTrimValue());
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_INTRO, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                hideShowProgressBar();

                sendBooleanLiveEvent(LivedataKeyMe.UPDATE_INTRO,true,true,true);
                successLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason.getErrCode() == Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE) {
                    errorCodeLiveData.postValue(Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE);
                    return;
                }
                if (reason.getErrCode() == 1505) {
                    errorTextObservable.set(reason.getErrReason());
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
    }

    @Nullable
    private String getTextTrimValue() {
        if(TextUtils.isEmpty(introTextObservable.get())){
            return "";
        }
        return StringUtil.trimEAndN(introTextObservable.get());
    }

    public void beautyUpdate(){
        if (TextUtils.equals(getPreIntroText(), introTextObservable.get())) {
            successLiveData.setValue(true);
            return;
        }
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("idealPartnerDesc", getTextTrimValue());
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_IDEAL_PARTNERDESC, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                hideShowProgressBar();
                sendBooleanLiveEvent(LivedataKeyMe.BEAUTY_UPDATE,true,true,true);
                successLiveData.setValue(true);

                // 延迟弹feedback弹框
                AgcFeedbackDialog.Companion.shouldShowDelayed(SCENE_IDEAL);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason.getErrCode() == Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE) {
                    errorCodeLiveData.postValue(Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE);
                    return;
                }
                if (reason.getErrCode() == 1505) {
                    errorTextObservable.set(reason.getErrReason());
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
    }

    public void familyContentUpdate() {
        if (TextUtils.equals(getPreIntroText(), introTextObservable.get())) {
            successLiveData.setValue(true);
            return;
        }
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("familyDesc", getTextTrimValue());
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_FAMILY_DESC, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                hideShowProgressBar();
                sendBooleanLiveEvent(LivedataKeyMe.FAMILY_CONTENT_UPDATE,true,true,true);
                successLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason.getErrCode() == Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE) {
                    errorCodeLiveData.postValue(Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE);
                    return;
                }
                if (reason.getErrCode() == 1505) {
                    errorTextObservable.set(reason.getErrReason());
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
    }

    public void likeContentUpdate() {
        if (TextUtils.equals(getPreIntroText(), introTextObservable.get())) {
            successLiveData.setValue(true);
            return;
        }
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("interest", getTextTrimValue());
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_INTEREST, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                hideShowProgressBar();
                sendBooleanLiveEvent(LivedataKeyMe.LIKE_CONTENT_UPDATE,true,true,true);
                successLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason.getErrCode() == Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE) {
                    errorCodeLiveData.postValue(Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE);
                    return;
                }
                if (reason.getErrCode() == 1505) {
                    errorTextObservable.set(reason.getErrReason());
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
    }


    public MutableLiveData<Boolean> getSuccessLiveData() {
        return successLiveData;
    }

    public MutableLiveData<Integer> getErrorCodeLiveData() {
        return errorCodeLiveData;
    }


    /**
     * 更新单身原因
     */
    public void singleUpdate() {
        if (TextUtils.equals(getPreIntroText(), introTextObservable.get())) {
            successLiveData.setValue(true);
            return;
        }
        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("singleReason", getTextTrimValue());
        HttpExecutor.requestSimplePost(URLConfig.URL_USER_UPDATE_SINGLE_REASON, params, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                hideShowProgressBar();
                sendBooleanLiveEvent(LivedataKeyMe.SINGLE_UPDATE,true,true,true);
                successLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                if (reason.getErrCode() == Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE) {
                    errorCodeLiveData.postValue(Constants.REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE);
                    return;
                }
                if (reason.getErrCode() == 1505) {
                    errorTextObservable.set(reason.getErrReason());
                } else {
                    T.ss(reason.getErrReason());
                }
            }
        });
    }

    public void setPreIntroText(String stringExtra) {
        this.preIntroText = stringExtra;
    }
}