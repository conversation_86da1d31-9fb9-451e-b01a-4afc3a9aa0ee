package com.kanzhun.marry.me.info.bean.adapterbean

import android.net.Uri
import android.text.TextUtils
import com.kanzhun.foundation.api.model.ProfileInfoModel.Story
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.bean.EmptyStoryItem
import com.kanzhun.foundation.bean.PicStoryItem
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.marry.me.info.bean.VideoStoryItem
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel
import com.kanzhun.utils.L
import com.kanzhun.utils.base.LList
import com.kanzhun.utils.file.FileUtils
import com.techwolf.lib.tlog.TLog
import org.json.JSONException
import org.json.JSONObject

class MyAlbumBean(data: ProfileMetaModel, val viewModel: MyInfoEditViewModel) : MyInfoBaseBean() {

    var list: ArrayList<BaseStoryShowItem> = ArrayList()

    init {
        list.clear()
        data.storyList.forEach {
            when (it.type) {
                1 -> {
                    val picStoryItem: PicStoryItem = getPicStoryItem(it)
                    list.add(picStoryItem)
                }

                else -> {
                    val videoStoryItem: VideoStoryItem = getVideoStoryItem(it)
                    list.add(videoStoryItem)
                }
            }
        }

        viewModel.count.set(list.size)
        viewModel.setCanShowStoryDeleteByData(list)
        viewModel.storySelectLiveData.postValue(list)
        if (list.size < 6) {
            val emptyCount = 6 - list.size
            val firstList = viewModel.getAndUpdateList(emptyCount, viewModel.storySuggestList)
            for (i in 0 until emptyCount) {
                val storyStr = LList.getElement(firstList, i)
                list.add(EmptyStoryItem(storyStr.text,storyStr.icon))
            }
        }
    }

    private fun getPicStoryItem(s: Story): PicStoryItem {
        val picStoryItem = PicStoryItem()
        if (!TextUtils.isEmpty(s.photo)) {
            val filePath = HttpExecutor.getDefaultDownLoadPath()
            val file = FileUtils.getFile(s.photo, filePath)
            if (file.exists()) {
                picStoryItem.file = Uri.fromFile(file)
                picStoryItem.setDownLoadPercent(100)
                TLog.print("zl_log", "MyAlbumBean: setDownLoadPercent(100) line=%s", 59);
            }
        }
        picStoryItem.id = s.id
        picStoryItem.url = s.photo
        picStoryItem.tinyUrl = s.tinyPhoto
        picStoryItem.token = s.photo
        picStoryItem.storyText = s.text
        picStoryItem.setCertStatus(s.certStatus)
        picStoryItem.certInfo = s.certInfo
        picStoryItem.textReject = s.textReject
        picStoryItem.setPhotoReject(s.mediaReject)
        picStoryItem.setUpLoadPercent(100)
        picStoryItem.setAddFailed(false)
        return picStoryItem
    }

    private fun getVideoStoryItem(s: Story): VideoStoryItem {
        val videoStoryItem = VideoStoryItem()
        videoStoryItem.type = s.type
        if (!TextUtils.isEmpty(s.video)) {
            val filePath = HttpExecutor.getDefaultDownLoadPath()
            val file = FileUtils.getFile(s.video, filePath)
            if (file.exists()) {
                videoStoryItem.file = Uri.fromFile(file)
                videoStoryItem.setDownLoadPercent(100)
            }
        }
        videoStoryItem.id = s.id
        videoStoryItem.url = s.video
        videoStoryItem.token = s.video
        videoStoryItem.storyText = s.text
        videoStoryItem.thumbnailToken = s.photo
        videoStoryItem.thumbnail = s.photo
        videoStoryItem.setCertStatus(s.certStatus)
        videoStoryItem.certInfo = s.certInfo
        videoStoryItem.textReject = s.textReject
        videoStoryItem.setPhotoReject(s.mediaReject)
        videoStoryItem.setUpLoadPercent(100)
        videoStoryItem.setAddFailed(false)
        if (!TextUtils.isEmpty(s.extendInfo)) {
            try {
                val jsonObject = JSONObject(s.extendInfo)
                if (jsonObject.has("duration")) {
                    videoStoryItem.duration = jsonObject.getInt("duration")
                }
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
        return videoStoryItem
    }

}