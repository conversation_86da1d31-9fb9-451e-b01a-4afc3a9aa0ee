package com.kanzhun.marry.me.info.fragment

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.databinding.Observable
import androidx.databinding.Observable.OnPropertyChangedCallback
import com.gyf.immersionbar.ktx.statusBarHeight
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.dialog.showOneButtonDialog
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.getViewTopLocationOnScreen
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.activity.BaseActivity
import com.kanzhun.common.kotlin.ui.click
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.profile.UserTipsBeanV2
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.ui.popup.XGravity
import com.kanzhun.foundation.ui.popup.YGravity
import com.kanzhun.foundation.ui.popup.ZPUIPopup
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityMyInfoBinding
import com.kanzhun.marry.me.info.adapter.MyUserInfoAdapter
import com.kanzhun.marry.me.info.bean.adapterbean.MyABBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyAboutMeBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyAlbumBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyAnswerBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyInfoBaseBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyIntroductionBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyLikeBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyLoveCharacterBean
import com.kanzhun.marry.me.info.bean.adapterbean.MyVoiceBean
import com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallbackImp
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel
import com.kanzhun.marry.me.point.MePointAction
import com.kanzhun.marry.me.views.GradientBorderConstraintLayout
import com.kanzhun.utils.T
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButtonDrawable
import com.techwolf.lib.tlog.TLog

class PersonEditFragment : BaseBindingFragment<MeActivityMyInfoBinding, MyInfoEditViewModel>() {
    lateinit var click :MeInfoEditFragmentCallbackImp
    lateinit var adapterClass : MyUserInfoAdapter
    override fun preInit(arguments: Bundle) {
        click = MeInfoEditFragmentCallbackImp(activity as BaseActivity,true)
        adapterClass = MyUserInfoAdapter(activity as BaseActivity)
    }

    override fun initView() {
        mBinding.idAppTitleView.setBackButtonRes(R.drawable.common_ic_black_back) {
            requireActivity().onBackPressed()
        }
        mBinding.idAppTitleView.cancelTitleBold()
        setRightIcon(false)
//        mBinding.idAppTitleView.setRightIcon(R.drawable.me_selector_ic_preview) {
//            reportPoint(MePointAction.MYINFO_PREVIEW_CLICK)
//        }
        click.setActivityViewModel(mViewModel)
    }

    override fun initData() {
        initObserver()

        getStateLayout().showLoading()
        mBinding.idAppTitleView.gone()
        mBinding.idContent.setPadding(0,(statusBarHeight+44.dp).toInt(),0,0)
//        mViewModel.getUserInfo()
        mViewModel.requestUserInfo(true)

        liveEventBusObserve(LivedataKeyMe.AUTH_AVATAR_COMMIT_SUCCESS) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.AUTH_HOUSE_COMMIT_SUCCESS) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.AUTH_CENTER_ACTIVITY_REFRESH) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.LOVE_TEST_UPDATE) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.AUTH_EDU_COMMIT_SUCCESS) { _: String ->
            onRetry()
        }
        //问题答案有更新
        liveEventBusObserve(LivedataKeyMe.USER_UPDATE_QUESTION_ANSWER) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.AUTH_REVENUE_COMMIT_SUCCESS) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.AUTH_MARRY_COMMIT_SUCCESS) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.USER_ACTIVITY_PIC_CHANGE) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.BEAUTY_UPDATE) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.PERSON_EDIT_REFRESH) { _: Boolean ->
            onRetry()
        }

        liveEventBusObserve(LivedataKeyMe.UPDATE_INTRO) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.LIKE_CONTENT_UPDATE) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.FAMILY_CONTENT_UPDATE) { _: Boolean ->
            onRetry()
        }
        liveEventBusObserve(LivedataKeyMe.SINGLE_UPDATE) { _: Boolean ->
            onRetry()
        }

        liveEventBusObserve(LivedataKeyMe.PERSON_EDIT_SCROLL_TO) { it: String ->
            scrollStr(it, mViewModel.listData.value?.list?:arrayListOf())
        }
        reportPoint(MePointAction.MYINFO_EDIT_PAGE_EXPO)
    }

    override fun onRetry() {
        mViewModel.requestUserInfo(true)
    }

    override fun getStateLayout()= mBinding.idStateLayout

    private fun setRightIcon(canClick: Boolean, str: String? = "") {
        mBinding.idAppTitleView.getTvCommonRight().apply {
            text = "预览"
            if (background is QMUIRoundButtonDrawable) {
                val drawable: QMUIRoundButtonDrawable = background as QMUIRoundButtonDrawable
                drawable.apply {
                    setBgData(
                        ColorStateList.valueOf(
                            ContextCompat.getColor(
                                context,
                                if (canClick) R.color.common_color_191919 else R.color.common_color_CCCCCC
                            )
                        )
                    )
//                    setTextColor(if(canClick) R.color.common_color else R.color.common_color_5E5E5E)
                    setCornerRadius(24.dp)
                    setIsRadiusAdjustBounds(false)
                }
            }
            setTextColor(R.color.color_white.toResourceColor())
            visible()
            onClick {
                if (canClick) {
                    MePageRouter.jumpToInfoPreviewActivity(
                        context,
                        AccountHelper.getInstance().userId,
                        "",
                        PageSource.CHILD_ME_EDIT_INFO
                    )
                } else {
                    if (str?.isNotEmpty() == true) {
                        T.ss(str)
                    }
                }
                reportPoint(MePointAction.MYINFO_PREVIEW_CLICK)
            }
        }
    }

    private fun initObserver() {
        mViewModel.progress.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                if (mViewModel.progress.get() > 0) {
                    mBinding.idAppTitleView.setTitle("资料完整度")
                    mBinding.idAppTitleView.setTitleRightIcon(R.mipmap.me_icon_help) {
                        activity?.showOneButtonDialog(
                            title = "资料完整度",
                            content = "填写更多信息可以提升资料完整度分数，也让你的社交形象更立体。\n" +
                                    "完整度分数越高，被其他人喜欢和被回复的概率也会更大哦~",
                            buttonText = "我知道了"
                        ) {
                        }
                        reportPoint("myinfo-integrity-icon-click")
                    }
                    mBinding.idMyInfoProgressView.setData(mViewModel.progress.get(),true)
                }
            }
        })
        mViewModel.requestCount.addOnPropertyChangedCallback(object : OnPropertyChangedCallback() {
            override fun onPropertyChanged(sender: Observable?, propertyId: Int) {
                TLog.print(
                    "zl_log",
                    "MyInfoEditActivity: requestCount=%s",
                    mViewModel.requestCount.get()
                )
                if (mViewModel.requestCount.get() == 0) {
                    mViewModel.requestCount.set(4)
                    mViewModel.handleBatchData()
                }
            }
        })
        mViewModel.listData.observe(this) {
            getStateLayout().showContent()
//            statusBarColor(color = R.color.common_color_F0F0F0.toResourceColor())
            mBinding.idAppTitleView.setTitle("资料完整度")
            mBinding.idAppTitleView.setTitleRightIcon(R.mipmap.me_icon_help) {
                activity?.showOneButtonDialog(
                    title = "资料完整度",
                    content = "填写更多信息可以提升资料完整度分数，也让你的社交形象更立体。\n" +
                            "完整度分数越高，被其他人喜欢和被回复的概率也会更大哦~",
                    buttonText = "我知道了"
                ) {
                }
                reportPoint("myinfo-integrity-icon-click")
            }
            if (it.canPreview == 1) {
                setRightIcon(true)
            } else {
                setRightIcon(false, it.canNotPreviewReason)
            }
            mBinding.idMyInfoProgressView.setData(it.userInfoProcess,true)
            adapterClass.multiTypeBindingAdapter(mBinding, click, mViewModel, it.list, isNew = true)
            scrollStr(arguments?.getString(BundleConstants.BUNDLE_DATA_STRING_1),it.list)
            arguments?.putString(BundleConstants.BUNDLE_DATA_STRING_1,"")
        }

        mViewModel.userTipsBeanV2.observe(this) { bean->
            bean?.let {
                updateTips(it)
            }
        }

        mViewModel.storySelectLiveData.observe(
            this
        ) {
            ExecutorFactory.runOnUiThread {
                mViewModel.listData.value.apply {
                    var i = 0
                    this?.list?.forEach { bean ->
                        if (bean is MyAlbumBean) {
                            val tmp = mutableListOf<BaseStoryShowItem>()
                            tmp.addAll(it)
                            bean.list.clear()
                            bean.list.addAll(tmp)
                            adapterClass.notifyStoryAdapter(bean.list, mViewModel)
                            return@apply
                        }
                        i++
                    }
                }
            }
        }

        //321 自我介绍
        mViewModel.introImgLiveData.observe(this) {
            ExecutorFactory.runOnUiThread {
                mViewModel.listData.value.apply {
                    var i = 0
                    this?.list?.forEach { bean ->
                        if (bean is MyAlbumBean) {
                            val tmp = mutableListOf<BaseStoryShowItem>()
                            tmp.addAll(it)
                            bean.list.clear()
                            bean.list.addAll(tmp)
//                            adapterClass.notifyStoryAdapter(bean.list, mViewModel)
                            return@apply
                        }
                        i++
                    }

                }
            }
        }

        //322 兴趣爱好
        mViewModel.interestImgLiveData.observe(this) {
            ExecutorFactory.runOnUiThread {
                mViewModel.listData.value.apply {
                    var i = 0
                    this?.list?.forEach { bean ->
                        if (bean is MyAlbumBean) {
                            val tmp = mutableListOf<BaseStoryShowItem>()
                            tmp.addAll(it)
                            bean.list.clear()
                            bean.list.addAll(tmp)
//                            adapterClass.notifyStoryAdapter(bean.list, mViewModel)
                            return@apply
                        }
                        i++
                    }

                }
            }
        }

        //323 家庭介绍
        mViewModel.familyImgLiveData.observe(this) {
            ExecutorFactory.runOnUiThread {
                mViewModel.listData.value.apply {
                    var i = 0
                    this?.list?.forEach { bean ->
                        if (bean is MyAboutMeBean) {
                            val tmp = mutableListOf<BaseStoryShowItem>()
                            tmp.addAll(it)
                            bean.list.clear()
                            bean.list.addAll(tmp)
//                            adapterClass.notifyStoryAdapter(bean.list, mViewModel)
                            return@apply
                        }
                        i++
                    }

                }
            }
        }
    }

    private fun scrollStr(str: String?,list:MutableList<MyInfoBaseBean>) {
        if (str.isNullOrEmpty()) return
        mBinding.idNestedScrollView.post {
            val nestedScrollViewTop = mBinding.idNestedScrollView.getViewTopLocationOnScreen()
            when (str) {
                "myLife" -> {
                    val distance =
                        mBinding.idMeHolderMyInfoTitleViewNew.idAvatarAndLife.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyAlbumBean){
                            if(mViewModel.count.get() < 3){
                                showPop(mBinding.idMeHolderMyInfoTitleViewNew.idChildRoot,"在这里上传更多生活照")
                            }
                            break
                        }
                    }
                }

                "abFace" -> {//ab面
                    val distance =
                        mBinding.idMeHolderMyInfoAbViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyABBean){
                            if(item.bean.isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoAbViewNew.idScrollToThisView,"在这里上传我的AB面")
                            }
                            break
                        }
                    }

                }

                "textAnswer" -> {
                    val distance =
                        mBinding.idMeHolderMyInfoAnswerViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyAnswerBean){
                            if(item.bean.isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoAnswerViewNew.idScrollToThisView,"在这里添加我的问答")
                            }else if(item.bean.size == 1){
                                showPop(mBinding.idMeHolderMyInfoAnswerViewNew.idScrollToThisView,"在这里添加更多问答")
                            }
                            break
                        }
                    }
                }

                "audioAnswer" -> {
                    val distance =
                        mBinding.idMeHolderMyInfoVoiceViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyVoiceBean){
                            if(item.bean.isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoVoiceViewNew.idScrollToThisView,"在这里上传我的声音")
                            }
                            break
                        }
                    }
                }

                "introImg" -> {
                    val distance =
                        mBinding.idMeHolderMyInfoIntroductionViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)
                }

                "familyImg" -> {
                    val distance =
                        mBinding.idMeHolderMyInfoAboutMeViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)
                }

                "interestImg" -> {
                    val distance =
                        mBinding.idMeHolderMyInfoLikeViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)
                }
                "activityAlbum" -> {
                    val distance =
                        mBinding.idMeHolderMyActivityPicViewNew.idRoot.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)
                }
                "interest" -> {//编辑页-兴趣爱好锚点
                    val distance =
                        mBinding.idMeHolderMyInfoLikeViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyLikeBean){
                            if((item.bean.interest?:"").isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoLikeViewNew.idGradientBorderConstraintLayout,"在这里添加兴趣爱好")
                            }
                            break
                        }
                    }

                }
                "family" -> {//编辑页-家庭介绍锚点
                    val distance =
                        mBinding.idMeHolderMyInfoAboutMeViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyAboutMeBean){
                            if((item.bean.familyDesc?:"").isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoAboutMeViewNew.idFamilyLayout,"在这里填写家庭背景")
                            }
                            break
                        }
                    }
                }
                "singleReason" -> {//编辑页-单身原因锚点
                    val distance =
                        mBinding.idMeHolderMyInfoAboutMeViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyAboutMeBean){
                            if((item.bean.singleReason?:"").isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoAboutMeViewNew.idSingleLayout,"在这里填写单身原因")
                            }
                            break
                        }
                    }
                }
                "idealPartner" -> {//编辑页-理想型锚点
                    val distance =
                        mBinding.idMeHolderMyInfoAboutMeViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyAboutMeBean){
                            if((item.bean.idealPartnerDesc?:"").isEmpty()){
                                showPop(mBinding.idMeHolderMyInfoAboutMeViewNew.idBeautyLayout,"在这里填写我的理想型")
                            }
                            break
                        }
                    }
                }
                "openScreenTest" -> {//编辑页-开平测试锚点
                    val distance =
                        mBinding.idMeHolderMyInfoLoveCharactorViewNew.idChildRoot.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyLoveCharacterBean){
                            if(item.bean == null){
                                showPop(mBinding.idMeHolderMyInfoLoveCharactorViewNew.idEmpty,"在这里完成性格测评")
                            }else if (item.bean?.status == 0){
                                showPop(mBinding.idMeHolderMyInfoLoveCharactorViewNew.idEmpty,"在这里完成性格测评")
                            }
                            break
                        }
                    }
                }
                "intro" -> {//编辑页-关于我锚点
                    val distance =
                        mBinding.idMeHolderMyInfoIntroductionViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)


                    for (item in list){
                        if(item is MyIntroductionBean){
                            if((item.bean.intro?:"").length < 50){
                                showPop(mBinding.idMeHolderMyInfoIntroductionViewNew.idItem1,"在这里补充自我介绍")
                            }
                            break
                        }
                    }
                }
                "profileTag" -> {//编辑页-个性标签锚点
                    val distance =
                        mBinding.idMeHolderMyInfoIntroductionViewNew.idScrollToThisView.getViewTopLocationOnScreen() - nestedScrollViewTop
                    mBinding.idNestedScrollView.fling(distance)
                    mBinding.idNestedScrollView.smoothScrollBy(0, distance)

                    for (item in list){
                        if(item is MyIntroductionBean){
                            if(item.bean2.size < 4){
                                showPop(mBinding.idMeHolderMyInfoIntroductionViewNew.idLabel,"在这里添加更多标签")
                            }
                            break
                        }
                    }
                }
            }
        }
    }


    private fun showPop(view: GradientBorderConstraintLayout,str: String) {
        view.postDelayed({
            view.showBorder = true
            view.postDelayed({view.showBorder = false

            },3000)

            @SuppressLint("InflateParams") val rootView: View = LayoutInflater.from(
                requireContext()
            ).inflate(R.layout.me_info_edit_layout_popup, null)
            val textView: TextView = rootView.findViewById(R.id.idContent)
            textView.text = str

            val popup = ZPUIPopup.create(requireContext())
                .setOutsideTouchable(false)
                .setTouchable(false)
                .setInputMethodMode(PopupWindow.INPUT_METHOD_NOT_NEEDED)
                .setContentView(rootView)
                .apply()

            popup.showAtAnchorView(
                view,
                YGravity.ABOVE,
                XGravity.CENTER,
                0,
                -4.dpI,
                true
            )
        },500)


    }


    private fun updateTips(mUserTipsBeanV2: UserTipsBeanV2) {
        mBinding.idWarn.gone()
        val userTipsBean = mUserTipsBeanV2.noticeTips
        val completeTips = mUserTipsBeanV2.completeTips

        if(completeTips == null){
            mBinding.idMyInfoProgressView.binding.idText.gone()
        }else{
            if(completeTips.showTip == true){
                mBinding.idMyInfoProgressView.binding.idText.text = completeTips.content
                mBinding.idMyInfoProgressView.binding.idEditProgressBar.onClick {
                    ProtocolHelper.parseProtocol(completeTips.button?.url)

                    if(completeTips.button?.url?.isNotEmpty() == true){
                        reportPoint("myinfo-guidance-click") {
                            actionp3 = completeTips.content?:""
                        }
                    }

                }
                if(completeTips.button?.url?.isNotEmpty() == true){
                    mBinding.idMyInfoProgressView.binding.idRightArrow.visible()
                }else{
                    mBinding.idMyInfoProgressView.binding.idRightArrow.gone()
                }
                reportPoint("myinfo-guidance-expo") {
                     actionp3 = completeTips.content?:""
                }
            }else{
                mBinding.idMyInfoProgressView.binding.idText.gone()
            }
        }

        if (userTipsBean == null) {
            mBinding.idMyInfoProgressView.binding.idWarn.gone()
        } else {
            if (userTipsBean.showTip == true) {
                mBinding.idWarn.visible()
                mBinding.idWarnText.text = userTipsBean.content
                mBinding.idWarnText.setTextColor(Color.parseColor(userTipsBean.textColor))
                mBinding.idWarn.setBackgroundColor(Color.parseColor(userTipsBean.bgColor))
                mBinding.idWarnIcon.load(userTipsBean.icon)
                mBinding.idWarnBtn.text = userTipsBean.button?.text
                mBinding.idWarnBtn.setTextColor(Color.parseColor(userTipsBean.button?.textColor))
                mBinding.idWarnBtn.setBackgroundColor(Color.parseColor(userTipsBean.button?.color))
                mBinding.idWarn.click { _ ->
                    ProtocolHelper.parseProtocol(userTipsBean.button?.url)
                    reportPoint("myinfo-guidance-click") {
                        actionp2 = userTipsBean.content
                    }
                }

                mBinding.idMyInfoProgressView.binding.idWarn.visible()
                mBinding.idMyInfoProgressView.binding.idWarnText.text = userTipsBean.content
                mBinding.idMyInfoProgressView.binding.idWarnText.setTextColor(
                    Color.parseColor(
                        userTipsBean.textColor
                    )
                )
                mBinding.idMyInfoProgressView.binding.idWarn.setBackgroundColor(
                    Color.parseColor(
                        userTipsBean.bgColor
                    )
                )
                mBinding.idMyInfoProgressView.binding.idWarnIcon.load(userTipsBean.icon)
                mBinding.idMyInfoProgressView.binding.idWarnBtn.text = userTipsBean.button?.text
                mBinding.idMyInfoProgressView.binding.idWarnBtn.setTextColor(
                    Color.parseColor(
                        userTipsBean.button?.textColor
                    )
                )
                mBinding.idMyInfoProgressView.binding.idWarnBtn.setBackgroundColor(
                    Color.parseColor(
                        userTipsBean.button?.color
                    )
                )
                mBinding.idMyInfoProgressView.binding.idWarn.click { _ ->
                    ProtocolHelper.parseProtocol(userTipsBean.button?.url)
                    reportPoint("myinfo-guidance-click") {
                        actionp2 = userTipsBean.content
                    }
                }
                reportPoint("myinfo-guidance-expo") {
                    actionp2 = userTipsBean.content?:""
                }
            } else {
                mBinding.idWarn.gone()
                mBinding.idMyInfoProgressView.binding.idWarn.gone()
            }
        }




    }

    fun picAdd(max: Int) {
        click.clickPicAdd(max)
    }
}