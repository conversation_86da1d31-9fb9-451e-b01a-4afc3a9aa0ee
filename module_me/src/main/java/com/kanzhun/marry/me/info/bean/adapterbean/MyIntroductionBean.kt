package com.kanzhun.marry.me.info.bean.adapterbean

import android.net.Uri
import android.text.TextUtils
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.bean.BaseStoryShowItem
import com.kanzhun.foundation.bean.EmptyStoryItem
import com.kanzhun.foundation.bean.PicStoryItem
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.marry.me.info.bean.VideoStoryItem
import com.kanzhun.marry.me.info.views.ImageChooserConstant
import com.kanzhun.utils.L
import com.kanzhun.utils.file.FileUtils
import com.techwolf.lib.tlog.TLog
import org.json.JSONException
import org.json.JSONObject

class MyIntroductionBean(data: ProfileMetaModel) : MyInfoBaseBean() {
    lateinit var bean: Bean

    val bean2: List<ProfileInfoModel.Label> = data.userTagList ?: listOf()
    var list: ArrayList<BaseStoryShowItem> = ArrayList()

    init {
        bean = Bean(
            data.baseInfo.intro,
            data.baseInfo.introCertStatus,
            data.baseInfo.introCertInfo,
        )
        list.clear()
        data.introImgList?.forEach {
            when (it.type) {
                1 -> {
                    val picStoryItem: PicStoryItem = getPicStoryItem(it)
                    list.add(picStoryItem)
                }

                else -> {
                    val videoStoryItem: VideoStoryItem = getVideoStoryItem(it)
                    list.add(videoStoryItem)
                }
            }
        }

    }

    data class Bean(
        var intro: String?,
        var introCertStatus: Int,// 个人简介审核状态，1-审核中 2-已驳回 3-通过  可空
        var introCertInfo: String?,
        var introImgList: List<ProfileInfoModel.Story>? = null
    )


    private fun getPicStoryItem(s: ProfileInfoModel.Story): PicStoryItem {
        val picStoryItem = PicStoryItem()
        if (!TextUtils.isEmpty(s.photo)) {
            val filePath = HttpExecutor.getDefaultDownLoadPath()
            val file = FileUtils.getFile(s.photo, filePath)
            if (file.exists()) {
                picStoryItem.file = Uri.fromFile(file)
                picStoryItem.setDownLoadPercent(100)
                TLog.print("zl_log", "MyAlbumBean: setDownLoadPercent(100) line=%s", 59);
            }
        }
        picStoryItem.id = s.id
        picStoryItem.url = s.photo
        picStoryItem.tinyUrl = s.tinyPhoto
        picStoryItem.token = s.photo
        picStoryItem.storyText = s.text
        picStoryItem.setCertStatus(s.certStatus)
        picStoryItem.certInfo = s.certInfo
        picStoryItem.textReject = s.textReject
        picStoryItem.setPhotoReject(s.mediaReject)
        picStoryItem.setUpLoadPercent(100)
        picStoryItem.setAddFailed(false)
        return picStoryItem
    }

    private fun getVideoStoryItem(s: ProfileInfoModel.Story): VideoStoryItem {
        val videoStoryItem = VideoStoryItem()
        videoStoryItem.type = s.type
        if (!TextUtils.isEmpty(s.video)) {
            val filePath = HttpExecutor.getDefaultDownLoadPath()
            val file = FileUtils.getFile(s.video, filePath)
            if (file.exists()) {
                videoStoryItem.file = Uri.fromFile(file)
                videoStoryItem.setDownLoadPercent(100)
            }
        }
        videoStoryItem.id = s.id
        videoStoryItem.url = s.video
        videoStoryItem.token = s.video
        videoStoryItem.storyText = s.text
        videoStoryItem.thumbnailToken = s.photo
        videoStoryItem.thumbnail = s.photo
        videoStoryItem.setCertStatus(s.certStatus)
        videoStoryItem.certInfo = s.certInfo
        videoStoryItem.textReject = s.textReject
        videoStoryItem.setPhotoReject(s.mediaReject)
        videoStoryItem.setUpLoadPercent(100)
        videoStoryItem.setAddFailed(false)
        if (!TextUtils.isEmpty(s.extendInfo)) {
            try {
                val jsonObject = JSONObject(s.extendInfo)
                if (jsonObject.has("duration")) {
                    videoStoryItem.duration = jsonObject.getInt("duration")
                }
            } catch (e: JSONException) {
                e.printStackTrace()
            }
        }
        return videoStoryItem
    }
}