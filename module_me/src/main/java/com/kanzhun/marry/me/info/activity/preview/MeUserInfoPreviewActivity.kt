package com.kanzhun.marry.me.info.activity.preview

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.asBackButton
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.painterResource
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.common.kotlin.ui.recyclerview.OffsetLinearLayoutManager
import com.kanzhun.common.kotlin.ui.statusbar.addStatusPadding
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.callback.IPreviewPlayer
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.kotlin.ktx.getPageSource
import com.kanzhun.foundation.kotlin.ktx.parsePageSourceFromBundle
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.foundation.model.profile.ext.isMyself
import com.kanzhun.foundation.permission.PermissionManager
import com.kanzhun.foundation.player.OPlayerHelper
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.screenShot.BottomSheetItem
import com.kanzhun.foundation.screenShot.ComposeScreenShotShareView
import com.kanzhun.foundation.screenShot.IScreenShotCallback
import com.kanzhun.foundation.screenShot.ScreenShotSetting
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.lib_share.ShareAction
import com.kanzhun.marry.me.databinding.MeActivityUserInfoPreviewBinding
import com.kanzhun.marry.me.info.activity.preview.item.ABFaceItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.AboutUsItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.BaseInfoItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.CertTagsProvider
import com.kanzhun.marry.me.info.activity.preview.item.CharacterItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.ErrorInfoItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.HeaderViewProvider
import com.kanzhun.marry.me.info.activity.preview.item.InterestItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.MerryInfoItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.MerryInfoItemProvider2
import com.kanzhun.marry.me.info.activity.preview.item.OfficeActivityItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.PicturesItemOneProvider
import com.kanzhun.marry.me.info.activity.preview.item.QAItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.QAOneItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.ReportItemProvider
import com.kanzhun.marry.me.info.activity.preview.item.UserPreviewItemBean
import com.kanzhun.marry.me.info.activity.preview.item.UserPreviewItemType
import com.kanzhun.marry.me.info.activity.preview.item.VoiceItemProvider
import com.kanzhun.marry.me.info.activity.preview.performance.AddTagPerformance
import com.kanzhun.marry.me.info.activity.preview.performance.AutoPlayOrStopPerformance
import com.kanzhun.marry.me.info.activity.preview.performance.PreviewSendLikePerformance
import com.kanzhun.marry.me.info.activity.preview.performance.ScrollScaleTopBgPerformance
import com.kanzhun.marry.me.info.activity.preview.performance.UserInfoActivityPicPerformance
import com.kanzhun.marry.me.info.activity.preview.performance.UserInfoDurationPerformance
import com.kanzhun.marry.me.info.activity.preview.performance.UserPreviewExposePerformance
import com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallbackImp
import com.kanzhun.marry.me.info.util.ScreenShotImageHandler
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel
import com.kanzhun.marry.me.point.MePointReporter
import com.kanzhun.utils.L
import com.kanzhun.utils.ui.ActivityUtils
import com.sankuai.waimai.router.annotation.RouterUri

//个人预览页
@RouterUri(path = [MePageRouter.ME_INFO_PREVIEW_ACTIVITY])
class MeUserInfoPreviewActivity :
    BaseBindingActivity<MeActivityUserInfoPreviewBinding, MeUserInfoViewModel>(),
    IPreviewPlayer {

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    //自动播放或停止相关逻辑，包括顶部封面、个人照片视频、AB面动画
    private val mAutoPlayOrStopPerformance: AutoPlayOrStopPerformance by lazy {
        AutoPlayOrStopPerformance(mBinding)
    }

    private val mPreviewSendLikePerformance: PreviewSendLikePerformance by lazy {
        PreviewSendLikePerformance(mBinding, this, parsePageSourceFromBundle(intent), mViewModel)
    }

    val layoutManager: OffsetLinearLayoutManager by lazy {
        OffsetLinearLayoutManager(this)
    }

    private val addTagPerformance: AddTagPerformance by lazy {
        AddTagPerformance(mViewModel, this)
    }

    private val click = MeInfoEditFragmentCallbackImp(this)


    private val mAdapter: CommonListAdapter<UserPreviewItemBean> = CommonListAdapter()

    //头部控件
    private var mHeaderViewProvider: HeaderViewProvider? = null

    override var setStatusBar = {
        //默认使用白底黑字状态栏
        fullScreenAndBlackText()
    }

    var oPlayerHelper: OPlayerHelper? = null

    override fun preInit(intent: Intent) {
        mViewModel.inComeUserId = intent.getStringExtra(BundleConstants.BUNDLE_USER_ID) ?: ""
        mViewModel.lid = intent.getStringExtra(BundleConstants.BUNDLE_LID) ?: ""
        mViewModel.mSource = intent.getPageSource()
        mViewModel.mSourceFrom = intent.getStringExtra(BundleConstants.BUNDLE_PROTOCOL_FROM) ?: ""
        mViewModel.thirdId = intent.getStringExtra(BundleConstants.BUNDLE_THIRD_ID) ?: ""
        mViewModel.thirdType = intent.getStringExtra(BundleConstants.BUNDLE_THIRD_TYP) ?: ""
        mViewModel.sourceType = intent.getStringExtra(BundleConstants.BUNDLE_SOURCE_TYP) ?: ""
        mViewModel.securityId = intent.getStringExtra(BundleConstants.BUNDLE_SECURITY_ID) ?: ""
        mViewModel.sendLikedToFinish = intent.getBooleanExtra("sendLikedToFinish", false)
    }

    override fun initView() {
        mBinding.clTitle.addStatusPadding()
        mBinding.ivBack.asBackButton()
        mBinding.ivShare.gone()
        mViewModel.showLoading()
        mHeaderViewProvider = HeaderViewProvider(mBinding, this).also {
            mAutoPlayOrStopPerformance.mHeaderViewProvider = it
        }
        initRecyclerView()
        addPerformances()
        liveEventBusObserve("MePreviewPicturesViewCurrentPlayingView") { it: Boolean ->
            if (it) {
                mBinding.recyclerview.post {
                    mAutoPlayOrStopPerformance.checkItemPositionAndPlay()
                }
            }
        }

        //家庭情况
        liveEventBusObserve(LivedataKeyMe.FAMILY_CONTENT_UPDATE) { it: Boolean ->
            mViewModel.getUserInfo()
        }
        //兴趣爱好文本
        liveEventBusObserve(LivedataKeyMe.LIKE_CONTENT_UPDATE) { it: Boolean ->
            mViewModel.getUserInfo()
        }
        //兴趣爱好标签
        liveEventBusObserve(LivedataKeyMe.LIKE_CONTENT_UPDATE_TAB) { it: Boolean ->
            mViewModel.getUserInfo()
        }
        //更新了单身原因
        liveEventBusObserve(LivedataKeyMe.SINGLE_UPDATE) { it: Boolean ->
            mViewModel.getUserInfo()
        }
        //更新了我的理想型
        liveEventBusObserve(LivedataKeyMe.BEAUTY_UPDATE) { it: Boolean ->
            mViewModel.getUserInfo()
        }
        //更新了问题的答案
        liveEventBusObserve(LivedataKeyMe.USER_UPDATE_QUESTION_ANSWER) { it: Boolean ->
            mViewModel.getUserInfo()
        }

        //更新了ab
        liveEventBusObserve(LivedataKeyMe.AB_UPDATE) { it: Boolean ->
            mViewModel.getUserInfo()
        }

        //更新了mbit
        liveEventBusObserve(LivedataKeyMe.USER_UPDATE_QUESTION_TEST_COMPLETED) { it: Boolean ->
            mViewModel.getUserInfo()
        }

        liveEventBusObserve(LivedataKeyMe.USER_CP_TASK_FINISH) { it: Boolean ->
            mViewModel.getUserInfo()
        }

        mViewModel.getUserInfo()
    }

    private fun initPlayer() {
        oPlayerHelper = OPlayerHelper(this, object : OPlayerHelper.CallBack {
            override fun onFirstFrame() {
                mHeaderViewProvider?.onFirstFrame()
            }
        }).also {
            it.isLooper = true
            it.isMute = true
        }
    }

    private fun addPerformances() {
        //滑动播放、暂停等相关逻辑
        performManager.addPerformance(mAutoPlayOrStopPerformance)
        //发送喜欢相关逻辑
        performManager.addPerformance(mPreviewSendLikePerformance)
        //顶部图片滑动放大逻辑
        performManager.addPerformance(ScrollScaleTopBgPerformance(this, mBinding))
        //列表卡片曝光埋点逻辑
        performManager.addPerformance(UserPreviewExposePerformance(mViewModel,mAdapter,mBinding.recyclerview))
        //页面停留时长埋点逻辑
        performManager.addPerformance(UserInfoDurationPerformance(this,mViewModel))
        //活动相册
        performManager.addPerformance(UserInfoActivityPicPerformance(this,mViewModel,mAdapter))
        //页面截屏监听
        initScreenShot()
    }

    var screenShotSDialogFragment: ComposeDialogFragment? = null

    private fun initScreenShot() {
        if (PermissionManager.checkAllSelfPermissions(
                this@MeUserInfoPreviewActivity,
                getMediaPermission()
            )
        ) {
            val screenShotSetting = ScreenShotSetting(this)
            screenShotSetting.setScreenShotCallback(object : IScreenShotCallback {
                override fun screenShot(path: String?, uri: Uri?) {
                    if (uri.toString().isNotEmpty() == true) {
                        reportPoint("app-screenshot"){
                            source = "个人信息页"
                            peer_id = mViewModel.profileMetaModel.value?.userId?:""
                        }
                        AppThreadFactory.getMainHandler().post {
                            if(ActivityUtils.isValid(this@MeUserInfoPreviewActivity)){
                                screenShotSDialogFragment?.dismissAllowingStateLoss()
                                screenShotSDialogFragment =
                                    ComposeDialogFragment.Companion.shouldShow(this@MeUserInfoPreviewActivity) {
                                        ComposeScreenShotShareView(listOf(
                                            BottomSheetItem(
                                                "分享截图",
                                                R.mipmap.icon_screen_shot_share_pic.painterResource()
                                            ) {
                                                ScreenShotImageHandler().shareImageAndQRCode(this@MeUserInfoPreviewActivity,uri,mViewModel.profileMetaModel.value?.userId?:"")
                                                reportPoint("app-screenshot-share-click"){
                                                    source = "个人信息页"
                                                    peer_id = mViewModel.profileMetaModel.value?.userId?:""
                                                    type = "分享截图"
                                                }
                                            },
                                            BottomSheetItem(
                                                "分享主页链接",
                                                R.mipmap.icon_screen_shot_share_link.painterResource()
                                            ) {
                                                ShareAction(this@MeUserInfoPreviewActivity).share(
                                                    mViewModel.profileMetaModel.value?.userId ?: "",
                                                    "1"
                                                )
                                                reportPoint("app-screenshot-share-click"){
                                                    source = "个人信息页"
                                                    peer_id = mViewModel.profileMetaModel.value?.userId?:""
                                                    type = "分享主页链接"
                                                }
                                            }
                                        ), imageUri = uri, onCancel = {
                                            screenShotSDialogFragment?.dismissAllowingStateLoss()
                                        })
                                    }
                            }

                        }
                    }
                }

            })
            lifecycle.addObserver(screenShotSetting)
        }
    }


    private fun getMediaPermission(): List<String> {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return listOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            )
        } else {
            return listOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE
            );
        }
    }

    private fun initRecyclerView() {
        mBinding.recyclerview.layoutManager = layoutManager
        //基本信息
        mAdapter.registerItemProvider(
            UserPreviewItemType.BASE_INFO.value,
            BaseInfoItemProvider(this, mViewModel)
        )
        //活动
        mAdapter.registerItemProvider(
            UserPreviewItemType.OFFICE_ACTIVITY.value,
            OfficeActivityItemProvider(this, mViewModel)
        )
        //认证
        mAdapter.registerItemProvider(
            UserPreviewItemType.CERT_TAGS.value,
            CertTagsProvider(this, mViewModel)
        )
        //关于我
        mAdapter.registerItemProvider(
            UserPreviewItemType.ABOUT_US.value,
            AboutUsItemProvider(mViewModel) { it: ProfileInfoModel.Label, bean: ProfileInfoModel ->
                addTagPerformance.onClickTag(it, 0, bean)
            })
        //看准活动照片
        mAdapter.registerItemProvider(
            UserPreviewItemType.ACTIVITY_PIC.value,
            PreviewItemActivityPicProvider(mViewModel,activity = this@MeUserInfoPreviewActivity,mAdapter)
        )
        ///我的ab面
        mAdapter.registerItemProvider(
            UserPreviewItemType.AB_FACE.value,
            ABFaceItemProvider(mViewModel, click).also {
                mAutoPlayOrStopPerformance.mABFaceItemProvider = it
            })
        //婚恋、家庭信息
        mAdapter.registerItemProvider(
            UserPreviewItemType.MERRY_INFO.value,
            MerryInfoItemProvider(mViewModel, click)
        )
        //单身原因、我的理想型
        mAdapter.registerItemProvider(
            UserPreviewItemType.MERRY_INFO_NEW.value,
            MerryInfoItemProvider2(mViewModel,click)
        )
        //我的兴趣爱好
        mAdapter.registerItemProvider(
            UserPreviewItemType.INTEREST.value,
            InterestItemProvider(
                mViewModel,
                click
            ) { it: ProfileInfoModel.Label, bean: ProfileInfoModel ->
                addTagPerformance.onClickTag(it, 1, bean)
            })
        //单个我的照片
        mAdapter.registerItemProvider(
            UserPreviewItemType.STORY_NEW.value,
            PicturesItemOneProvider(mViewModel)
        )
        //单个问答
        mAdapter.registerItemProvider(
            UserPreviewItemType.QA_ITEM.value,
            QAOneItemProvider(click, mViewModel)
        )

        //语音
        mAdapter.registerItemProvider(
            UserPreviewItemType.VOICE.value,
            VoiceItemProvider(this, mViewModel).also {
                mAutoPlayOrStopPerformance.mVoiceItemProvider = it
                //回调赋值
                it.callback = mAutoPlayOrStopPerformance::onVoicePlayStateChange
            })
        //问答
        mAdapter.registerItemProvider(UserPreviewItemType.QA.value, QAItemProvider(click))
        //我的性格
        mAdapter.registerItemProvider(
            UserPreviewItemType.CHARACTER.value,
            CharacterItemProvider(mViewModel)
        )
        //举报
        mAdapter.registerItemProvider(
            UserPreviewItemType.REPORT.value,
            ReportItemProvider(mViewModel)
        )
        //封禁等原因的错误状态
        mAdapter.registerItemProvider(UserPreviewItemType.ERROR.value, ErrorInfoItemProvider())
        mBinding.recyclerview.adapter = mAdapter
    }


    override fun initData() {
        mViewModel.profileMetaModel.observe(this) {
            it?.run {
                mHeaderViewProvider?.setData(this,false)
                showOrHideShareButton(this)
                mPreviewSendLikePerformance.setData(this)
                mBinding.tvTitle.text = this.baseInfo?.nickName
                mBinding.ivShare.onClick {
                    MePointReporter.reportUserDetailShare(
                        userId,
                        mViewModel.profileMetaModel.value?.relationStatus ?: 0
                    )
                    ShareAction(this@MeUserInfoPreviewActivity).share(userId, "1")
                }
            }
        }
        mViewModel.profileList.observe(this) {
            mAdapter.setNewInstance(it.toMutableList())
            //重置两个状态
            mAutoPlayOrStopPerformance.mPictureItemPosition = -1
            mAutoPlayOrStopPerformance.mAbFaceItemPosition = -1
            it.forEachIndexed { index, userPreviewItemBean ->
                //记录对应位置
                if (userPreviewItemBean.getLocalItemType() == UserPreviewItemType.STORY_NEW.value) {
                    mAutoPlayOrStopPerformance.mPictureItemPosition = index
                }
                //记录对应位置
                if (userPreviewItemBean.getLocalItemType() == UserPreviewItemType.AB_FACE.value) {
                    mAutoPlayOrStopPerformance.mAbFaceItemPosition = index
                }
            }
        }
    }

    fun getAdapterData(): MutableList<UserPreviewItemBean> {
        return mAdapter.data
    }

    private fun showOrHideShareButton(bean: ProfileMetaModel) {
        bean.run {
            if (bean.userId.isMyself()) {
                if (this.userCurrentRelation == 2 || this.userCurrentRelation == 3) {
                    mBinding.ivShare.visible(false)
                } else {
                    mBinding.ivShare.visible(!this.blockPreview())
                }
            } else {
                mBinding.ivShare.visible(!this.blockPreview())
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (mViewModel.profileMetaModel.value?.securityId?.isNotEmpty() == true) {
            val params: MutableMap<String, Any?> = mutableMapOf()
            params["securityId"] = mViewModel.profileMetaModel.value?.securityId
            params["type"] = 1
            HttpExecutor.requestSimplePost(
                URLConfig.URL_DATA_PUSH_BEHAVIOR,
                params,
                object : SimpleRequestCallback() {
                    override fun onSuccess() {
                    }

                    override fun dealFail(reason: ErrorReason) {

                    }
                })
        }

    }


    override fun onRetry() {
        mViewModel.getUserInfo()
    }

    override fun getStateLayout() = mBinding.stateLayout

    override fun getHeaderPlayer(): OPlayerHelper? {
        if (oPlayerHelper == null) {
            initPlayer()
        }
        return oPlayerHelper
    }
}












