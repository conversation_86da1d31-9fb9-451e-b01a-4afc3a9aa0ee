package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable

/**
 * 公共卡片： 1 话题游戏提示卡片
 */
class MessageForCommonCard : ChatMessage() {
    var commonCardInfo: CommonCardInfo? = null

    init {
        mediaType = MessageConstants.MSG_COMMON_CARD
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val commonCard = message.body?.commonCard ?: return
            commonCardInfo = CommonCardInfo(
                commonCard.sourceId,
                commonCard.type,
                commonCard.title,
                commonCard.content
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val cardInfo = GsonUtils.getGson().fromJson(content, CommonCardInfo::class.java)
        if (cardInfo != null) {
            commonCardInfo = cardInfo
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(commonCardInfo)
    }

    override fun getSummary(): String {
        if (isWXCard()){
            return "[交换微信号]"
        }
        return commonCardInfo?.content ?: ""
    }

    /**
     * 是否是话题游戏提示卡片
     */
    fun isTopicGameHintCard(): Boolean {
        return commonCardInfo?.type == MessageConstants.MSG_COMMON_CARD_TYPE_TOPIC_GAME
    }

    /**
     * 是否是动态推荐卡片
     */
    fun isDynamicRecommendCard(): Boolean {
        return commonCardInfo?.type == MessageConstants.MSG_COMMON_CARD_TYPE_DYNAMIC_RECOMMEND
    }

    fun isWXCard(): Boolean{
        return commonCardInfo?.type == MessageConstants.MSG_COMMON_CARD_TYPE_WECHAT_EXCHANGE
    }
}

data class CommonCardInfo(
    val sourceId: String? = null,
    val type: Int = 0, //公共卡片： 1 话题游戏提示卡片 2 动态推荐
    val title: String? = null,
    val content: String? = null,
    val extend: String? = null // 扩展数据（结构化数据）
) : Serializable