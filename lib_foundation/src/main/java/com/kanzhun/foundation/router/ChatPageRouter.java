package com.kanzhun.foundation.router;

import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.getPageSourceBundle;
import static com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_CHRISTMAS;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.dialog.CommonBaseDialog;
import com.kanzhun.common.dialog.CommonLayoutDialog;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.api.model.MatchingPageInfo;
import com.kanzhun.foundation.databinding.ChatRelationshipDialogBinding;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.router.service.IChatRouterService;
import com.kanzhun.foundation.utils.AppTheme;
import com.kanzhun.utils.rxbus.RxBus;
import com.sankuai.waimai.router.Router;

public class ChatPageRouter {

    public static final String CHAT_SERVICE = "chat_service";

    private static final String MODULE_NAME_CHAT = "/chat";
    public static final String SINGLE_CHAT_ACTIVITY = MODULE_NAME_CHAT + "/single_chat_activity";
    public static final String SANTA_CLAUS_ACTIVITY = MODULE_NAME_CHAT + "/christmas_activity";
    public static final String YUE_YUE_ACTIVITY = MODULE_NAME_CHAT + "/yueyue_activity";

    public static final String DATE_CARD_ACTIVITY = MODULE_NAME_CHAT + "/date_card_activity";
    public static final String DATE_CARD_DETAIL_ACTIVITY = MODULE_NAME_CHAT + "/date_card_detail_activity";
    public static final String DATE_CARD_INPUT_ACTIVITY = MODULE_NAME_CHAT + "/date_card_input_activity";
    public static final String LOVE_CARD_ACTIVITY = MODULE_NAME_CHAT + "/love_card_activity";
    public static final String LOVE_CARD_DETAIL_ACTIVITY = MODULE_NAME_CHAT + "/love_card_detail_activity";
    public static final String LOVE_CARD_INPUT_ACTIVITY = MODULE_NAME_CHAT + "/love_card_input_activity";
    public static final String LIKE_RELATED_ACTIVITY = MODULE_NAME_CHAT + "/like_related_activity";
    public static final String CHANGE_WX_ACTIVITY = MODULE_NAME_CHAT + "/change_wx_activity";

    /**
     * 跳转到圣诞限定（官方活动）
     */
    public static void jumpToSantaClausActivity(@NonNull Context context, @NonNull PageSource pageSource) {
        Bundle bundle = getPageSourceBundle(pageSource);
        bundle.putString(BundleConstants.BUNDLE_USER_ID, AppTheme.INSTANCE.getSystemChristmasId());
        bundle.putLong(BundleConstants.BUNDLE_SYSTEM_ID, SYS_ID_OFFICIAL_CHRISTMAS);
        AppUtil.startUri(context, SANTA_CLAUS_ACTIVITY, bundle);
    }

    /**
     * 跳转到小红娘月月
     */
    public static void jumpToYueYueActivity(Context context, String chatId, PageSource pageSource) {
        Bundle bundle = getPageSourceBundle(pageSource);
        bundle.putString(BundleConstants.BUNDLE_USER_ID, chatId);
        bundle.putLong(BundleConstants.BUNDLE_SYSTEM_ID, MessageConstants.SYS_ID_OFFICIAL_YUEYUE);
        AppUtil.startUri(context, YUE_YUE_ACTIVITY, bundle);
    }

    /**
     * 跳转到单聊聊天页
     */
    public static void jumpToSingleChatActivity(Context context, String userId, PageSource pageSource) {
        Bundle bundle = getPageSourceBundle(pageSource);
        bundle.putString(BundleConstants.BUNDLE_USER_ID, userId);
        AppUtil.startUri(context, SINGLE_CHAT_ACTIVITY, bundle);
    }

    /**
     * 跳转到单聊聊天页
     */
    public static void jumpToSingleChatActivity(Context context, String userId, long systemId) {
        if (systemId == SYS_ID_OFFICIAL_CHRISTMAS) {
            jumpToSantaClausActivity(context, PageSource.NONE);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_USER_ID, userId);
        bundle.putLong(BundleConstants.BUNDLE_SYSTEM_ID, systemId);
        AppUtil.startUri(context, SINGLE_CHAT_ACTIVITY, bundle);
    }

    public static void jumpToSingleChatActivityAndScroll(Context context, String userId, long systemId, boolean scrollToBottom) {
        if (systemId == SYS_ID_OFFICIAL_CHRISTMAS) {
            jumpToSantaClausActivity(context, PageSource.NONE);
            return;
        }
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_USER_ID, userId);
        bundle.putLong(BundleConstants.BUNDLE_SYSTEM_ID, systemId);
        bundle.putBoolean(BundleConstants.BUNDLE_TO_CHAT_SCROLL_TO_BOTTOM, scrollToBottom);
        AppUtil.startUri(context, SINGLE_CHAT_ACTIVITY, bundle);
    }

    /**
     * 跳转到单聊聊天页
     */
    public static void jumpToSingleChatActivity(Context context, String userId, String avatar, String nickName, boolean isFried) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_USER_ID, userId);
        bundle.putString(BundleConstants.BUNDLE_CHAT_AVATAR, avatar);
        bundle.putString(BundleConstants.BUNDLE_CHAT_NICK_NAME, nickName);
        bundle.putBoolean(BundleConstants.BUNDLE_CHAT_FRIEND, isFried);
        AppUtil.startUri(context, SINGLE_CHAT_ACTIVITY, bundle);
    }

    /**
     * 跳转到单聊聊天页,插入临时好友
     */
    public static void jumpToSingleChatActivityWithTemporaryContact(Context context, String userId,
                                                                    String avatar, String nickName,
                                                                    int relationStatus,
                                                                    boolean isFried, String securityId, PageSource pageSource) {

        jumpToSingleChatActivityWithTemporaryContact(context, userId, avatar, nickName, null, null, relationStatus, isFried, securityId, pageSource);

    }

    /**
     * 跳转到单聊聊天页,插入临时好友
     */
    public static void jumpToSingleChatActivityWithTemporaryContact(Context context, String userId,
                                                                    String avatar, String nickName, String moodIcon, String moodTitle,
                                                                    int relationStatus,
                                                                    boolean isFried, String securityId, PageSource pageSource) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contact == null) {
            contact = new Contact();
            contact.setUserId(userId);
            contact.setTinyAvatar(avatar);
            contact.setNickName(nickName);
            contact.setMoodIcon(moodIcon);
            contact.setMoodTitle(moodTitle);
            contact.setSecurityId(securityId);
            ServiceManager.getInstance().getContactService().insertTemporaryContact(contact);
        }
        Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(userId, MessageConstants.MSG_SINGLE_CHAT);
        if (conversation == null) {
            conversation = Conversation.createConversation(userId, avatar, nickName, moodIcon, moodTitle, MessageConstants.MSG_SINGLE_CHAT);
        } else {
            conversation.setChatId(userId);
            conversation.setAvatar(avatar);
            conversation.setNickName(nickName);
            conversation.setMoodIcon(moodIcon);
            conversation.setMoodTitle(moodTitle);
            conversation.setType(MessageConstants.MSG_SINGLE_CHAT);
            if (conversation.getTags() == null) {
                conversation.setTags(contact.getTags());
            }
        }

        if (conversation.getTags() == null) {
            conversation.setTags(contact.getTags());
        }

        if (conversation.getRelationStatus() == 0) {
            conversation.setRelationStatus(relationStatus);
        }
        ServiceManager.getInstance().getConversationService().updateConversation(conversation);
        Bundle bundle = getPageSourceBundle(pageSource);
        bundle.putString(BundleConstants.BUNDLE_USER_ID, userId);
        bundle.putString(BundleConstants.BUNDLE_CHAT_AVATAR, avatar);
        bundle.putString(BundleConstants.BUNDLE_CHAT_NICK_NAME, nickName);
        bundle.putBoolean(BundleConstants.BUNDLE_CHAT_FRIEND, isFried);
        bundle.putString(BundleConstants.BUNDLE_CHAT_MOOD_ICON, moodIcon);
        bundle.putString(BundleConstants.BUNDLE_CHAT_MOOD_TITLE, moodTitle);
        AppUtil.startUri(context, SINGLE_CHAT_ACTIVITY, bundle);
    }

    /**
     * H5跳转到单聊聊天页
     */
    public static void jumpToSingleChatActivityForH5(Context context, String userId) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(userId, MessageConstants.MSG_SINGLE_CHAT);
                if (conversation != null) {
                    ExecutorFactory.execMainTask(new Runnable() {
                        @Override
                        public void run() {
                            jumpToSingleChatActivity(context, userId, conversation.getSystemId());
                        }
                    });
                } else {
                    RxBus.getInstance().post("switch", Constants.POST_TAG_CHAT_REQUIREMENT);
                }
            }
        });
    }

    /**
     * 跳转到相识卡页
     */
    public static void jumpToDateCardActivity(Context context, String chatId) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
        AppUtil.startUri(context, DATE_CARD_ACTIVITY, bundle, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 跳转到相识卡页
     */
    public static void jumpToDateCardDetailActivity(Context context, String msgId, String msgSenderId) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_MSG_ID, msgId);
        bundle.putString(BundleConstants.BUNDLE_MSG_SENDER_ID, msgSenderId);
        AppUtil.startUri(context, DATE_CARD_DETAIL_ACTIVITY, bundle, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 跳转到相识卡输入页
     */
    public static void jumpToDateCardInputActivity(Context context, String chatId) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
        AppUtil.startUri(context, DATE_CARD_INPUT_ACTIVITY, bundle);
    }

    /**
     * 跳转到表白信页
     */
    public static void jumpToLoveCardActivity(Context context, String chatId, String fromUsername, String toUsername) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
        bundle.putString(BundleConstants.BUNDLE_FROM_NAME, fromUsername);
        bundle.putString(BundleConstants.BUNDLE_TO_NAME, toUsername);
        AppUtil.startUri(context, LOVE_CARD_ACTIVITY, bundle, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 跳转到表白信页
     */
    public static void jumpToLoveCardDetailActivity(Context context, String msgId, String msgSenderId) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_MSG_ID, msgId);
        bundle.putString(BundleConstants.BUNDLE_MSG_SENDER_ID, msgSenderId);
        AppUtil.startUri(context, LOVE_CARD_DETAIL_ACTIVITY, bundle, ActivityAnimType.UP_GLIDE);
    }

    /**
     * 跳转到表白信输入页
     */
    public static void jumpToLoveCardInputActivity(Context context, String chatId, String fromUsername, String toUsername) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, chatId);
        bundle.putString(BundleConstants.BUNDLE_FROM_NAME, fromUsername);
        bundle.putString(BundleConstants.BUNDLE_TO_NAME, toUsername);
        AppUtil.startUri(context, LOVE_CARD_INPUT_ACTIVITY, bundle);
    }

    /**
     * 跳转到匹配卡片页
     */
    public static void jumpToMatchingCardActivity(Activity activity, MatchingPageInfo pageInfo) {
        if (pageInfo == null || pageInfo.matchStatus <= 1
                || pageInfo.friend == null || TextUtils.isEmpty(pageInfo.friend.nickName)) return;

        ChatRelationshipDialogBinding binding = ChatRelationshipDialogBinding.inflate(
                LayoutInflater.from(activity), null, false);
        binding.setPageInfo(pageInfo);
        CommonLayoutDialog dialog = new CommonLayoutDialog.Builder(activity)
                .setContentView(binding.getRoot())
                .setGravity(Gravity.TOP)
                .setPadding(0, 0)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .add(R.id.iv_close)
                .add(R.id.btn_i_see)
                .setOnItemClickListener(new CommonBaseDialog.OnItemClickListener() {
                    @Override
                    public void onItemClick(Dialog dialog, View view) {
                        dialog.dismiss();
                        ServiceManager.getInstance().getSettingService().getMatchingGuideLiveData().setValue(pageInfo);
                    }
                })
                .create(R.style.common_dialog_no_dim);
        binding.getRoot().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        }, 5000);
        dialog.show();
    }

    /**
     * 跳转到喜欢我/我喜欢页
     *
     * @param likeRelatedType 1 喜欢我，2 我喜欢
     */
    public static void jumpToLikeRelatedActivity(Context context, int likeRelatedType, int likeMeCount, int pageSource) {
        Bundle bundle = new Bundle();
        bundle.putInt(BundleConstants.BUNDLE_LIKE_RELATED_TYPE, likeRelatedType);
        bundle.putInt(BundleConstants.BUNDLE_LIKE_ME_COUNT, likeMeCount);
        bundle.putInt(BundleConstants.BUNDLE_DATA_INT, pageSource);
        AppUtil.startUri(context, LIKE_RELATED_ACTIVITY, bundle);
    }

    /*
     * 保存表情数据
     */
    public static void saveEmotionData(String data) {
        IChatRouterService s = Router.getService(IChatRouterService.class, ChatPageRouter.CHAT_SERVICE);
        if (s != null) {
            s.saveEmotionData(data);
        }
    }

    public static void jumpToChangeWxActivity(@NonNull Context context, String uid) {
        Bundle bundle = new Bundle();
        bundle.putString(BundleConstants.BUNDLE_CHAT_ID, uid);
        AppUtil.startUri(context, CHANGE_WX_ACTIVITY, bundle);
    }
}
