package com.kanzhun.foundation.ai

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.api.KFoundationApi
import com.kanzhun.foundation.api.SCENE_HOBBY
import com.kanzhun.foundation.api.response.Questionnaire
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.http.createApi
import com.kanzhun.utils.T
import com.techwolf.lib.tlog.TLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

private const val TAG = "AiViewModel"

class AiViewModel : BaseViewModel() {
    val localBeanGroup = listOf(
        Bean(id = 1, name = "语气风格不喜欢"),
        Bean(id = 2, name = "内容不够严肃"),
        Bean(id = 3, name = "内容不够生动"),
        Bean(id = 4, name = "丢失了我的信息"),
        Bean(id = 5, name = "AI味儿太重"),
        Bean(id = 6, name = "其他"),
    )

    var inGenerating by mutableStateOf(false)
    var dynamicAgcContent by mutableStateOf("")

    var regenerateStep by mutableIntStateOf(1)
    var regenerateCount by mutableIntStateOf(3)

    var questionnaire by mutableStateOf(Questionnaire())

    fun initQuestionnaire(getStringScene: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                questionnaire =
                    createApi(KFoundationApi::class.java).loadRecommendTagQuestionnaire(getStringScene = getStringScene)

                questionnaire.apply {
                    serverOptionsItem = questionnaire.optionsItem?.mapIndexed { index, item ->
                        Bean(
                            id = index,
                            name = item
                        )
                    }
                }
            } catch (e: Throwable) {
                TLog.error(TAG, "initQuestionnaire: $e")
            }
        }
    }

    fun onRegenerate(scene: String = SCENE_HOBBY, callback: (toast: String?) -> Unit = {}) {
        dynamicAgcContent = ""
        inGenerating = true

        var hasCalled = false
        viewModelScope.launch(Dispatchers.IO) {
            loadRecommendTag(scene = scene)
                .catch { cause ->
                    TLog.error(TAG, "catch.loadRecommendTag: $cause")

                    val toast: String? =
                        if (cause is CancellationException) {
                            cause.message
                        } else {
                            // 其他异常，如，网络连接错误、StreamResetException等
                            "生成失败，请稍后重试！"
                        }

                    if (toast?.isNotEmpty() == true) {
                        viewModelScope.launch(Dispatchers.Main) {
                            T.ss(toast)
                        }
                    }

                    if (!hasCalled) {
                        callback(toast)
                        hasCalled = true
                    }

                    inGenerating = false

                    cancel()
                }
                .collect { info ->
//                    TLog.info(TAG, "collect.loadRecommendTag: $info")

                    dynamicAgcContent =
                        (if (isQaDebugUser()) info + info else info).replace("\\n", "\n")

                    if (!hasCalled) {
                        callback(null)
                        hasCalled = true
                    }

                    inGenerating = info.isNotEmpty()
                }
        }
    }

    // https://stackoverflow.com/a/76239229/24070075
    private fun loadRecommendTag(scene: String = SCENE_HOBBY) = flow {
        coroutineScope {
            val startMs = System.currentTimeMillis()

            val inputReader =
                createApi(KFoundationApi::class.java).loadRecommendTag(scene = scene).byteStream()
                    .bufferedReader()

            val endMs = System.currentTimeMillis()

            TLog.info(TAG, "loadRecommendTag.timeConsuming: ${endMs - startMs}ms")

            var event: String? = null
            var data: String? = null

            while (isActive) {
                val line: String? = inputReader.readLine()

                if (isActive) {
                    delay((0..30L).random())
                }

                TLog.info(TAG, "line.loadRecommendTag: $line")

                /*
                        event : generateCount // 生成次数
                        data : 2_3 // 已使用次数_总次数

                        event : generateContent // 生成内容
                        data : 作为一名来自 // 具体的生成内容

                        event: error // 报错
                        data: 今日已达到使用次数上限啦~ // 报错提示
                 */

                val prefixEvent = "event:"
                val prefixData = "data:"
                val eventGenerateCount = "generateCount"
                val eventGenerateContent = "generateContent"

                when {
                    line?.startsWith(prefixEvent) == true -> {
                        event = line.removePrefix(prefixEvent).trim()
                    }

                    line?.startsWith(prefixData) == true -> {
                        data = line.removePrefix(prefixData).trim()

                        try {
                            if (event == eventGenerateCount) {
                                // data:2_3
                                val stepCount = data.split("_")
                                if (stepCount.size == 2) {
                                    regenerateStep = stepCount[0].toInt()
                                    regenerateCount = stepCount[1].toInt()
                                }
                            } else if (event == "error") {
                                TLog.error(TAG, "error.loadRecommendTag: $data")
                                cancel(CancellationException(data))
                            }
                        } catch (e: Throwable) {
                            TLog.error(TAG, "generateCount.loadRecommendTag: $e")
                        }
                    }

                    line?.isEmpty() == true -> {
                        if (event == eventGenerateContent) {
                            data?.let { emit(it) }
                        }
                        data = null
                    }

                    else -> { // 异常处理
//                        data?.let { emit("") }
                        data = null
                        TLog.error(TAG, "else.loadRecommendTag: $line")

                        cancel(CancellationException())
                    }
                }
            }

            inputReader.close()
        }
    }
}