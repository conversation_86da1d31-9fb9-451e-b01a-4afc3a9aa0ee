package com.kanzhun.foundation.facade;

import androidx.lifecycle.MediatorLiveData;

import com.google.gson.JsonObject;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.liveeventbus.LiveEventBus;
import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.model.UserInfoModel;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.L;
import com.techwolf.lib.tlog.TLog;

import io.reactivex.rxjava3.core.Observable;

public class ProfileSyncHandler extends BaseHandler<UserInfoModel> {
    private MediatorLiveData<User> mUserMediatorLiveData;
    boolean canStart = true;

    public ProfileSyncHandler(MediatorLiveData<User> userMediatorLiveData) {
        mUserMediatorLiveData = userMediatorLiveData;
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                mUserMediatorLiveData.observeForever(user -> {
                    if (canStart) {
                        canStart = false;
                        startUpdate();
                    }
                });
            }
        });
    }

    @Override
    protected void syncData() {
        Observable<BaseResponse<UserInfoModel>> responseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).queryUserInfo();
        HttpExecutor.execute(responseObservable, this);
        //获取表情包
        Observable<BaseResponse<JsonObject>> emotionResponse = RetrofitManager.getInstance().createApi(FoundationApi.class).getStickerList();
        HttpExecutor.execute(emotionResponse, new BaseRequestCallback<JsonObject>() {
            @Override
            public void onSuccess(JsonObject data) {
                TLog.print("获取user/me 成功","onSuccess");
                ChatPageRouter.saveEmotionData(GsonUtils.getGson().toJson(data));
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason != null){
                    TLog.print("获取user/me 失败",reason.toString());
                }
            }

            @Override
            public void onComplete() {
                super.onComplete();
                TLog.print("获取user/me ","onComplete");
            }
        });
    }

    @Override
    public void handleInChildThread(UserInfoModel userInfoModel) {
        super.handleInChildThread(userInfoModel);
        if (userInfoModel != null) {
//            User user = mUserMediatorLiveData.getValue();
//            if (user == null) {
//                return;
//            }
//            user.setNickName(userInfoModel.nickName);
//            user.setNickNamePy(userInfoModel.nickNamePy);
//            user.setAvatar(userInfoModel.avatar);
//            user.setTinyAvatar(userInfoModel.tinyAvatar);
//            user.setLiveVideo(userInfoModel.liveVideo);
//            user.setSchool(userInfoModel.school);
//            user.setSchoolArea(userInfoModel.schoolArea);
//            user.setSchoolLevel(userInfoModel.schoolLevel);
//            user.setGender(userInfoModel.gender);
//            user.setBirthday(userInfoModel.birthday);
//            user.setPhase(userInfoModel.phase);
            //如果通过小程序h5等完善了首善 通知首善跳过
            if (AccountHelper.getInstance().isInvite() && userInfoModel.phase > Account.ACCOUNT_STATUS_REGISTER_INVITE) {
                LiveEventBus.post("other_platform_complete", true);
            }
            ServiceManager.getInstance()
                    .getDatabaseService()
                    .getUserDao()
                    .update(userInfoModel.userId, userInfoModel.nickName, userInfoModel.nickNamePy, userInfoModel.avatar, userInfoModel.tinyAvatar,
                            userInfoModel.liveVideo, userInfoModel.school, userInfoModel.schoolLevel, userInfoModel.schoolArea, userInfoModel.gender,
                            userInfoModel.birthday, userInfoModel.phase, userInfoModel.communityLocked, userInfoModel.profileLocked,
                            userInfoModel.getCode(), userInfoModel.getName(), userInfoModel.getIcon(),
                            userInfoModel.shakeLocked, userInfoModel.degree, userInfoModel.loveGoal,
                            userInfoModel.addressCode, userInfoModel.hukouCode, userInfoModel.hometownCode, userInfoModel.industryCode, userInfoModel.schoolTime);
            AccountHelper.getInstance().refreshActivateConfig(userInfoModel.phase);
            if (userInfoModel.accountType == 2) {
                ServiceManager.getInstance()
                        .getDatabaseService()
                        .getUserDao()
                        .updateChildInfo(userInfoModel.userId, userInfoModel.getChildGender(), userInfoModel.getChildAddressCode(), userInfoModel.getChildAddress(), userInfoModel.getChildBirthdayStr());
                if (userInfoModel.childInfo != null && userInfoModel.childInfo.phase >= 10) {
                    AccountHelper.getInstance().refreshParentActivateConfig(1);
                }
            }

            AccountHelper.getInstance().refreshCommunityLocked(userInfoModel.communityLocked);
            AccountHelper.getInstance().refreshProfileLocked(userInfoModel.profileLocked);
            AccountHelper.getInstance().refreshMarryIntent(userInfoModel.marryIntent);
            AccountHelper.getInstance().refreshMarryIntentCertStatus(userInfoModel.marryIntentCertStatus);

            AccountHelper.getInstance().refreshMeetPlanId(userInfoModel.meetPlanId);
            AccountHelper.getInstance().refreshMeetPlanUserStatus(userInfoModel.meetPlanUserStatus);
            AccountHelper.getInstance().refreshMeetPlanTestReportUrl(userInfoModel.meetPlanTestReportUrl);

            AccountHelper.getInstance().refreshUserInfo(userInfoModel);
            if (userInfoModel.inviteCodeType != null) {
                AccountHelper.getInstance().refreshInviteCodeType(userInfoModel.inviteCodeType);
                AccountHelper.getInstance().refreshCustomerInviteCodeH5Url(userInfoModel.customerInviteCodeH5Url);
            }
            AccountHelper.getInstance().refreshWatermark(userInfoModel.watermark);
        }
    }
}
