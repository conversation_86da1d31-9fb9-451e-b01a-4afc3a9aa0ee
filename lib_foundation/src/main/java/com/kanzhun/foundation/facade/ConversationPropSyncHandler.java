package com.kanzhun.foundation.facade;

import com.google.gson.JsonObject;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.ConversationPropResponse;
import com.kanzhun.foundation.api.model.SingleChatsBean;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.error.ErrorReason;
import com.techwolf.lib.tlog.TLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ConversationPropSyncHandler extends BaseHandler<ConversationPropResponse> {

    private ConversationProvider mConversationProvider;
    private long mSyncVersion;
    private long mSubVersion;
    private long mDefaultSyncVersion;
    private int mHasMore;

    private final int S_VOICE_STATUS = 3;
    private final int S_LOVE_STATUS = 2;
    private final int S_DATE_STATUS = 1;

    public ConversationPropSyncHandler(ConversationProvider conversationProvider) {
        mConversationProvider = conversationProvider;
        mSyncVersion = SpManager.get().user().getLong(Constants.CONVERSATION_PROP_UPDATE_TIMES, 0);
        mDefaultSyncVersion = mSyncVersion;
    }

    @Override
    protected void syncData() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("version", mSyncVersion);
        map.put("subVersion", mSubVersion);
        HttpExecutor.executeJsonGet(URLConfig.URL_SYNC_CONVERSATION, map, this);
    }

    @Override
    public ConversationPropResponse parseResponse(JsonObject result) {
        TLog.debug("ConversationPropSyncHandler","parseResponse"+result);
        try {
            ConversationPropResponse response = new ConversationPropResponse();
            JSONObject jsonObject = new JSONObject(result.toString());
            response.version = jsonObject.optLong("version");
            response.subVersion = jsonObject.optLong("subVersion");
            response.hasMore = jsonObject.optInt("hasMore");
            JSONArray chatArray = jsonObject.optJSONArray("singleChats");
            List<SingleChatsBean> chatList = new ArrayList<>();
            if (chatArray != null) {
                final int arrayLength = chatArray.length();
                for (int i = 0; i < arrayLength; i++) {
                    JSONObject itemJSON = chatArray.getJSONObject(i);
                    SingleChatsBean chatCommonBean = new SingleChatsBean();
                    chatCommonBean.visibleMessage = MessageUtils.jsonPb2Message(itemJSON.optString("visibleMessage"));
                    chatCommonBean.lastMessage = MessageUtils.jsonPb2Message(itemJSON.optString("lastMessage"));
                    chatCommonBean.chatId = itemJSON.optString("chatId");
                    chatCommonBean.relationStatus = itemJSON.optInt("relationStatus");
                    chatCommonBean.linkMsgFunc = itemJSON.optInt("linkMsgFunc");
                    chatCommonBean.dateCardStatus = itemJSON.optInt("dateCardStatus");
                    chatCommonBean.loveCardStatus = itemJSON.optInt("loveCardStatus");
                    chatCommonBean.inChat = itemJSON.optInt("inChat");
                    chatCommonBean.unreadCount = itemJSON.optInt("unreadCount");
                    chatCommonBean.readMarkMid = itemJSON.optLong("readMarkMid");
                    chatCommonBean.readSyncMid = itemJSON.optLong("readSyncMid");
                    chatCommonBean.icon = itemJSON.optString("icon");
                    chatCommonBean.iconShowType = itemJSON.optInt("iconShowType");
                    chatCommonBean.chatSystemId = itemJSON.optInt("chatSystemId");

                    /*
                        "sort":1233, // 排序值，高值在前
                        "shield":1,// 是否屏蔽 1 屏蔽 0 未屏蔽
                     */
                    chatCommonBean.sort = itemJSON.optLong("sort");
                    chatCommonBean.shield = itemJSON.optInt("shield");

                    chatList.add(chatCommonBean);
                }
                response.singleChats = chatList;
            } else {
                response.singleChats = new ArrayList<>();
            }
            return response;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return super.parseResponse(result);
    }

    @Override
    public void handleInChildThread(ConversationPropResponse conversationPropResponse) {
        super.handleInChildThread(conversationPropResponse);
        if (conversationPropResponse != null) {
            if (mDefaultSyncVersion > 0) {
                boolean change = false;
                HashMap<String, Integer> cardUnlockMap = new HashMap<>();
                for (SingleChatsBean singleChatsBean : conversationPropResponse.singleChats) {
                    String key = singleChatsBean.chatId + "_card_pop";
                    int status = SpManager.get().user().getInt(key, 0);

                    if (singleChatsBean.linkMsgFunc > 1 && status < S_VOICE_STATUS) {
                        Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(singleChatsBean.chatId, MessageConstants.MSG_SINGLE_CHAT);
                        if (conversation != null && conversation.getLinkMsgFunc() == Constants.LINK_MSG_FUNC_CLOSE) {
                            cardUnlockMap.put(singleChatsBean.chatId, S_VOICE_STATUS);
                            change = true;
                        }
                        SpManager.putUserInt(key, S_VOICE_STATUS);
                    } else if (singleChatsBean.loveCardStatus > 1 && status < S_LOVE_STATUS) {
                        Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(singleChatsBean.chatId, MessageConstants.MSG_SINGLE_CHAT);
                        if (conversation != null && conversation.getLoveCardStatus() == 1) {
                            cardUnlockMap.put(singleChatsBean.chatId, S_LOVE_STATUS);
                            change = true;
                        }
                        SpManager.putUserInt(key, S_LOVE_STATUS);
                    } else if (singleChatsBean.dateCardStatus > 1 && status < S_DATE_STATUS) {
                        Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(singleChatsBean.chatId, MessageConstants.MSG_SINGLE_CHAT);
                        if (conversation != null && conversation.getDateCardStatus() == 1) {
                            cardUnlockMap.put(singleChatsBean.chatId, S_DATE_STATUS);
                            change = true;
                        }
                        SpManager.putUserInt(key, S_DATE_STATUS);
                    }
                }
                if (change) {
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_SYNC_CARD_STATUS, cardUnlockMap);
                }
            }

            mSyncVersion = conversationPropResponse.version;
            mSubVersion = conversationPropResponse.subVersion;
            mHasMore = conversationPropResponse.hasMore;
            if (mSyncVersion > 0) {
                SpManager.get().user().edit().putLong(Constants.CONVERSATION_PROP_UPDATE_TIMES, mSyncVersion).apply();
            }
            mConversationProvider.processProps(conversationPropResponse.singleChats);
            if (mHasMore == 0) {
                mDefaultSyncVersion = mSyncVersion;
            }
        }
    }

    @Override
    public void onComplete() {
        super.onComplete();
        if (mHasMore == 1) {
            startUpdate();
        }
    }

    @Override
    public void handleErrorInChildThread(ErrorReason reason) {
        super.handleErrorInChildThread(reason);
        mHasMore = 0;
    }
}
