package com.kanzhun.foundation.db.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.message.MessageConstants;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2019/03/19.
 */
@Dao
public interface ConversationDao {

    @Query("select * from TB_CONVERSATION WHERE inChat = 1 order by sort desc, relationStatus desc, modifyTime desc, lastTime desc")
    LiveData<List<Conversation>> queryAllConversation();

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(Conversation conversation);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long[] insert(List<Conversation> conversations);

    @Query("SELECT * FROM TB_CONVERSATION WHERE chatId = :uid AND type = :type LIMIT 1")
    Conversation queryByUid(String uid, int type);

    @Query("SELECT * FROM TB_CONVERSATION WHERE chatId = :uid AND type = :type LIMIT 1")
    LiveData<Conversation> findByUid(String uid, int type);

    @Query("UPDATE TB_CONVERSATION SET inChat = 0, unreadCount = 0 WHERE chatId = :chatId AND type = :type")
    int delete(String chatId, int type);

    @Query("SELECT sum(unreadCount) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1")
    LiveData<Integer> queryAllSilenceUnReadCount();

    @Query("SELECT sum(unreadCount) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1")
    Integer getAllUnReadCount();

    @Query("SELECT lastTime FROM TB_CONVERSATION WHERE chatId = :chatId AND type = :type LIMIT 1")
    long getLastTime(String chatId, int type);

    @Query("UPDATE TB_CONVERSATION SET unreadCount = 0, lastMsgStatus = " + MessageConstants.MSG_STATE_READ + " WHERE chatId = :chatId AND type = :type")
    void resetUnread(String chatId, int type);

    @Query("UPDATE TB_CONVERSATION SET unreadCount = 0, readSyncId = :mid, lastMsgStatus = " + MessageConstants.MSG_STATE_READ + " WHERE chatId = :chatId AND type = :type")
    void resetUnread(String chatId, int type, long mid);

    @Query("UPDATE TB_CONVERSATION SET unreadCount = 0, readSyncId = :mid WHERE chatId = :chatId AND type = :type")
    void resetUnreadWithoutStatus(String chatId, int type, long mid);

    @Query("UPDATE TB_CONVERSATION SET lastMsgStatus = " + MessageConstants.MSG_STATE_FAILURE + ", lastSeq = (SELECT max(a.seq) from TB_MESSAGE a where a.mid < " + MessageConstants.MSG_MID_STARTING_VALUE + " AND a.chatId = TB_CONVERSATION.chatId AND a.type = TB_CONVERSATION.type)  WHERE lastMsgStatus = " + MessageConstants.MSG_STATE_SENDING + "")
    void updateFailure();

    @Query("SELECT sum(unreadCount) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1 AND chatId != :chatId")
    LiveData<Integer> queryAllUnReadCountWithoutSelf(String chatId);

    @Query("SELECT sum(unreadCount) FROM TB_CONVERSATION WHERE chatId = :chatId AND type = :type")
    int queryChatUnReadCount(String chatId, int type);

    @Query("SELECT * FROM TB_CONVERSATION WHERE chatId = :chatId AND type = :type")
    Conversation getConversation(String chatId, int type);

    @Query("SELECT CASE " +
           "WHEN (SELECT SUM(unreadCount) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1 AND shield = 0) > 0 " +
           "THEN (SELECT SUM(unreadCount) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1 AND shield = 0) " +
           "WHEN (SELECT SUM(unreadCount) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1 AND shield = 1) > 0 " +
           "THEN -1 " +
           "WHEN (SELECT SUM(showFilmRedPoint) FROM TB_CONVERSATION WHERE chatId > 0 AND inChat = 1) > 0 " +
           "THEN -1 " +
           "ELSE 0 " +
           "END")
    LiveData<Integer> queryUnReadCount();

    @Query("select count(*) from TB_CONVERSATION")
    LiveData<Integer> queryAllConversationCount();

    @Query("UPDATE TB_CONVERSATION SET lastMsgStatus = :status WHERE chatId = :chatId AND type = :type")
    void updateReadStatus(String chatId, int type, int status);

    @Query("UPDATE TB_CONVERSATION SET content = :content WHERE chatId = :chatId AND type = :type")
    void updateConversationContent(String chatId, int type, String content);

    @Query("select * from TB_CONVERSATION WHERE inChat = 1 order by modifyTime desc, lastTime desc")
    List<Conversation> queryConversations();

    @Query("select * from TB_CONVERSATION WHERE inChat = 1 AND chatId = :chatId AND type = :type")
    LiveData<Conversation> queryTargetConversations(long chatId, int type);

    @Query("DELETE FROM TB_CONVERSATION")
    void deleteAll();

    @Query("select * from TB_CONVERSATION WHERE inChat = 1 order by modifyTime desc, lastTime desc")
    LiveData<List<Conversation>> getAllVisibleConversations();

    @Query("UPDATE TB_CONVERSATION SET draft = :draft, modifyTime = :modifyTime WHERE chatId = :chatId AND type = :type")
    int updateDraft(String chatId, int type, String draft, long modifyTime);

    @Query("UPDATE TB_CONVERSATION SET minSeq = :seq WHERE chatId = :chatId AND type = :type")
    void updateConversationStartSeq(String chatId, int type, long seq);

    @Query("UPDATE TB_CONVERSATION SET nickName = :nickName, avatar = :avatar WHERE chatId = :chatId AND type = :type")
    void updateConversationNameAndAvatar(String chatId, int type, String nickName, String avatar);

    @Query("UPDATE TB_CONVERSATION SET nickName = :nickName, avatar = :avatar, moodIcon = :moodIcon, moodTitle = :moodTitle WHERE chatId = :chatId AND type = :type")
    void updateConversationNameAndAvatarAndMood(String chatId, int type, String nickName, String avatar, String moodIcon, String moodTitle);

    @Query("UPDATE TB_CONVERSATION SET nickName = :nickName, avatar = :avatar, tags = :tags WHERE chatId = :chatId AND type = :type")
    void updateConversationNameAndAvatarAndTags(String chatId, int type, String nickName, String avatar, List<String> tags);


    @Query("UPDATE TB_CONVERSATION SET nickName = :nickName, avatar = :avatar, tags = :tags, moodIcon = :moodIcon, moodTitle = :moodTitle WHERE chatId = :chatId AND type = :type")
    void updateConversationNameAndAvatarAndTagsAndMood(String chatId, int type, String nickName, String avatar, List<String> tags, String moodIcon, String moodTitle);

    @Query("UPDATE TB_CONVERSATION SET unreadCount = :unreadCount, lastSeq = :lastSeq, lastTime = :lastTime, modifyTime = :modifyTime, msgId = :msgId, isSend = :isSend, sender = :sender, msgType = :messageType, badged = :badged, withDraw = :withDraw, lastMsgStatus = :lastMsgStatus, content = :content WHERE chatId = :chatId AND type = :type")
    void updateConversationContentWithMessage(String chatId, int type, int unreadCount, long lastSeq, long lastTime, long modifyTime, long msgId, boolean isSend, String sender, int messageType, boolean badged, int withDraw, int lastMsgStatus, String content);

    @Query("UPDATE TB_CONVERSATION SET lastSeq = :lastSeq, lastTime = :lastTime, modifyTime = :modifyTime, msgId = :msgId, isSend = :isSend, sender = :sender, msgType = :messageType, badged = :badged, withDraw = :withDraw, lastMsgStatus = :lastMsgStatus, content = :content WHERE chatId = :chatId AND type = :type")
    void updateConversationContentWithMessageWithoutUnreadCount(String chatId, int type, long lastSeq, long lastTime, long modifyTime, long msgId, boolean isSend, String sender, int messageType, boolean badged, int withDraw, int lastMsgStatus, String content);

    @Query("UPDATE TB_CONVERSATION SET relationStatus = :status, dateCardStatus = :dateCardStatus, loveCardStatus = :loveCardStatus, linkMsgFunc = :linkMsgFunc, inChat = :inChat WHERE chatId = :chatId AND type = :type")
    void updateConversationPropSync(String chatId, int type, int status, int dateCardStatus, int loveCardStatus, int linkMsgFunc, int inChat);

    @Query("UPDATE TB_CONVERSATION SET relationStatus = :status, dateCardStatus = :dateCardStatus, loveCardStatus = :loveCardStatus, linkMsgFunc = :linkMsgFunc, inChat = :inChat, sort = :sort, shield = :shield WHERE chatId = :chatId AND type = :type")
    void updateConversationPropSyncWithSortAndShield(String chatId, int type, int status, int dateCardStatus, int loveCardStatus, int linkMsgFunc, int inChat, long sort, int shield);

    @Query("select * from TB_CONVERSATION WHERE inChat = 1 AND nickName isnull")
    List<Conversation> queryNoNameConversations();

    @Query("select * from TB_CONVERSATION WHERE inChat = 1 AND unreadCount > 0 order by relationStatus desc, modifyTime desc, lastTime desc")
    LiveData<List<Conversation>> queryUnreadConversation();

    @Query("UPDATE TB_CONVERSATION SET momentId = :momentId WHERE chatId = :chatId AND type = :type")
    void updateLatestMomentId(String chatId, int type, String momentId);
}
