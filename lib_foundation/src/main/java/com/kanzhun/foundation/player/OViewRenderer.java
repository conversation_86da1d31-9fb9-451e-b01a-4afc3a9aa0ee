package com.kanzhun.foundation.player;

import android.content.Context;
import android.graphics.Outline;
import android.graphics.Rect;
import android.os.Build;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewOutlineProvider;

import com.kanzhun.utils.L;

import net.lucode.hackware.magicindicator.buildins.UIUtil;

import org.alita.core.AlitaMediaCore;
import org.alita.webrtc.SurfaceViewRenderer;
import org.alita.webrtc.VideoRenderer;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/1
 */
public class OViewRenderer extends SurfaceViewRenderer {
    private VideoRenderer videoRender;
    private boolean initCore = false;

    public OViewRenderer(Context context) {
        this(context,null);
    }

    public OViewRenderer(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init(){
        setFrameScaleType(SurfaceViewRenderer.RENDER_MODE_FULL_FILL_SCREEN);
    }

    public void initCore() {
        if (!initCore) {
            initCore = true;
            try {
                init(AlitaMediaCore.getInstance().getEglInstance().getEglBaseContext(), null);
            }catch (Exception e){

            }
        }
    }

    /**
     * 设置视频圆角
     */
    public void setVideoRadius(final int dp) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            setOutlineProvider(new ViewOutlineProvider() {
                final int radius = UIUtil.dip2px(getContext(), dp);
                final Rect selfRect = new Rect();

                @Override
                public void getOutline(View view, Outline outline) {
                    selfRect.set(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
                    outline.setRoundRect(selfRect, radius);
                }
            });
            setClipToOutline(true);
        }
    }

    public VideoRenderer getVideoRender() {
        initCore();
        if (videoRender == null) {
            videoRender = new VideoRenderer(this);
        }
        return videoRender;
    }

    @Override
    public void release() {
        if (videoRender!=null) {
            videoRender.dispose();
            videoRender = null;
        }
        initCore = false;
        super.release();
    }
}
