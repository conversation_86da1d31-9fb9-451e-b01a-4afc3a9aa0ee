package com.kanzhun.foundation.views

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.kanzhun.common.base.AllBaseActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.relationStatusStr
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.Constants.RELATION_FRIEND
import com.kanzhun.foundation.Constants.RELATION_LIKE
import com.kanzhun.foundation.Constants.RELATION_NOT_LIKE
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.databinding.CommonSendLikeButtonLayoutBinding
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.matching.UserGuideBlockModel
import com.kanzhun.foundation.model.message.MessageConstants
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.SendLikeHandler
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.utils.point.toSendLikeButtonSource
import com.kanzhun.foundation.utils.point.toSendLikeButtonSource2
import com.kanzhun.foundation.views.SendLikeButton.SendButtonStatus
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.utils.T
import com.petterp.floatingx.imp.FxAppLifecycleProvider
import com.techwolf.lib.tlog.TLog

class SendLikeButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    val mBinding =
        CommonSendLikeButtonLayoutBinding.inflate(LayoutInflater.from(context), this, true)

    private var userId: String? = null
    private var userName: String? = null
    private var avatar: String? = null
    private var securityId: String? = null
    private var sendLikedToFinish: Boolean = false
    private var relationStatus: Int = 0//关系状态
    private var useAi: Boolean = false

    private var mButtonStatus: SendButtonStatus = SendButtonStatus.NORMAL

    var isBlockByParent: Boolean = false

    var blockCallback: () -> Unit = {}

    private val mViewModel: SendLikeViewModel by lazy {
        ViewModelProvider(context as AllBaseActivity)[SendLikeViewModel::class.java]
    }

    var pageSource: PageSource = PageSource.NONE

    init {
        if (!isInEditMode) {
            gone()
            mBinding.lavSendLike.clickWithTrigger {

                TLog.print("zl_log", "SendLikeButton: 点击=%s", "lavSendLike");


                if (isBlockByParent) {
                    blockCallback()
                    reportClickLike(userId, pageSource, "", relationStatus)
                    return@clickWithTrigger
                }
                if (mButtonStatus == SendButtonStatus.LIKE) {
                    T.ss("已经发送过喜欢啦～")
                    reportClickLike(userId, pageSource, "", relationStatus)
                    return@clickWithTrigger
                }
                mViewModel.checkCanSendLikeForGuide().observe(context as AllBaseActivity) {
                    if (it?.scene == 1) {
                        MatchingPageRouter.showSendLikeExaminingBlockDialog(
                            context,
                            it.blockInfo.also { bean ->
                                bean?.localLikeUserName = userName
                            },
                            pageSource
                        )
                        reportClickLike(userId, pageSource, "新手引导拦截", relationStatus)
                    } else {
                        if (it?.isUserGuideBlock() == true) { //需要阻断，弹出阻断弹框
                            MePageRouter.showSendLikeBlockDialog(
                                context,
                                it.blockInfo.also { bean ->
                                    bean?.localLikeUserName = userName
                                },
                                pageSource,userId
                            )
                            reportClickLike(userId, pageSource, "", relationStatus)
                        } else {
                            innerClickAction(it)
                            reportClickLike(userId, pageSource, "", relationStatus)
                        }

                    }

                }

            }
        }


    }

    /**
     * 内部实际点击行为
     */
    private fun innerClickAction(model: UserGuideBlockModel?) {
        if (!userId.isNullOrBlank()) {
            if (userId == AccountHelper.getInstance().userId) {
                T.ss("不能向自己发送喜欢哦")
                return
            }
            when (mButtonStatus) {
                SendButtonStatus.LIKE -> {
                    T.ss("已经发送过喜欢啦～")
                }

                SendButtonStatus.NORMAL -> {
                    MatchingPageRouter.jumpSendLikeActivity(
                        context,
                        userId,
                        userName,
                        avatar,
                        relationStatus,
                        pageSource,
                        model?.chatWordList,
                        "",
                        securityId,
                        sendLikedToFinish,
                        useAi
                    )
                }

                SendButtonStatus.FRIEND -> {
                    ChatPageRouter.jumpToSingleChatActivity(context, userId,pageSource)
                }
                else -> {

                }
            }
        }
    }

    fun setLike(
        userId: String?, nickName: String?,
        avatar: String?, relationShowStatus: Int,
        showAnimation: Boolean = false, pageSource: PageSource,securityId:String? = null,
        useAi: Boolean = false
    ) {
        this.userId = userId
        this.userName = nickName
        this.avatar = avatar
        this.pageSource = pageSource
        this.securityId = securityId
        this.useAi = useAi
        canShowButton(userId) {
            when (relationShowStatus) {
                Constants.RELATION_NOT_LIKE -> {
                    mButtonStatus = SendButtonStatus.NORMAL
                    showNormalStatus()
                }

                Constants.RELATION_LIKE -> {
                    mButtonStatus = SendButtonStatus.LIKE
                    showLikeStatus(showAnimation)
                }

                Constants.RELATION_FRIEND -> {
                    mButtonStatus = SendButtonStatus.FRIEND
                    showFriendStatus(showAnimation)
                }

                else -> {
                    gone()
                }
            }
        }

    }



    private fun canShowButton(userId: String?, callback: () -> Unit) {
        if (!userId.isNullOrBlank() && userId != AccountHelper.getInstance().userId) {
            visible()
            callback.invoke()
        } else {
            gone()
        }
    }

    private fun showLikeStatus(showAnimation: Boolean = false) {
        visible()
        mBinding.run {
            lavSendLike.setAnimation(anim)
            lavSendLike.imageAssetsFolder = animDir
            if (showAnimation) {
                lavSendLike.progress = 0F
                lavSendLike.playAnimation()
            } else {
                lavSendLike.progress = 1F
            }
        }
    }
    
    var anim = "send_like/like.json"
    var animDir = "send_like/images"

    var animChat = "send_like/chat.json"
    var animChatDir = "send_like/images"

    private fun showNormalStatus() {
        visible()
        mBinding.run {
            lavSendLike.setAnimation(animChat)
            lavSendLike.imageAssetsFolder = animChatDir
            lavSendLike.progress = 0F
        }
    }

    private fun showFriendStatus(showAnimation: Boolean = false) {
        visible()
        mBinding.run {
            lavSendLike.setAnimation(animChat)
            lavSendLike.imageAssetsFolder = animChatDir
            if (showAnimation) {
                lavSendLike.progress = 0F
                lavSendLike.playAnimation()
            } else {
                lavSendLike.progress = 1F
            }
        }
    }

    /**
     * 点击喜欢按钮（触发留言弹窗，不一定发送出去）
     */
    fun reportClickLike(userId: String?, source: PageSource, msg: String?, friend: Int) {
        reportPoint("like-button-click") {
            this.actionp2 = if (TextUtils.equals(
                    "个人信息页",
                    source.toSendLikeButtonSource2()
                )
            ) source.toSendLikeButtonSource() else ""
            this.peer_id = userId
            this.source = source.toSendLikeButtonSource2()
            this.msg = msg
            this.friend = friend.relationStatusStr()
        }

    }


    enum class SendButtonStatus {
        LIKE, NORMAL, FRIEND,GONE
    }

    /**
     * @param userId
     * @param canSendLike  是否可以发送喜欢
     * @param relationStatus  11 你喜欢ta,12 ta喜欢你,20 普通好友
     * @param showAnimation 是否播放动画
     */
    fun setLikeByStatus(
        userId: String?, nickName: String?, avatar: String?,
        canSendLike: Int, relationStatus: Int,
        showAnimation: Boolean = false,securityId:String? = null,sendLikedToFinish:Boolean = false,
        useAi: Boolean = false
    ) {
        this.userId = userId
        this.userName = nickName
        this.avatar = avatar
        this.relationStatus = relationStatus
        this.securityId = securityId
        this.sendLikedToFinish = sendLikedToFinish
        this.useAi = useAi
        canShowButton(userId) {
            if (canSendLike == 1) {
                mButtonStatus = SendButtonStatus.NORMAL
                showNormalStatus()
            } else {
                when (relationStatus) {
                    11 -> { //已发送
                        visible()
                        mButtonStatus = SendButtonStatus.LIKE
                        showLikeStatus(showAnimation)
                    }

                    20, 30, 40 -> { //好友
                        visible()
                        mButtonStatus = SendButtonStatus.FRIEND
                        showFriendStatus(showAnimation)
                    }

                    else -> { //隐藏
                        gone()
                    }
                }
            }
        }

    }

}

fun getSendButtonStatusByRelationShowStatus(relationShowStatus: Int):SendButtonStatus{
    return when(relationShowStatus){
        RELATION_NOT_LIKE -> SendButtonStatus.NORMAL
        RELATION_LIKE -> SendButtonStatus.LIKE
        RELATION_FRIEND -> SendButtonStatus.FRIEND
        else -> SendButtonStatus.GONE
    }
}


fun getSendButtonStatus(canSendLike: Int, relationStatus: Int):SendButtonStatus{
    if (canSendLike == 1) {
        return SendButtonStatus.NORMAL
    } else {
        when (relationStatus) {
            11 -> { //已发送
                return SendButtonStatus.LIKE
            }

            20, 30, 40 -> { //好友
                return SendButtonStatus.FRIEND
            }

            else -> { //隐藏
                return SendButtonStatus.GONE
            }
        }
    }
}

fun likeLick(relationShowStatus: Int,uid:String,userName: String,securityId: String,avatar: String,relationStatus: Int,userAi: Boolean = false){
    if (FxAppLifecycleProvider.getTopActivity() == null)
        return

    val context = FxAppLifecycleProvider.getTopActivity()!!

    if (getSendButtonStatusByRelationShowStatus(relationShowStatus) == SendButtonStatus.LIKE) {
        T.ss("已经发送过喜欢啦~")
        return
    }
    if(getSendButtonStatusByRelationShowStatus(relationShowStatus) != SendButtonStatus.NORMAL && !AccountHelper.getInstance().isFormalUser){
        gotoSingleChatActivity(context,uid)
        return
    }
    SendLikeHandler().sendLike(context,PageSource.NONE,userName,uid = uid){ it ->
        MatchingPageRouter.jumpSendLikeActivity(
            context,uid, userName,avatar,relationStatus,PageSource.NONE,it.chatWordList,"",securityId,false,userAi)
    }
}

fun gotoSingleChatActivity(context: Context, uid: String?) {
    ExecutorFactory.execLocalTask {
        val conversation =
            ServiceManager.getInstance().conversationService.getConversation(
                uid,
                MessageConstants.MSG_SINGLE_CHAT
            )
        if (conversation != null) {
            ExecutorFactory.execMainTaskDelay({
                ChatPageRouter.jumpToSingleChatActivityAndScroll(
                    context,
                    uid,
                    conversation.systemId,
                    true
                )
            },300)
        }
    }
}

class SendLikeViewModel : BaseViewModel() {

    /**
     * 检查是否需要新手阻断
     */
    fun checkCanSendLikeForGuide(): LiveData<UserGuideBlockModel?> {
        val result: MutableLiveData<UserGuideBlockModel?> = MutableLiveData()
        val observable =
            RetrofitManager.getInstance().createApi<FoundationApi>(FoundationApi::class.java)
                .checkBeforeSendLike("")
        HttpExecutor.execute<UserGuideBlockModel>(
            observable,
            object : BaseRequestCallback<UserGuideBlockModel?>(false) {
                override fun onSuccess(data: UserGuideBlockModel?) {
                    result.value = data
                }

                override fun dealFail(reason: ErrorReason?) {

                }
            })
        return result
    }
}