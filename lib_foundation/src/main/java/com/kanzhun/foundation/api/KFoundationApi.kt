package com.kanzhun.foundation.api

import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.response.Questionnaire
import okhttp3.ResponseBody
import retrofit2.http.GET
import retrofit2.http.Query
import retrofit2.http.Streaming

const val SCENE_HOBBY: String = "0"
const val SCENE_IDEAL: String = "2"

interface KFoundationApi {

    @GET(URLConfig.URL_RECOMMEND_TAG_SSE)
    @Streaming
    suspend fun loadRecommendTag(
        @Query("scene") scene: String = SCENE_HOBBY, // 0:兴趣爱好 2:我的理想型
    ): ResponseBody

    @GET(URLConfig.URL_RECOMMEND_TAG_QUESTIONNAIRE)
    suspend fun loadRecommendTagQuestionnaire(@Query("scene") getStringScene: String?): Questionnaire
}