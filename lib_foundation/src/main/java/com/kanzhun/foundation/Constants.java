package com.kanzhun.foundation;

/**
 * <AUTHOR>
 * @date 2022/2/21.
 */
public class Constants {
    /**
     * 底部列表弹窗操作类型
     */
    public static final int CAPTURE_VIDEO = 1; //拍摄视频
    public static final int SELECT_FILE = 2; //选择文件
    public static final int CAPTURE_PHOTO = 3; //拍摄照片
    public static final int SELECT_ALBUM = 4; //相册选择
    public static final int WEB_VIEW_SHARE_COPY = 5;//WebView页面分享弹窗，复制
    public static final int WEB_VIEW_SHARE_REFRESH = 6;//WebView页面分享弹窗，刷新
    public static final int WEB_VIEW_SHARE_BROWSER_OPEN = 7;//WebView页面分享弹窗，系统浏览器查看
    public static final int VISUAL_ALL = 8;//所有人可见
    public static final int VISUAL_FRIEND = 9;//仅好友可见


    public static final int AT_ME_TYPE_ALL = 1;//@所有人
    public static final int AT_ME_TYPE_ME = 2;

    public static final char INVISIBLE_SPLIT_START = '\u200b';//不可见的起始分割符
    public static final char INVISIBLE_SPLIT_END = '\u2060';//不可见的结束分割符
    public static final String COMMON_INFO_COMMA = " , ";// 带空格间距的逗号分隔符

    public static final String INVISIBLE_SPLIT_START_STRING = INVISIBLE_SPLIT_START + "";
    public static final String INVISIBLE_SPLIT_END_STRING = INVISIBLE_SPLIT_END + "";

    public static final String CONVERSATION_SINGLE_ID = "conversation_single_id";
    public static final String CONVERSATION_PROP_UPDATE_TIMES = "conversation_update_prop_times";
    public static final String SYS_CONFIG = "sys_config";
    public static final String USER_SETTING_CONFIG = "user_setting_config";

    public static final int VIDEO_CHAT_VIDEO = 2;//2 视频
    public static final int VIDEO_CHAT_AUDIO = 1;//1 音频

    public static final String LOCAL_WITHDRAW_MSG_V2 = "local_withdraw_msg_v2";//本地撤回的消息列表

    public static final int FORWARD_ITEM_BY_ITEM = 1;//逐条转发
    public static final int FORWARD_MERGE = 2;//合并转发

    public static final String UPLOAD_FILE_TYPE_AVATAR = "avatar";//用户头像
    public static final String UPLOAD_FILE_TYPE_AUTH = "auth";//认证信息
    public static final String UPLOAD_FILE_TYPE_PROFILE = "profile";//个人页
    public static final String UPLOAD_FILE_TYPE_CHAT = "chat";//聊天

    public static final int MIN_STATURE_CM = 120;// 最小身高（cm）
    public static final int MAX_STATURE_CM = 220;// 最大身高（cm）
    public static final int MALE_DEFAULT_STATURE_CM = 175;// 男生默认身高（cm）
    public static final int FEMALE_DEFAULT_STATURE_CM = 165;// 女生默认身高（cm）

    public static final int MIN_WEIGHT_KG = 40;// 最小体重（cm）
    public static final int MAX_WEIGHT_KG = 120;// 最大体重（cm）
    public static final int MALE_DEFAULT_WEIGHT_KG = 70;// 男生默认体重kg
    public static final int FEMALE_DEFAULT_WEIGHT_KG = 50;// 女生默认体重Kg

    public static final int HAS_NO_CAR = 1;// 无车
    public static final int HAS_CAR = 2;// 有车
    public static final int HAS_NO_HOUSE = 1;// 有房
    public static final int HAS_HOUSE = 2;// 有房

    public static final int NEWCOMER_AVATAR_CODE = 10;// 形象照
    public static final int NEWCOMER_NICKNAME_CODE = 20;// 昵称
    public static final int NEWCOMER_INTRO_CODE = 30;// 签名简介
    public static final int NEWCOMER_INDUSTRY_CODE = 39;// 行业
    public static final int NEWCOMER_OCCUPATION_CODE = 40;// 职业
    public static final int NEWCOMER_STORY_CODE = 50;// 用户故事
    public static final int NEWCOMER_HOMETOWN_CODE = 80;// 家乡
    public static final int NEWCOMER_REVENUE_CODE = 90;// 收入
    public static final int NEWCOMER_HOUSE_CAR_CODE = 100;// 房车
    public static final int NEWCOMER_RESIDENCE_CODE = 110;// 户口所在地
    public static final int NEWCOMER_STATURE_CODE = 120;// 身高
    public static final int NEWCOMER_LIVING_PLACE_CODE = 130;// 生活所在地

//    public static final String HAS_SHOW_STORY_GUIDE = "has_show_story_guide";//是否显示过故事引导页面

    public static final String HAS_SHOW_PREVIEW_AB_FACE = "preview_ab_face";  //是否展示过ab面
    public static final String HAS_SHOW_MATCH_PREVIEW_AB_FACE = "match_preview_ab_face";  //匹配页是否展示过ab面
    public static final String HAS_SHOW_MATCH_USER_INFO = "match_user_info";  //是否展示过匹配用户展示页

    public static final String SP_AB_GUIDE = "sp_ab_guide";
    public static final String SP_REMEMBER_CHAT_GUIDE = "sp_remember_chat_guide";//记得和双向喜欢的人聊天的引导标识
    public static final String SP_GUESTS_MODEL = "SP_GUESTS_MODEL";//记得和双向喜欢的人聊天的引导标识

    public static final int TYPE_LIKE_USER = 0;  //0-喜欢人
    public static final int TYPE_LIKE_AVATAR = 10;  //10-形象照
    public static final int TYPE_LIKE_ABOUT_ME = 30;  //30-关于我
    public static final int TYPE_LIKE_IDEAL_PARTNER_DESC = 32;  //32-理想型
    public static final int TYPE_LIKE_STORY = 50;  //50-用户故事
    public static final int TYPE_LIKE_VOICE = 61;  //61-语音问答
    public static final int TYPE_LIKE_TEXT = 62; //62-文字问答,
    public static final int TYPE_LIKE_A_B_FACE = 70;  //70-AB面
    public static final int TYPE_LIKE_MOOD = 80;  //80-心情
    public static final int TYPE_LIKE_HOBBY = 200;  //200-兴趣爱好
    public static final int TYPE_LIKE_SINGLE_REASON = 210;  //210-单身原因
    public static final int TYPE_LIKE_ABOUT_ME_PIC = 321;  //321-关于我图片
    public static final int TYPE_LIKE_HOBBY_PIC = 322;  //322 兴趣爱好图片

    public static final int TYPE_ACTIVITY_PERSONAL_PHOTO = 506;  // 506 活动个人照片，这里类型只有1-图片

    /**
     * RxBus post tag值
     */
    public static final String POST_TAG_MATCHING_REQUIREMENT = "post_tag_matching_requirement";//匹配设置post
    public static final String POST_TAG_SHOW_REMEMBER_CHAT = "post_tag_show_remember_chat";//弹出记得聊天的提示
    public static final String POST_TAG_MATCH_PREFERENCE_REQUEST_SUCCESS = "post_tag_match_preference_request_success";//恋爱要素请求成功回调
    public static final String POST_TAG_REFRESH_F1 = "post_tag_refresh_f1";//刷新F1数据
    public static final String POST_F1_SCROLL_TO = "post_tag_scroll_to";//F1滚动到
    public static final String POST_TAG_PERSONALITY_TEST_SUCCESS = "post_tag_personality_test_success";//性格测试成功
    public static final String POST_TAG_NOTIFY_CLICKED_IN_MEETING = "post_tag_notify_clicked_in_meeting";//点击通知的时候正在语音通话中
    public static final String POST_TAG_MATCHING_SCROLL_TO_TOP = "post_tag_matching_scroll_to_top";//匹配滑动到顶部
    public static final String POST_TAG_JUMP_TO_MATCHING = "post_tag_jump_to_matching";//跳转到f2匹配页
    public static final String POST_TAG_SHOW_CHAT_HISTORY_GUIDE = "post_tag_show_chat_history_guide";//聊天历史记录添加朦层
    public static final String POST_TAG_CHAT_HISTORY_SELECTED = "post_tag_chat_history_selected";
    public static final String POST_TAG_CHAT_REQUIREMENT = "post_tag_chat_requirement";//会话列表页设置post
    public static final String POST_TAG_FIRST_TAB_CHECKED_INDEX = "post_tag_first_tab_checked_index";//f1界面切换到那一个索引界面，用于切换背景
    public static final String POST_TAG_MATCH_PREFERENCE_PAGE = "post_tag_match_preference_page";//F1界面跳转匹配设置状态
    public static final String POST_TAG_GO_MAIN_TAB = "post_tag_go_main_tab";//跳转到主界面
    public static final String POST_TAG_MATCHING_BLOCK_PROTOCOL = "post_tag_matching_block_protocol";//匹配跳转协议
    public static final String POST_TAG_MAIN_TAB_SELECTED_INDEX = "post_tag_main_tab_selected_index";//主界面被选中的tab
    public static final String POST_TAG_MATCH_TAB_SELECT_INDEX = "post_tag_match_tab_select_index";//F1匹配,推荐tab选中
    public static final String POST_WEB_AGREEMENT = "post_web_agreement";//协议弹窗页面
    public static final String POS_TAG_MATCH_RECOMMEND_PIC_INDEX = "pos_tag_match_recommend_pic_index";//F1匹配，心情共鸣，缘分碰撞索引

    /**
     * WebView传递附加参数key
     */
    public static final String PARAM_ANSWER = "param_answer";


    public static final String CONTACT_UPDATE_TIMES = "contact_update_times";
    public static final String CONVERSATION_TAB_SUMMARY = "conversation_tab_summary";

    public static final String MATCH_DATE_LOVE_INFO = "match_date_love_info";  //匹配页相识恋爱数据

    public static final String MATCHING_USER_INFO = "matching_user_info";

    //排序
    public static final int ORDER_ASC = 1;//正序排列
    public static final int ORDER_DESC = 2;//倒序排列

    public static final int MALE = 1;//性别男
    public static final int FEMALE = 2;//性别女

    public static final int LINK_OVER_CODE = 2703;
    public static final int LINK_USER_BUSY_CODE = 2702;

    public static final int LINK_MSG_FUNC_OPEN = 2;//语音通话item展示
    public static final int LINK_MSG_FUNC_CLOSE = 1;//语音通话item关闭

    public static final long S_ONE_WEEK = 7 * 24 * 60 * 60 * 1000;

    // 1 正常 2 封禁 3 待注销 4 已注销
    public static final int CONTACT_STATUS_NORMAL = 1;
    public static final int CONTACT_STATUS_BAN = 2;
    public static final int CONTACT_STATUS_TO_BE_CANCELLED = 3;
    public static final int CONTACT_STATUS_DELETED = 4;

    public static final String CHRISTMAS_DIALOG = "android-christmas";//圣诞弹窗
    public static final String CHRISTMAS_WELCOME_DIALOG = "android-welcome_christmas";//圣诞弹窗

    public static final String TO_WEB_VIEW_URL = "100";//打开链接
    public static final String TO_MATCHING_PAGE = "101";//跳转匹配页面
    public static final String TO_CHAT_PAGE = "102";//跳转聊天页面
    public static final String TO_CHAT_LIST = "103";//聊天好友列表
    public static final String WECHAT_EXCHANGE = "105";//交换微信
    public static final String TO_MY_TAB = "201";//跳转我的tab页面
    public static final String TO_PROFILE_EDIT = "202";//跳转个人页编辑态
    public static final String TO_CERT_LIST = "203";//认证列表页
    public static final String NICKNAME_EDIT = "206";//昵称编辑页
    public static final String BASE_INFO_EDIT = "207";//基本信息编辑页
    public static final String ABOUT_ME_EDIT = "208";//关于我编辑页
    public static final String INTEREST_EDIT = "210";//我的兴趣爱好编辑页
    public static final String FAMILY_EDIT = "211";//家庭介绍编辑页
    public static final String SINGLE_REASON_EDIT = "212";//单身原因编辑页
    public static final String IDEAL_PARTNER_EDIT = "213";//我的理想型编辑页
    public static final String AB_FACE_EDIT = "214";//AB 面编辑页
    public static final String MY_VOICE_EDIT = "215";//我的声音编辑页
    public static final String MY_TEXT_EDIT = "216";//我的问答编辑页
    public static final String INTEREST_TAG_EDIT = "217";//兴趣爱好标签页
    public static final String USER_TAG_EDIT = "218";//我的个性标签页
    public static final String USER_STROY_CAMERA = "219";//我的生活跳转相册
    public static final String AB_FACE_ADD_LIST = "220";//ab面新增列表选择页
    public static final String USER_REPORT = "221";//举报二级页面
    public static final String USER_SETTING_HELP = "222";//举报二级页面
    public static final String MOOD_EDIT = "223";//心情编辑页
    public static final String MOOD_DETAIL = "224";//主态心情详情页
    public static final String LIKE_LIST_ACTIVITY = "226";//个人页点赞列表
    public static final String FILM_LIST_ACTIVITY = "227";//胶片冲洗列表
    public static final String FILM_LIST_TAB_2_ACTIVITY = "228";//胶片冲洗列表
    public static final String TO_LEVEL_ONE_FINISH_229 = "229";//刷新f1权限弹窗

    public static final String TO_LIKE_ME_PAGE = "301";//跳转喜欢我的列表
    public static final String TO_SOCIAL_NOTIFY = "302";//跳转社区通知列表
    public static final String TO_TOPIC_GAME_REPLY = "303";//话题游戏回复
    public static final String TO_MOMENT_SUBMIT = "305";//发布器页面
    public static final String TO_MAIN_SEE_ME_308 = "308";//互动-看过我
    public static final String TO_FILM_PROCESS_ID_309 = "309";//胶片详情
    public static final String TO_MOMENT_DETAIL_310 = "310";//动态详情页

    public static final String TO_AVATAR_CERT_401 = "401";//头像认证页面"
    public static final String TO_EDU_CERT_402 = "402";//学历认证页面
    public static final String TO_COM_CERT_403 = "403";//工作认证页面
    public static final String TO_HOUSE_CERT_404 = "404";//房产认证页面
    public static final String TO_CAR_CERT_405 = "405";//车产认证页面
    public static final String TO_FACE_CERT_406 = "406";//人脸认证
    public static final String TO_ME_INFO_CERT_407 = "407";//个人完善信息
    public static final String TO_ME_INFO_CERT_408 = "408";//个人主页
    public static final String TO_ME_INFO_CERT_409 = "409";//MBTI测试页
    public static final String TO_ME_REVENUE_CERT_410 = "410";//收入认证
    public static final String TO_MATCHING_GUEST_ACTIVITY_411 = "411";//活动嘉宾
    public static final String TO_FILM_ACTIVITY_412 = "412";//冲洗房
    public static final String TO_MARRY_CERT_ACTIVITY_413 = "413";//婚姻认证页面
    public static final String TO_ACCOUNT_APPEAL_ACTIVITY_414 = "414";//账号申诉页面
    public static final String TO_XUE_XIN_WEBVIEW_421 = "421";//学信网在线验证
    public static final String TO_XUE_XIN_CODE_422 = "422";//学信网在线验证码
    public static final String TO_GRADUATE_CODE_423 = "423";//毕业证/学位证编号
    public static final String TO_GRADUATE_PIC_424 = "424";//毕业证/学位证照片
    public static final String TO_ABROAD_GRADUATE_425 = "425";//教留服证书编号
    public static final String TO_LIVE_ACTIVITY_452 = "452";//活动现场页
    public static final String TO_MEETING_PLAN_470 = "470";//见面计划模式跳转

    public static final String RECORD_START_TIMER_KEY = "record_start_timer_key";//用于记录APP启动时间的KEY，存于SP中
    public static final String RECORD_RECOVERY_TIMER_KEY = "record_recovery_timer_key";//用于记录APP从后台恢复时间的KEY，存于SP中

    public static final int BROWSER_INNER = 1;//内部浏览器
    public static final int BROWSER_OUTER = 2;//外部浏览器

    public static final String NOTIFY_TIPS_SHOW_TIME = "notify_tips_show_time";
    public static final String NOTIFY_TIPS_SHOW_TIME_2 = "notify_tips_show_time_2";
    public static final String NOTIFY_TIPS_SHOW_LIKE_TIME = "notify_tips_show_like_time";

    public static final String NOTIFY_TIPS_SHOW_LIKE_TIME_AI = "notify_tips_show_like_time_ai";

    public static final long S_ONE_DAY = 7 * 24 * 60 * 60 * 1000;
    public static final long ONE_DAY = 1 * 24 * 60 * 60 * 1000;

    public static final int SOCIAL_NOTIFY_REPLAY_TYPE_COMMENT = 1;//"type":1, //1 评论 2 点赞动态 3 点赞评论  4 回复评论
    public static final int SOCIAL_NOTIFY_REPLAY_TYPE_THUMBS_UP = 2;
    public static final int SOCIAL_NOTIFY_REPLAY_TYPE_COMMENT_THUMBS_UP = 3;
    public static final int SOCIAL_NOTIFY_REPLAY_TYPE_COMMENT_REPLAY = 4;

    public static final int SOCIAL_NOTIFY_FRIEND_APPLY_NOT_AGREE = 1;//待处理
    public static final int SOCIAL_NOTIFY_FRIEND_APPLY_AGREE = 2;//同意

    public static final int SOCIAL_NOTIFY_REPLAY_STATUS_NORMAL = 1;//正常状态
    public static final int SOCIAL_NOTIFY_REPLAY_STATUS_WITHDRAWN = 2;//撤回状态

    public static final int SOCIAL_VISIBLE_TYPE_ALL = 1;//全部可见
    public static final int SOCIAL_VISIBLE_TYPE_FRIEND = 2;//好友可见
    public static final int SOCIAL_VISIBLE_TYPE_SELF = 3;//自己可见

    public static final int SOCIAL_CONTENT_DELETED_ERROR_CODE = 2301;//内容已被删除
    public static final int SOCIAL_CONTENT_COMMENT_DELETED_ERROR_CODE = 2302;//一级评论被删除
    public static final int SOCIAL_CONTENT_COMMENT_REPLY_DELETED_ERROR_CODE = 2303;//二级评论被删除
    public static final int SOCIAL_JOIN_CIRCLE_REFUSE = 1702;//加入圈子失败

    public static final String CIRCLE_DRAFT_ID_PREFIX = "draft_id_circle_";//圈子草稿ID前缀

    public static final String SCHEME_PATH = "scheme_path";//外部协议
    public static final String USER_DYNAMIC_DRAFT_ID_PREFIX = "draft_id_user_dynamic_";//个人动态草稿ID前缀
    public static final String SQUARE_DRAFT_ID_PREFIX = "draft_id_square_";//广场动态草稿ID前缀
    public static final String ME_TAB_DRAFT_ID_PREFIX = "draft_id_me_tab_";//我的tab动态草稿ID前缀
    public static final String SOCIAL_MOMENT_DELETED = "social_moment_deleted";//广场动态内容已被删除
    public static final String SOCIAL_MOMENT_UPDATE = "social_moment_update";//广场动态内容更新
    public static final String SOCIAL_MOMENT_DELETED_ID = "social_moment_deleted_id";
    public static final String SOCIAL_REFRESH_CIRCLE_JOIN_STATUS = "social_refresh_circle_join_status";//圈子状态变化，更新动态详情界面圈字加入状态，圈子界面的加入状态（因为存在多个相同圈子，动态详情的界面）

    public static final int PUBLISH_TYPE_CIRCLE = 0;//圈子动态发布
    public static final int PUBLISH_TYPE_USER_DYNAMIC = 1;//个人动态发布
    public static final int PUBLISH_TYPE_SQUARE = 2;//广场动态发布
    public static final int PUBLISH_TYPE_ME_TAB = 3;

    public static final int TEXT_NUM_ADD_STRING_TYPE_APPLAUD = 0;//点赞
    public static final int TEXT_NUM_ADD_STRING_TYPE_COMMENT = 1;//评论

    public static final int HAS_MORE = 1;//加载更多
    public static final int SOCIAL_PUBLISH_SCROLL_SLOP = 50;// 发表动态页，上下滑动阈值

    public static final int REQUEST_ERROR_CODE_SYSTEM_UNDER_MAINTENANCE = 1011;//"系统维护中"


    public static final int MATCH_SEND_LIKE_TYPE = 0;//发送喜欢
    public static final int MATCH_SEND_REPLY_TYPE = 1;//发送评论

    public static final int CHECK_INVITE_ERROR_ERROR_CODE = 1108;//邀请码错误
    public static final int CHECK_INVITE_USED_ERROR_CODE = 1118;//邀请码已被使用
    public static final int CHECK_INVITE_USED_OVER_TIME = 1221;//当日5次验证机会用尽

    public static final int SYNC_PERSONAL = 1;//发布圈子动态时，是否同步个人动态，1-是
    public static final int UN_SYNC_PERSONAL = 0;//发布圈子动态时，是否同步个人动态，0-否
    public static final String LIFECYCLE_CHANGE = "lifecycle_change";

    public static final int F1_RECOMMEND_INDEX = 0;
    public static final int F1_MATCHING_INDEX = 1;

    public static final int MOOD_SET_SOURCE_EMPATHY = 1;//心情共鸣
    public static final int RELATION_NOT_LIKE = 1;//未发送喜欢
    public static final int RELATION_LIKE = 2;//已经喜欢
    public static final int RELATION_FRIEND = 3;//朋友

    public static final String MAIN_FRAGMENT_TAG = "main_fragment_tag";//匹配
    public static final String MAIN_FRAGMENT_GUEST = "main_fragment_guest";//活动嘉宾

    public static final String CUSTOM_SERVICE_KEY = "custom_service_key";//客服信息
    public static final String CUSTOM_SERVICE_KEY_PHONE = "custom_service_key_phone";//客服电话
    public static final String SP_STORY_HINT_SWITCH = "sp_story_hint_switch";//形象照引导开关
    public static final String CHILD_MODEL_DIALOG = "child_model_dialog";//青少年模式弹窗
    public static final String CUSTOM_COMPANY = "custom_company";//是否允许自定义：1是 空或其他为否


}
