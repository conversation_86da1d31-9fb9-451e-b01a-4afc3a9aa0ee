package com.kanzhun.localNotify;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.kpswitch.util.StatusBarHeightUtil;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.router.ChatPageRouterKT;
import com.petterp.floatingx.FloatingX;
import com.petterp.floatingx.assist.FxDisplayMode;
import com.petterp.floatingx.assist.FxGravity;
import com.petterp.floatingx.assist.FxScopeType;
import com.petterp.floatingx.assist.helper.FxAppHelper;
import com.petterp.floatingx.imp.FxAppLifecycleProvider;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.techwolf.lib.tlog.TLog;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;

public class MeetingPlanProtectNotificationManager implements FloatingCardTouchListener.FloatingCardStateCallback {
    private static final String TAG = "MeetingPlanProtectNotificationManager";

    // UI Constants
    private static final int COLLAPSED_WIDTH = 50;
    private static final int COLLAPSED_HEIGHT = 50;
    private static final int EXPANDED_HEIGHT = 160;
    private static final int MARGIN_DP = 16;

    // Volatile ensures the field is properly published across threads
    private static volatile MeetingPlanProtectNotificationManager sInstance;

    // Use WeakReference to avoid memory leaks
    private final WeakReference<Context> contextRef;
    private View rootView;
    private View expandedCard;
    private View collapsedCard;
    private boolean isCollapsed;
    private String recordId;

    private MeetingPlanProtectNotificationManager(Context context) {
        this.contextRef = new WeakReference<>(context.getApplicationContext());

    }

    public <T extends Activity> MeetingPlanProtectNotificationManager initData(Class<T> cls, String btnStr, int style, int count,String recordId) {
        initViews(contextRef.get(), btnStr, style, count);
        setupFloatingWindow(contextRef.get(), cls);
        this.recordId = recordId;
        return sInstance;
    }

    public static MeetingPlanProtectNotificationManager getInstance(Context context) {
        if (sInstance == null) {
            synchronized (MeetingPlanProtectNotificationManager.class) {
                if (sInstance == null) {
                    sInstance = new MeetingPlanProtectNotificationManager(context.getApplicationContext());
                }
            }
        }
        return sInstance;
    }

    public synchronized void show() {
        if (!isShowing()) {
            FloatingX.control(TAG).updateView(rootView);
            // Add initial scale and alpha
            rootView.setScaleX(0.1f);
            rootView.setScaleY(0.1f);
            rootView.setAlpha(0f);

            FloatingX.control(TAG).show();

            // Animate to final state
            rootView.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .alpha(1f)
                    .setDuration(300)
                    .start();
        }
    }

    public synchronized void stop() {
        TLog.info(TAG, "stop ---------1");
//        if (isShowing()){
        try {
            isCollapsed = false;
            FloatingX.control(TAG).cancel();
        } catch (Exception e) {
            e.printStackTrace();
        }
//            FloatingX.control(TAG).hide();
//        }
    }

    public boolean isShowing() {
        try {
            return FloatingX.control(TAG).isShow();
        } catch (Exception e) {
            return false;
        }
    }

    private void initViews(Context context, String btnStr, int style, int count) {
        rootView = LayoutInflater.from(context).inflate(R.layout.layout_floating_card, null);
        rootView.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));

        expandedCard = rootView.findViewById(R.id.expanded_card);
        collapsedCard = rootView.findViewById(R.id.collapsed_card);

        updateView(btnStr, style, count);

    }

    private <T extends Activity> void setupFloatingWindow(Context context, Class<T> cls) {
        FloatingCardTouchListener floatingCardTouchListener = new FloatingCardTouchListener(context, this);
        FxAppHelper helper = FxAppHelper.builder()
                .setLayoutView(rootView)
                .setContext(context)
                .setScopeType(FxScopeType.APP)
                .setEnableLog(true, TAG)
                .setEnableEdgeAdsorption(true)
                .setEdgeOffset(0f)
                .setOffsetXY(0,QMUIDisplayHelper.dp2px(context, 44) + StatusBarHeightUtil.getStatusBarHeight(context))
                .setEnableAllInstall(false)
                .addInstallWhiteClass(cls)
                .setGravity(FxGravity.LEFT_OR_TOP)
                .setEnableScrollOutsideScreen(false)
                .setManagerParams(new FrameLayout.LayoutParams(
                        WindowManager.LayoutParams.WRAP_CONTENT,
                        WindowManager.LayoutParams.WRAP_CONTENT))
                .setTag(TAG)
                .setEnableAnimation(true)
                .setDisplayMode(FxDisplayMode.Normal)
                .setTouchListener(floatingCardTouchListener)
                .setSaveDirectionImpl(floatingCardTouchListener)
                .build();

        FloatingX.install(helper);
    }

    @Override
    public void onStateChange() {
        if (isCollapsed) {
            expandedCard.setVisibility(View.VISIBLE);
            collapsedCard.setVisibility(View.GONE);
        } else {
            expandedCard.setVisibility(View.GONE);
            collapsedCard.setVisibility(View.VISIBLE);
            //判断是在屏幕左边还是右边 左边collapsedCard使用bg_floating_card_collapsed_left  右边使用bg_floating_card_collapsed_right
        }
        isCollapsed = !isCollapsed;
    }


    @Override
    public boolean isCollapsed() {
        return isCollapsed;
    }

    @Override
    public void updateImage(boolean isLeft) {
        if (isLeft) {
            collapsedCard.setBackgroundResource(R.drawable.bg_floating_card_collapsed_left);
        } else {
            collapsedCard.setBackgroundResource(R.drawable.bg_floating_card_collapsed_right);
        }
    }

    @Override
    public void onExpandedClick(@NotNull MotionEvent event) {
        //判断点击位置是否在idBottomLayout内
        if (idBottomLayout != null) {
            // 获取idBottomLayout在屏幕中的坐标位置
            int[] location = new int[2];
            idBottomLayout.getLocationOnScreen(location);
            
            // 计算idBottomLayout的边界
            float left = location[0];
            float top = location[1];
            float right = left + idBottomLayout.getWidth();
            float bottom = top + idBottomLayout.getHeight();
            
            // 判断点击事件的坐标是否在idBottomLayout的范围内
            if (event.getRawX() >= left && event.getRawX() <= right && 
                event.getRawY() >= top && event.getRawY() <= bottom) {
                // 点击在底部区域内，跳转到见面详情页面
                Activity topActivity = FxAppLifecycleProvider.Companion.getTopActivity();
                if (topActivity != null) {
                    ChatPageRouterKT.jumpToMeetingLocationActivity(topActivity,recordId, PageSource.NONE);
                }
            } else {
                // 点击在其他区域，跳转到安全守护页面
                Activity topActivity = FxAppLifecycleProvider.Companion.getTopActivity();
                if (topActivity != null) {
                    ChatPageRouterKT.jumpToSafetyGuardActivity(topActivity, "", PageSource.NONE);
                }
            }
        } else {
            // idBottomLayout为空时默认跳转到安全守护页面
            Activity topActivity = FxAppLifecycleProvider.Companion.getTopActivity();
            if (topActivity != null) {
                ChatPageRouterKT.jumpToSafetyGuardActivity(topActivity, "", PageSource.NONE);
            }
        }
    }

    View idBottomLayout;

    public void updateView(String btnStr, int style, int count) {
        if (contextRef.get() == null)return;
        TextView idBottom = rootView.findViewById(R.id.idBottom);
        View idBg = rootView.findViewById(R.id.idBG);

        int screenWidth = contextRef.get().getResources().getDisplayMetrics().widthPixels;

        FrameLayout.LayoutParams expandedParams = new FrameLayout.LayoutParams(
                screenWidth - QMUIDisplayHelper.dp2px(contextRef.get(), 32),
                QMUIDisplayHelper.dp2px(contextRef.get(), 160)
        );
        idBg.setLayoutParams(expandedParams);
        idBottom.setText(btnStr);
        View idIcon1 = rootView.findViewById(R.id.idIcon1);
        View idIcon2 = rootView.findViewById(R.id.idIcon2);
        TextView idIconContext = rootView.findViewById(R.id.idIconContext);
        View idBottomText = rootView.findViewById(R.id.idBottomText);
        View idLabel = rootView.findViewById(R.id.idLabel);
        ImageView idCardBg = rootView.findViewById(R.id.idCardBg);
        idBottomLayout = rootView.findViewById(R.id.idBottomLayout);
        TextView idContent = rootView.findViewById(R.id.idContent);
        ImageView idCollapsedCardIcon = rootView.findViewById(R.id.idCollapsedCardIcon);
        if (style == 1) {
            idBg.setBackgroundResource(R.drawable.bg_floating_card_big_card_bg_blue);
            idCardBg.setBackgroundResource(R.drawable.icon_floating_protect_card_bg_blue);
            idIcon1.setVisibility(View.VISIBLE);
            idIcon2.setVisibility(View.VISIBLE);
            idIconContext.setVisibility(View.VISIBLE);
            idBottomText.setVisibility(View.GONE);
            idLabel.setVisibility(View.GONE);
            idContent.setText("安全守护中");

            idIconContext.setText(count >= 2 ? "已设置两位亲友守护" : "已设置一位亲友守护");
            if (count == 1) {
                ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) idIconContext.getLayoutParams();
                lp.setMarginStart(QMUIDisplayHelper.dp2px(contextRef.get(), 40));
                idIcon2.setVisibility(View.GONE);
                idIconContext.setLayoutParams(lp);
            }else {
                ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) idIconContext.getLayoutParams();
                lp.setMarginStart(QMUIDisplayHelper.dp2px(contextRef.get(), 56));
                idIcon2.setVisibility(View.VISIBLE);
                idIconContext.setLayoutParams(lp);
            }
            idCollapsedCardIcon.setImageResource(R.drawable.icon_floating_protect_card_small_icon_blue);
        } else {
            idCollapsedCardIcon.setImageResource(R.drawable.icon_floating_protect_card_small_icon_orange);
            idCardBg.setBackgroundResource(R.drawable.icon_floating_protect_card_bg_orange);
            idContent.setText("安全守护功能");
            idBg.setBackgroundResource(R.drawable.bg_floating_card_big_card_bg);
            idBottomText.setVisibility(View.VISIBLE);
            idIcon1.setVisibility(View.GONE);
            idIcon2.setVisibility(View.GONE);
            idIconContext.setVisibility(View.GONE);
            idLabel.setVisibility(View.VISIBLE);
        }
    }
}
