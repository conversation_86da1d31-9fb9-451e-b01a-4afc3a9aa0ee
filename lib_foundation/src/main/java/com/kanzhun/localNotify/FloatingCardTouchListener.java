package com.kanzhun.localNotify;

import android.content.Context;
import android.view.MotionEvent;

import com.petterp.floatingx.listener.IFxConfigStorage;
import com.petterp.floatingx.listener.IFxTouchListener;
import com.petterp.floatingx.view.FxDefaultContainerView;
import com.petterp.floatingx.view.IFxInternalHelper;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.techwolf.lib.tlog.TLog;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class FloatingCardTouchListener implements IFxTouchListener, IFxConfigStorage {
    private static final String TAG = "FloatingCardTouchListener";
    private static final float CLICK_THRESHOLD = 10f;
    private static final float DRAG_THRESHOLD = 80f;
    private static final float EDGE_THRESHOLD = 100f;

    private final Context context;
    private final FloatingCardStateCallback stateCallback;
    private boolean isDragging = false;
    private IFxInternalHelper currentControl;
    private float dragStartRawX, dragStartRawY;
    private float initialTouchX;
    private float initialTouchY;
    private boolean neetResetLcation = false;

    @Override
    public float getX() {
        return 0;
    }

    @Override
    public float getY() {
        return 0;
    }

    @Override
    public void update(float x, float y) {
        boolean isLeft = x < QMUIDisplayHelper.getScreenWidth(currentControl.getContainerView().getContext()) / 2.f;
        stateCallback.updateImage(isLeft);
    }

    @Override
    public boolean hasConfig() {
        return false;
    }

    @Override
    public void clear() {

    }

    public interface FloatingCardStateCallback {
        void onStateChange();

        boolean isCollapsed();

        void updateImage(boolean isLeft);

        void onExpandedClick(@NotNull MotionEvent event);
    }

    public FloatingCardTouchListener(Context context, FloatingCardStateCallback callback) {
        this.context = context;
        this.stateCallback = callback;
    }

    @Override
    public void onUp() {
        isDragging = false;
        currentControl = null;
    }

    @Override
    public void onDown() {
        isDragging = false;
    }

    @Override
    public void onDragIng(@NotNull MotionEvent event, float x, float y) {
        if (currentControl == null) return;

        if (!isDragging) {
            dragStartRawX = event.getRawX();
            dragStartRawY = event.getRawY();
            isDragging = true;
        }

        float dragDistanceX = Math.abs(event.getRawX() - dragStartRawX);
        float dragDistanceY = Math.abs(event.getRawY() - dragStartRawY);
        float dragDistance = (float) Math.sqrt(dragDistanceX * dragDistanceX + dragDistanceY * dragDistanceY);

        // Only check for edge collapse if we're not collapsed and we've dragged far enough
        if (!stateCallback.isCollapsed() && dragDistance > DRAG_THRESHOLD) {
            int screenWidth = context.getResources().getDisplayMetrics().widthPixels;
            
            // If the card is in expanded state and near the edge, collapse it
            // But only do this if we're not in the middle of a horizontal drag
//            boolean isNearEdge = (x <= EDGE_THRESHOLD || x >= screenWidth - EDGE_THRESHOLD);
//            boolean isHorizontalDrag = dragDistanceX > dragDistanceY * 1.5; // Primarily horizontal movement

            // Only collapse when near edge and NOT in middle of horizontal drag
//            if (isNearEdge && !isHorizontalDrag) {
                checkEdgeAndCollapse(event, x, y);
//            }
        }
    }

    private void checkEdgeAndCollapse(MotionEvent event, float x, float y) {
        int screenWidth = context.getResources().getDisplayMetrics().widthPixels;

        if (!stateCallback.isCollapsed() && (x <= EDGE_THRESHOLD || x >= screenWidth - EDGE_THRESHOLD)) {
            int collapsedWidth = QMUIDisplayHelper.dpToPx(50) / 2;

            if (currentControl != null) {
                // 先改变状态
                stateCallback.onStateChange();
                neetResetLcation = false;
                moveTo(event, collapsedWidth);
            }
        }
    }

    private void moveTo(MotionEvent event, int collapsedWidth) {
        currentControl.getContainerView().post(new Runnable() {

            @Override
            public void run() {
                if (currentControl.getContainerView() instanceof FxDefaultContainerView) {
                    // 计算触摸点相对于手指在屏幕上的位置偏移
                    float offsetX = event.getRawX() - initialTouchX + collapsedWidth;
                    float offsetY = event.getRawY() - initialTouchY + collapsedWidth;


                    // 重置touchDown位置，传递差值
                    ((FxDefaultContainerView) currentControl.getContainerView()).resetTouchDown(offsetX, offsetY);
                }
            }
        });
    }

    @Override
    public boolean onInterceptTouchEvent(@NotNull MotionEvent event, @Nullable IFxInternalHelper control) {
        return true;
    }

    private float initX = 0.0f;
    private float initY = 0.0f;


    @Override
    public boolean onTouch(@NotNull MotionEvent event, @Nullable IFxInternalHelper control) {
        if (control != null) {
            currentControl = control;
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                initialTouchX = event.getRawX();
                initialTouchY = event.getRawY();
                if (stateCallback.isCollapsed()) {
                    neetResetLcation = true;
                    initX = currentControl.getContainerView().getX();
                    initY = currentControl.getContainerView().getY();
                }
                break;
            case MotionEvent.ACTION_UP:
                float deltaX = Math.abs(event.getRawX() - initialTouchX);
                float deltaY = Math.abs(event.getRawY() - initialTouchY);

                // 点击事件
                if (deltaX < CLICK_THRESHOLD && deltaY < CLICK_THRESHOLD) {
                    if (stateCallback.isCollapsed()) {
                        if (neetResetLcation) {
                            stateCallback.onStateChange();
                            currentControl.getContainerView().post(new Runnable() {

                                @Override
                                public void run() {
                                    if (currentControl.getContainerView() instanceof FxDefaultContainerView) {
                                        // 重置touchDown位置，传递差值
                                        ((FxDefaultContainerView) currentControl.getContainerView()).resetTouchDown(initX, initY);
                                        currentControl.moveLocation(initX, initY, false);
                                    }
                                }
                            });
                            // initX 判断是在屏幕左边还是右边
                            boolean isLeft = initX < QMUIDisplayHelper.getScreenWidth(currentControl.getContainerView().getContext()) / 2.f;
                            stateCallback.updateImage(isLeft);
                        }
                    } else {
                        stateCallback.onExpandedClick(event);
                    }
                    return true;
                }

                break;
        }

        return true;
    }
} 