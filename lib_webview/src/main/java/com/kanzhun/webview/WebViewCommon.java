package com.kanzhun.webview;

import static com.kanzhun.common.util.AppUtil.startActivity;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.CookieSyncManager;
import android.webkit.WebBackForwardList;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.ZoomButtonsController;

import androidx.annotation.NonNull;

import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.common.base.AllBaseActivity;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.kotlin.ext.StringExtKt;
import com.kanzhun.common.util.FileProviderUtils;
import com.kanzhun.common.util.LText;
import com.kanzhun.common.util.MediaFile;
import com.kanzhun.foundation.RequestCodeConstants;
import com.kanzhun.foundation.SystemConfigInstance;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.model.WebViewBean;
import com.kanzhun.foundation.utils.WebViewCallUtil;
import com.kanzhun.marry.pay.PayConstants;
import com.kanzhun.marry.wxapi.BzbReportBean;
import com.kanzhun.marry.wxapi.BzbRespResult;
import com.kanzhun.marry.wxapi.BzbSource;
import com.kanzhun.utils.L;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.URLUtils;
import com.techwolf.lib.tlog.TLog;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class WebViewCommon implements WebViewLifecycle<IWebView> {
    private static final String tag = "WebViewCommon";
    private IWebView iWebView;
    private AllBaseActivity activity;
    private String failUrl;
    private String mCurrentUrl;
    private String mName;
    private WebViewJavascriptInterface javascriptInterface = new WebViewJavascriptInterface(this);
    private HashMap<String, String> stringParams = new HashMap<>();
    private WebViewBean webViewBean;

    public WebViewBean getWebViewBean() {
        return webViewBean;
    }

    public int delayProgress = 0;

    @Override
    public boolean onCreate(AllBaseActivity activity, IWebView iWebView, Intent intent) {
        if (intent == null) {
            return false;
        }
        this.activity = activity;
        this.iWebView = iWebView;
        webViewBean = (WebViewBean) intent.getSerializableExtra(BundleConstants.BUNDLE_WEB_VIEW_BEAN);
        if (activity == null || iWebView == null || webViewBean == null || TextUtils.isEmpty(webViewBean.getPath())) {
            return false;
        }
        if (!webViewBean.getStringParams().isEmpty()) {
            stringParams.putAll(webViewBean.getStringParams());
        }
        delayProgress = intent.getIntExtra("delayProgress", 0);
        mName = webViewBean.getName();
        String title = webViewBean.getTitle();
        String pathOrUrl = webViewBean.getPath();
        if (TextUtils.isEmpty(pathOrUrl)) {
            pathOrUrl = webViewBean.getUrl();
        }
        mCurrentUrl = pathOrUrl;
        iWebView.initContentView();
        initWebView();
        initReceiver();

        File file = new File(pathOrUrl);
        if (file.exists()) {
            if (TextUtils.isEmpty(mName)) {
                mName = file.getName();
            }
            if (MediaFile.isVideoFileType(pathOrUrl)) {
                if (LText.isChinese(pathOrUrl)) {
                    Uri uri = FileProviderUtils.getUriForFile(activity, file);
                    load("" + uri);
                } else {
                    load("file://" + pathOrUrl);
                }
            }
            if (TextUtils.isEmpty(title)) {
                title = mName;
            }
            iWebView.setTitle(title);
        } else {
            load(pathOrUrl);
            if (TextUtils.isEmpty(title)) {
                title = mName;
            }
            if (!TextUtils.isEmpty(title)) {
                iWebView.setTitle(title);
            }
        }
        return true;
    }



    public String getmCurrentUrl() {
        return mCurrentUrl;
    }

    private String parseFormat(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 加载URL
     *
     * @param url
     */
    void load(String url) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        TLog.info(tag, "%s", "============webview url:" + url);
        L.i(tag, "请求：" + url);
        if (url.startsWith("weixin://wap/pay?")) {
            try {
                Intent intent = new Intent();
                intent.setAction(Intent.ACTION_VIEW);
                intent.setData(Uri.parse(url));
                startActivity(activity, intent);
            } catch (Exception e) {
                T.ss("请安装微信最新版！");
            }
            return;
        }
        if (url.startsWith("alipays://")) {
            try {
                Intent intent = new Intent();
                intent.setAction(Intent.ACTION_VIEW);
                intent.setData(Uri.parse(url));
                startActivity(activity, intent);
            } catch (Exception e) {
                T.ss("调用支付宝失败");
            }
            return;
        }

        if (url.startsWith("file:/") || url.startsWith("content:/")) {
            WebView webview = getWebView();
            if (webview != null) {
                webview.loadUrl(url);
            }
            return;
        }

//        if (LText.isWebSite1(url)) {
//            WebView webview = getWebView();
//            if (webview != null) {
//                if (!url.startsWith("http")) {
//                    url = "http://" + url;
//                }
//                webview.loadUrl(url);
//            }
//            return;
//        }

        if (url.contains("://") && !(url.contains("http://") || url.contains("https://"))) {
            // 如果Url是自定义的网址类型，如：youku://play?sharefrom=xxx&sharekey=xxx，则不做redirect处理
            return;
        }
        url = handlerWebUrlRule(url);
//        if (unknowWeb(url)) {
//            Uri uri = Uri.parse(url);
//            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
//            startActivity(activity, intent);
//            finishActivity(activity);
//            return;
//        }

        syncCookie(url);

//        syncCookie(url);
//        if (isSpecialUrl(url)) return;
//        setShareButtonVisibility(false);
//        setScanButtonVisibility(null);
        WebView webview = getWebView();
        if (webview != null) {
            markStartLoad();
            webview.loadUrl(url);
//            javascriptInterface.setInterceptJsMethodCall(URLConfig.unknowWeb(url));
            if (iWebView != null) {
                iWebView.refreshBackStatus();
            }

            setInterceptNavBackEvent(TextUtils.equals(StringExtKt.getUrlParameterValue(url, "isInterceptNavBackEvent"), "1"));
        }
    }

    private long startLoad;
    public void markStartLoad() {
        this.startLoad = System.currentTimeMillis();
    }


    private void syncCookie(String url) {
        if (activity == null) {
            return;
        }
        URL cookUrl = null;
        try {
            cookUrl = new URL(url);
        } catch (MalformedURLException e) {
            e.printStackTrace();
            cookUrl = null;
            L.i(tag, "转换URL失败", e);
        }
        if (cookUrl == null) {
            return;
        }
        if (AccountHelper.getInstance().getAccount() == null) {
            return;
        }
        if (URLConfig.unknowWeb(url)) return;
        CookieSyncManager cookieSyncManager = CookieSyncManager.createInstance(activity);
        cookieSyncManager.sync();
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.setAcceptCookie(true);
        String cookieUrl = cookUrl.getProtocol() + "://" + cookUrl.getHost() + "/";
        cookieManager.setCookie(cookieUrl, "v=" + SettingBuilder.getInstance().getVersionCode());
        cookieManager.setCookie(cookieUrl, "access_token=" + AccountHelper.getInstance().getAccessToken());
        cookieManager.setCookie(cookieUrl, "token_type=" + AccountHelper.getInstance().getTokenType());
        cookieManager.setCookie(cookieUrl, "sk=" + AccountHelper.getInstance().getAccount().getSk());
        cookieManager.setCookie(cookieUrl, "hitWebOpAbExperiment=" + SystemConfigInstance.INSTANCE.getHitWebOpAbExperiment());

        cookieManager.flush();
        String CookieStr = cookieManager.getCookie(url);
        L.e("cookie", CookieStr);
        L.e("cookie sk:", cookieManager.getCookie(cookUrl.getProtocol() + "://" + cookUrl.getHost() + cookUrl.getPath()));
    }

    public String handlerWebUrlRule(String url) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "http://" + url;
        }
        return url;
    }

    public void reload() {
        load(mCurrentUrl);
    }


    /**
     * 注册广播
     */
    private void initReceiver() {
//        // 注册为接收直聘协议中版本更新的广播
//        IntentFilter mUpdateVersionFilter = new IntentFilter();
//        mUpdateVersionFilter.addAction(Constants.RECEIVER_ZHIPIN_SECRETORY_UPDATE_VERSION_ACTION);
//        try {
//            activity.registerReceiver(mUpdateVersionReceiver, mUpdateVersionFilter);
//        } catch (Exception e) {
//            L.i(tag, MException.getErrorLog(e));
//        }


        IntentFilter mPayFilter = new IntentFilter();
        mPayFilter.addAction(PayConstants.RECEIVER_WX_PAY_RESULT_ACTION);
        activity.registerReceiver(mPayReceiver, mPayFilter);
    }

    WebView getWebView() {
        return iWebView != null ? iWebView.getWebView() : null;
    }

    private void initWebView() {
        WebView webView = getWebView();
        if (webView == null) return;
        WebSettings settings = webView.getSettings();
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setJavaScriptEnabled(true);
        settings.setDefaultTextEncodingName("UTF-8");
        settings.setBuiltInZoomControls(true);
        settings.setDomStorageEnabled(true);
        settings.setDatabaseEnabled(true);
//        settings.setAppCacheEnabled(true);
        if(SystemConfigInstance.INSTANCE.getHitWebOpAbExperiment()){
            settings.setCacheMode(android.webkit.WebSettings.LOAD_DEFAULT);
        }

        settings.setMediaPlaybackRequiresUserGesture(false);
        settings.setPluginState(WebSettings.PluginState.ON);
        settings.setAllowFileAccess(true);
        settings.setTextZoom(100);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            settings.setAllowFileAccessFromFileURLs(false);
            settings.setAllowUniversalAccessFromFileURLs(false);
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }


        String appCachePath = BaseApplication.getApplication().getCacheDir().getAbsolutePath();
//        settings.setAppCachePath(appCachePath);
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
            settings.setDatabasePath("/data/data/" + webView.getContext().getPackageName() + "/databases/");
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // 在5.0版本及以上，Android默认不允许https站点内访问一个http的资源，设置此属性可以解决
            settings.setMixedContentMode(android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        setZoomControlGone(settings, new Object[]{false});
        String userAgent = settings.getUserAgentString();
        String bossUserAgent = "orange2/" + SettingBuilder.getInstance().getVersionName();
        if (userAgent != null) {
            userAgent = userAgent + " " + bossUserAgent;
        } else {
            userAgent = bossUserAgent;
        }
        settings.setUserAgentString(userAgent);
        webView.setWebViewClient(new O2WebViewClient(this,webView));
        webView.addJavascriptInterface(javascriptInterface, "BZLBridge");
        webView.removeJavascriptInterface("searchBoxJavaBridge_");
        webView.setWebChromeClient(new HiWebChromeClient(this));
        webView.setDownloadListener(new WebViewDownloadListener(activity));
    }

    @Override
    public AllBaseActivity getActivity() {
        return activity;
    }

    @Override
    public View getContentView() {
        return getiWebView().getParentView();
    }

    @Override
    public void onDestroy() {
//        CookieSyncManager cookieSyncManager = CookieSyncManager.createInstance(activity);
//        cookieSyncManager.sync();
//        CookieManager cookieManager = CookieManager.getInstance();
//        cookieManager.removeAllCookie();
//        CookieSyncManager.getInstance().sync();

        try {
            activity.unregisterReceiver(mPayReceiver);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        WebView webView = getWebView();
        if (webView != null) {
            webView.setWebChromeClient(null);
            webView.setWebViewClient(null);
            webView.getSettings().setJavaScriptEnabled(false);
//            webView.clearCache(true);
        }

        // 解决注册接收直聘协议中的版本更新广播
        FrameLayout parent = iWebView != null ? iWebView.getParentView() : null;
//        if (getReadView() != null) {
//            getReadView().a()
//        }
        if (parent != null) {
            parent.removeAllViews();
        }
        if (webView != null) {
            webView.removeAllViews();
            webView.destroyDrawingCache();
            webView.destroy();
        }
    }

    @Override
    public void onResume() {
        if(WebViewJavascriptInterface.sendSMS){
            WebViewJavascriptInterface.sendSMS = false;
            WebViewCallUtil.Companion.callSendSMSResult(getActivity());
        }
    }


    public IWebView getiWebView() {
        return iWebView;
    }

    /**
     * 页面加载失败
     *
     * @param errorUrl
     */
    @Override
    public void onError(String errorUrl) {
        failUrl = errorUrl;
        iWebView.onWebViewLoadingError();
    }

    /**
     * 设置WebView隐藏缩放控件
     *
     * @param view
     */
    private void setZoomControlGone(View view) {
        Class classType;
        Field field;
        try {
            classType = WebView.class;
            field = classType.getDeclaredField("mZoomButtonsController");
            field.setAccessible(true);
            ZoomButtonsController mZoomButtonsController = new ZoomButtonsController(view);
            mZoomButtonsController.getZoomControls().setVisibility(View.GONE);
            try {
                field.set(view, mZoomButtonsController);
            } catch (IllegalArgumentException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setZoomControlGone(WebSettings view, Object[] args) {
        Class classType = view.getClass();
        try {
            Class[] argsClass = new Class[args.length];

            for (int i = 0, j = args.length; i < j; i++) {
                argsClass[i] = args[i].getClass();
            }
            Method[] ms = classType.getMethods();
            for (int i = 0; i < ms.length; i++) {
                if (ms[i].getName().equals("setDisplayZoomControls")) {
                    try {
                        ms[i].invoke(view, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param url
     */
    @Override
    public void onLoadStart(String url) {
        Map<String, String> params = URLUtils.parseUrlParam(url);
        if (params.containsKey("noHead")) {
            if (TextUtils.equals("1", params.get("noHead"))) {
                getiWebView().setFullScreen();
            } else {
                getiWebView().showTitleBar();
            }
        }
    }

    /**
     * 页面加载成功
     *
     * @param url
     */
    @Override
    public void onLoadComplete(final String url) {
        WebView webview = getWebView();
        if (webview == null) return;
        if (LText.equal(url, failUrl)) return;
        failUrl = null;
        if (LText.empty(url)) return;
        mCurrentUrl = url;
        if (TextUtils.isEmpty(getiWebView().getTitleName()) && !TextUtils.isEmpty(webview.getTitle())) {
            getiWebView().setTitle(webview.getTitle());
        }
        iWebView.onLoadFinish();
    }

    @Override
    public void initProgressBar(int progress) {
        if (iWebView == null || iWebView.getProgressBar() == null) return;
        ProgressBar progressBar = iWebView.getProgressBar();
        if (progress == 100) {
            delayProgress = 0;
            progressBar.setVisibility(View.GONE);
        } else {
            if (delayProgress <= 0 || System.currentTimeMillis() - startLoad > delayProgress) {
                progressBar.setVisibility(View.VISIBLE);
                progressBar.setProgress(progress);
            }
        }
    }

    public boolean goBack() {
        WebView webview = getWebView();
        if (webview != null) {
            WebBackForwardList list = webview.copyBackForwardList();
            if (list == null) {
                return false;
            }
            final int start = list.getCurrentIndex();
            int stepBack = -1;
            while (start + stepBack >= 0) {
                String hist = list.getItemAtIndex(start + stepBack).getUrl();
                if (!LText.empty(hist) && hist.endsWith("#")) {
                    hist = hist.substring(0, hist.length() - 1);
                }
                if (!LText.empty(mCurrentUrl) && mCurrentUrl.endsWith("#")) {
                    mCurrentUrl = mCurrentUrl.substring(0, mCurrentUrl.length() - 1);
                }
                mCurrentUrl = hist;
                break;
            }
            if (webview.canGoBackOrForward(stepBack)) {
                webview.goBackOrForward(stepBack);
                return true;
            }
        }
        return false;
    }

    public HashMap<String, String> getStringParams() {
        return stringParams;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == Activity.RESULT_OK) {
            if(requestCode == RequestCodeConstants.REQUEST_JUMP_TO_FACE_AUTH && data != null){
                int code = data.getIntExtra(BundleConstants.BUNDLE_FACE_CODE, -1);
                String msg = data.getStringExtra(BundleConstants.BUNDLE_FACE_MSG);
                if(WebViewJavascriptInterface.jumpToFaceAuth) {
                    WebViewJavascriptInterface.jumpToFaceAuth = false;
                    TLog.info("onActivityResult","jumpToFaceAuth:"+WebViewJavascriptInterface.jumpToFaceAuth);
                    WebViewCallUtil.Companion.callJumpToFaceAuthResult(getActivity(), code, msg);
                }
            }

        }
    }

    public boolean isInterceptNavBackEvent = false;

    public boolean isInterceptNavBackEvent() {
        return isInterceptNavBackEvent;
    }

    public void setInterceptNavBackEvent(boolean interceptNavBackEvent) {
        isInterceptNavBackEvent = interceptNavBackEvent;
    }


    // 发送支付宝支付结果
    private static final int WHAT_JS_POST_ZFB_PAY_RESULT = 31;
    // 发送微信支付结果
    private static final int WHAT_JS_POST_WX_PAY_RESULT = 28;


    @NonNull
    public Handler getHandler() {
        return handler;
    }

    private Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            switch (msg.what) {
                case WHAT_JS_POST_ZFB_PAY_RESULT:
                    int zfbResultCode = msg.arg1;
                    String zfbResultMsg = (String) msg.obj;
                    javascriptInterface.postZFBPayResult(zfbResultCode,zfbResultMsg);
                    break;
                case WHAT_JS_POST_WX_PAY_RESULT:
                    int wxResultCode = msg.arg1;
                    String wxResultMsg = (String) msg.obj;
                    javascriptInterface.postWxPayResult(wxResultCode,wxResultMsg);
                    break;
            }
            return true;
        }
    });
    /**
     * 处理支付结果的广播
     */
    private final BroadcastReceiver mPayReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (TextUtils.equals(intent.getAction(), PayConstants.RECEIVER_WX_PAY_RESULT_ACTION)) {
//                if (activity != null) {
//                    activity.dismissProgressDialog();
//                }

                /*三方支付平台返回的结果code*/
                String payResultCode = intent.getStringExtra(PayConstants.APP_PAY_CODE);
                /*三方支付平台返回的结果描述*/
                String payResultErrMsg = intent.getStringExtra(PayConstants.APP_PAY_ERR_MSG);
                /*APP内自定义支付结果*/
                int payResultState = intent.getIntExtra(PayConstants.APP_PAY_RESULT, PayConstants.APP_PAY_RESULT_USER_CANCEL);
                /*支付的平台*/
                int payPlatform = intent.getIntExtra(PayConstants.APP_PAY_PLATFORM, PayConstants.APP_PAY_PLATFORM_WX);
                /*支付的平台*/
                int platform = intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT, payPlatform);

                /*分装支付响应结果*/
                BzbRespResult bzbRespResult = BzbRespResult.obj()
                        .setErrorCode(payResultCode)
                        .setErrorMsg(payResultErrMsg)
                        .setLocalErrorCode(payResultState)
                        .setPayPlatform(platform)
                        .setBzbSource(BzbSource.SOURCE_BZB_RESP_H5);

                int what;
                if (platform == PayConstants.APP_PAY_PLATFORM_ZFB) {
                    what = WHAT_JS_POST_ZFB_PAY_RESULT;
                } else {
                    what = WHAT_JS_POST_WX_PAY_RESULT;
                }

                Message message = handler.obtainMessage(what);
                message.arg1 = payResultState;
                message.obj = payResultErrMsg;
                message.sendToTarget();

                /*上报错误信息*/
                ApmAnalyzer.create().action("action_pay_receiver")
                        .p2(BzbReportBean.obj().setBzbChannel(String.valueOf(platform)).toString())
                        .p3(bzbRespResult.toString())
                        .p3(payResultState+"")
                        .report();

            }
        }
    };


}
