package com.kanzhun.common.kotlin.ui

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import com.kanzhun.common.R
import com.kanzhun.common.databinding.CommonBlurryedLayoutBinding
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.utils.L
import com.kanzhun.common.kotlin.ui.onClick

class BlurryedLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {


    val binding: CommonBlurryedLayoutBinding
    var isBlurry = false
    var isCancel = false

    init {
        val rootView = LayoutInflater.from(context)
            .inflate(R.layout.common_blurryed_layout, this, false)
        binding = CommonBlurryedLayoutBinding.bind(rootView)
        addView(rootView)
    }


    fun getImageView():ImageView{
        return binding.idBlurryBg
    }

    fun cancelBlurry(){
        binding.idRoot.gone()
        isBlurry = false
        isCancel = true
    }

    fun toBlurry(btnTxt:String = "去添加",contentText:String,enable:Boolean = true,hitViews:Boolean = false,click:()->Unit = {}){
        if(isCancel){
            return
        }
        isBlurry = true
        binding.idRoot.visible()
        binding.idButton.text = btnTxt
        binding.idButton.enable(enable)
        if(enable){
            binding.idButton.setBackgroundColor(R.color.image_color_black.toResourceColor())
            binding.idButton.setTextColor(R.color.color_white.toResourceColor())
        }else{
            binding.idButton.setBackgroundColor(R.color.common_color_CCCCCC.toResourceColor())
            binding.idButton.setTextColor(R.color.color_white.toResourceColor())
        }
        binding.idButton.onClick {
            click()
        }
        binding.idContent.text = contentText
        if(hitViews){
            binding.idButton.gone()
            binding.idContent.gone()
            binding.idIcon.gone()
        }else{
            binding.idContent.visible()
            binding.idIcon.visible()
            if (btnTxt.isEmpty()){
                binding.idButton.gone()
            }else{
                binding.idButton.visible()
            }
        }
    }

}