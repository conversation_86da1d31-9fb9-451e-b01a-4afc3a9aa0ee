package com.kanzhun.common.kotlin.ui

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.kanzhun.common.R
import com.kanzhun.common.blurkit.blurry.Blurry
import com.kanzhun.common.databinding.CommonBlurTextViewBinding
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.invisible
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.techwolf.lib.tlog.TLog
import kotlin.math.max

class BlurTextView  @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
    var binding: CommonBlurTextViewBinding
    init {
        binding = CommonBlurTextViewBinding.inflate(LayoutInflater.from(context),this,true)
    }

    fun setMaxLine(maxLine:Int){
        binding.tvBlurText.maxLines  = maxLine
    }

    fun setText(blur:Boolean,text:String?){
        binding.tvBlurText.text = text
        binding.tvBlurText.invisible()
        binding.ivBlur.invisible()
        if(blur){
            binding.ivCover.visible()
            binding.tvBlurText.post {
                Blurry.with(context)
                    .radius(25)
                    .async()
                    .color(Color.argb(122, 255, 255, 255))
                    .capture(binding.tvBlurText)
                    .getAsync {
                        val width = binding.tvBlurText.measuredWidth
                        val height = binding.tvBlurText.measuredHeight
                        binding.ivBlur.layoutParams = LayoutParams(width, height)
                        binding.ivBlur.setImageDrawable(BitmapDrawable(resources, it))
                        binding.ivBlur.visible()
                        binding.ivBlur.post{
                            binding.ivCover.invisible()
                        }
                    }
            }

        }else{
            binding.tvBlurText.visible()
            binding.ivBlur.invisible()
            binding.ivCover.invisible()
        }
    }


}