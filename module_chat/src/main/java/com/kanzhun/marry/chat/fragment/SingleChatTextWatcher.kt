package com.kanzhun.marry.chat.fragment

import android.text.Editable
import android.text.TextWatcher
import com.kanzhun.foundation.kotlin.ktx.simplePost

class SingleChatTextWatcher(val chatId: String) : TextWatcher {
    
    private var lastRequestTime = 0L
    
    override fun beforeTextChanged(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
    }

    override fun onTextChanged(charSequence: CharSequence?, i: Int, i1: Int, i2: Int) {
        val currentTime = System.currentTimeMillis()
        // Rate limit: only send request if more than 1000ms (1 second) has passed since last request
        if (currentTime - lastRequestTime > 1000 && charSequence?.isNotEmpty() == true) {
            "orange/chat/reportSendStatus".simplePost(map = mapOf("type" to "1",  "chatId" to chatId))
            lastRequestTime = currentTime
        }
    }

    override fun afterTextChanged(editable: Editable?) {
    }
}