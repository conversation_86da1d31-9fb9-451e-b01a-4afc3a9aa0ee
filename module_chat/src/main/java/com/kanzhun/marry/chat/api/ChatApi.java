package com.kanzhun.marry.chat.api;

import android.util.ArrayMap;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.model.matching.MatchingLikeModel;
import com.kanzhun.foundation.model.message.ChatUserInfo;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.chat.api.bean.ClearMessagesBean;
import com.kanzhun.marry.chat.api.model.MomentLatestModel;
import com.kanzhun.marry.chat.api.model.TopicGameLabelModel;
import com.kanzhun.marry.chat.api.model.TopicGameQuestionDetailModel;
import com.kanzhun.marry.chat.api.model.TopicGameQuestionModel;
import com.kanzhun.marry.chat.api.response.ChatRecommendStickerResponse;
import com.kanzhun.marry.chat.api.response.MeetupChatPlanCardResponse;
import com.kanzhun.marry.chat.api.response.ProtectMeetChatCard;
import com.kanzhun.marry.chat.bindadapter.ReviewDetailData;
import com.kanzhun.marry.chat.model.CardProcessBean;
import com.kanzhun.marry.chat.model.CheckCardStatusBean;
import com.kanzhun.marry.chat.model.DateCardStatusBean;
import com.kanzhun.marry.chat.model.LoveCardStatusBean;
import com.kanzhun.marry.chat.model.MeetingConflictResponse;

import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;


/**
 * Created by Qu Zhiyong on 2022/5/27
 */
public interface ChatApi {

    @FormUrlEncoded
    @POST(URLConfig.URL_MATCH_LIKE)
    Observable<BaseResponse<MatchingLikeModel>> sendLike(@FieldMap ArrayMap<String, Object> map);

    /**
     * @param msgId 消息id
     */
    @GET(URLConfig.URL_CHAT_GET_DATE_CARD)
    Observable<BaseResponse<DateCardStatusBean>> getDateCard(@Query("msgId") String msgId);

    /**
     * @param reply 1:收下2:拒绝
     * @param msgId 消息Id
     */
    @FormUrlEncoded
    @POST(URLConfig.URL_CHAT_REPLY_DATE_CARD)
    Observable<BaseResponse> replyDateCard(@Field("reply") int reply, @Field("msgId") String msgId);

    /**
     * @param msgId 消息id
     */
    @GET(URLConfig.URL_CHAT_GET_LOVE_CARD)
    Observable<BaseResponse<LoveCardStatusBean>> getLoveCard(@Query("msgId") String msgId);

    /**
     * @param reply 1:收下2:拒绝
     * @param msgId 消息Id
     */
    @FormUrlEncoded
    @POST(URLConfig.URL_CHAT_REPLY_LOVE_CARD)
    Observable<BaseResponse> replyLoveCard(@Field("reply") int reply, @Field("msgId") String msgId);

    /**
     * @param type   1:相识卡 2:表白信
     * @param chatId 对方的用户id
     */
    @GET(URLConfig.URL_CHAT_CHECK_CARD_STATUS)
    Observable<BaseResponse<CheckCardStatusBean>> checkCardStatus(@Query("type") int type, @Query("chatId") String chatId);

    /**
     * @param chatId 用户id
     */
    @GET(URLConfig.URL_CHAT_GET_CARD_PROCESS)
    Observable<BaseResponse<CardProcessBean>> getCardProcess(@Query("chatId") String chatId);

    //region 话题游戏相关请求
    @GET(URLConfig.URL_TOPIC_GAME_LABEL_LIST)
    Observable<BaseResponse<TopicGameLabelModel>> getTopicGameLabelList();

    @GET(URLConfig.URL_TOPIC_GAME_QUESTION_LIST)
    Observable<BaseResponse<TopicGameQuestionModel>> getTopicGameQuestionList(@Query("labelId") String labelId, @Query("page") int page, @Query("pageSize") int pageSize);

    @GET(URLConfig.URL_TOPIC_GAME_QUESTION_DETAIL)
    Observable<BaseResponse<TopicGameQuestionDetailModel>> getTopicGameQuestionDetail(@Query("questionId") String questionId);

    /**
     * toId	加密后接收方的用户id
     * questionId 加密后问题id
     * optionId	加密后用户选项id
     * answer	用户的录音/答案
     * type	 1:文本答案 2:语音答案。当用户只选择了选项 没有答案时不传
     */
    @FormUrlEncoded
    @POST(URLConfig.URL_TOPIC_GAME_SEND)
    Observable<BaseResponse> topicGameSend(@FieldMap Map<String, Object> params);

    /**
     * msgId 消息id
     * optionId 加密后用户选项id
     * answer 用户的录音/答案
     * type	文本答案 2:语音答案。当用户只选择了选项 没有答案时不传
     */
    @FormUrlEncoded
    @POST(URLConfig.URL_TOPIC_GAME_REPLY)
    Observable<BaseResponse> topicGameReply(@FieldMap Map<String, Object> params);
    //endregion

    /**
     * @param userId 用户id
     */
    @GET(URLConfig.URL_MOMENT_LATEST)
    Observable<BaseResponse<MomentLatestModel>> momentLatest(@Query("userId") String userId);

    @FormUrlEncoded
    @POST(URLConfig.URL_CHAT_CLEAR_MESSAGE)
    Observable<BaseResponse<ClearMessagesBean>> clearMessages(@Field("encChatId") String chatId, @Field("maxSeq") long maxSeq);

    /**
     * @param chatId 用户id
     */
    @GET(URLConfig.URL_CHAT_STICKER_RECOMMEND)
    Observable<BaseResponse<ChatRecommendStickerResponse>> stickerRecommend(@Query("chatId") String chatId);

    /**
     * @param userId 用户id
     */
    @GET(URLConfig.URL_CHAT_GET_USER_CARD)
    Observable<BaseResponse<ChatUserInfo>> getChatUserInfo(@Query("userId") String userId);

    @GET(URLConfig.URL_MEETUP_CHAT_PLAN_CARD)
    Observable<BaseResponse<MeetupChatPlanCardResponse>> getMeetupChatPlanCard(@Query("planId") String planId, @Query("chatId") String chatId);

    @GET("orange/miniapp/protect/meet/review/detail")
    Observable<BaseResponse<ReviewDetailData>> getMeetupChatReviewDetail(@Query("recordId") String recordId);

    @GET("orange/protect/meet/chat/card")
    Observable<BaseResponse<ProtectMeetChatCard>> getProtectMeetupChatCard(@Query("chatId") String chatId);

    @GET("orange/protect/meet/setting/accept/check")
    Observable<BaseResponse<MeetingConflictResponse>> getProtectMeetupChatReviewDetail(@Query("recordId") String recordId, @Query("meetTime") String meetTime);

    /**
     * Update shield status for a chat
     * @param chatId Friend's user ID
     * @param type 1 for shield (block), 0 for unshield (unblock)
     */
    @FormUrlEncoded
    @POST("orange/chat/updateShield")
    Observable<BaseResponse> updateShield(@Field("chatId") String chatId, @Field("type") int type);

    /**
     * Update pin status for a chat
     * @param chatId Friend's user ID
     * @param type 1 for pin (top), 0 for unpin (cancel top)
     */
    @FormUrlEncoded
    @POST("orange/chat/updateTop")
    Observable<BaseResponse> updateTop(@Field("chatId") String chatId, @Field("type") int type);
}
