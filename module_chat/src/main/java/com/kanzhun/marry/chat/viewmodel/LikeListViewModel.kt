package com.kanzhun.marry.chat.viewmodel

import androidx.lifecycle.MutableLiveData
import com.hpbr.ui.recyclerview.ListData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.common.kotlin.ui.recyclerview.CommonListAdapter
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.model.ThumbBean
import com.kanzhun.foundation.api.model.ThumbListResponse
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.item.LikeListItemFactory
import com.kanzhun.utils.GsonUtils
import com.kanzhun.utils.L
import com.techwolf.lib.tlog.TLog

class LikeListViewModel : BaseViewModel() {

    var listData: MutableLiveData<ListData<BaseListItem>> = MutableLiveData()
    var isLoadingData = false
    var page = 1
    var thumbId : String? = ""

    fun isLoading() = isLoadingData

    fun loadData(isRefresh: Boolean) {
        if (isLoadingData) {
            return
        }
        if (isRefresh) {
            page = 1
            thumbId = ""
        }
        isLoadingData = true
        val observable = RetrofitManager.getInstance().createApi(FoundationApi::class.java).getThumbList(thumbId, 20)
        HttpExecutor.execute(observable, object : BaseRequestCallback<ThumbListResponse?>(false) {
            override fun onSuccess(data: ThumbListResponse?) {
                if (data == null) {
                    page = 1
                    thumbId = ""
                    listData.value = ListData(true, isSuccess = true, hasMore = false, data = mutableListOf())
                    return
                }else{
                    val list = data.rows ?: listOf()
                    if (list.isEmpty()) {
                        thumbId = ""
                        listData.value = ListData(isRefresh, isSuccess = true, hasMore = false, data = list.convertToItemList())
                        if (isRefresh) {
                            showEmpty()
                        }
                    } else {

                        val hasMore = data.hasMore ?:false
                        if(hasMore){
                            page++
                            thumbId = list[list.size-1].thumbId
                        }
                        if (isRefresh) {
                            listData.value = ListData(true, true, hasMore, list.convertToItemList())
                        } else {
                            listData.value = ListData(isRefresh, true, hasMore, list.convertToItemList())
                        }

                    }
                }
            }

            override fun dealFail(reason: ErrorReason?) {
                listData.value = ListData(isRefresh, false, hasMore = false, data = emptyList())
                if (isRefresh) {
                    showError()
                }
            }

            override fun onComplete() {
                super.onComplete()
                isLoadingData = false
            }
        })
    }

    fun List<ThumbBean>?.convertToItemList(): MutableList<BaseListItem> {
        val itemList = mutableListOf<BaseListItem>()
        this?.forEach {
            itemList.add(LikeListItemFactory.createItemBean(it))
        }
        return itemList
    }



}
