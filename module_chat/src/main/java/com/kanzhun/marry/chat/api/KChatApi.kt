package com.kanzhun.marry.chat.api

import com.kanzhun.http.response.BaseResponse
import com.kanzhun.marry.chat.api.model.AddSafetyGuardianResult
import com.kanzhun.marry.chat.api.model.AddressModel
import com.kanzhun.marry.chat.api.model.AddressPic
import com.kanzhun.marry.chat.api.model.MeetingPlanSettingDetailResponse
import com.kanzhun.marry.chat.api.model.NearbySearchResponse
import com.kanzhun.marry.chat.api.response.ChatRestrictJumpUrlResponse
import com.kanzhun.marry.chat.api.response.GetLimitedActivityQuotaResponse
import com.kanzhun.marry.chat.api.response.GuardianListResponse
import com.kanzhun.marry.chat.api.response.GuessUserResponse
import com.kanzhun.marry.chat.api.response.LimitChatResponse
import com.kanzhun.marry.chat.api.response.TopicListResponse
import com.kanzhun.marry.chat.api.response.YueYueRecMsgResponse
import com.kanzhun.marry.chat.api.response.WechatExchangeDetailResponse
import com.kanzhun.marry.chat.api.response.YueYueTopCardResponse
import com.kanzhun.marry.chat.model.CancelMeetingReasonResponse
import io.reactivex.rxjava3.core.Observable
import okhttp3.ResponseBody
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.QueryMap
import retrofit2.http.Streaming

interface KChatApi {
    @GET(ChatUrlConfig.URL_LIMIT_CHAT_TIP)
    fun getChatRestrictTip(): Observable<BaseResponse<LimitChatResponse?>>

    @GET(ChatUrlConfig.URL_LIMIT_CHAT_GET_JUMP_URL)
    fun getChatRestrictJumpUrl(): Observable<BaseResponse<ChatRestrictJumpUrlResponse?>>?

    @GET(ChatUrlConfig.URL_LIMITED_ACTIVITY_QUOTA)
    fun getLimitedActivityQuota(): Observable<BaseResponse<GetLimitedActivityQuotaResponse?>>?

    @FormUrlEncoded
    @POST(ChatUrlConfig.URL_LIMITED_ACTIVITY_TASK_FIND_CHANGE)
    fun findChange(@Field("taskId") taskId: String): Observable<BaseResponse<Any?>>?

    @GET(ChatUrlConfig.URL_PROTECT_MEET_SETTING_DETAIL)
    suspend fun getMeetingPlanSettingDetail(@Query("recordId") recordId: String): BaseResponse<MeetingPlanSettingDetailResponse>

    @POST(ChatUrlConfig.URL_PROTECT_MEET_SETTING)
    @FormUrlEncoded
    suspend fun submitMeetingPlanSetting(@FieldMap request: Map<String, String>): BaseResponse<Any>

    @FormUrlEncoded
    @POST(ChatUrlConfig.URL_LIMITED_ACTIVITY_TASK_GUESS_USER)
    fun guessUser(
        @Field("taskId") taskId: String,
        @Field("chooseUserId") chooseUserId: String
    ): Observable<BaseResponse<GuessUserResponse?>>?

    @GET(ChatUrlConfig.URL_CHAT_TOPIC_LIST)
    suspend fun getTopicList(): BaseResponse<TopicListResponse?>?

    @FormUrlEncoded
    @POST(ChatUrlConfig.URL_CHAT_TOPIC_DELETE)
    suspend fun deleteTopic(@Field("id") id: Int? = null): BaseResponse<Any?>?

    @FormUrlEncoded
    @POST(ChatUrlConfig.URL_CHAT_TOPIC_SAVE)
    suspend fun saveTopic(@Field("content") content: String? = null): BaseResponse<Any?>?

    @GET(ChatUrlConfig.URL_CHAT_AI_ASSISTANT_SSE)
    @Streaming
    suspend fun aiAssistantSse(@Query("chatId") chatId: String): ResponseBody

    @GET(ChatUrlConfig.URL_PROTECT_MEET_RECOMMEND_ADDRESS_INFO)
    suspend fun getMeetingRecommendAddressInfo(): BaseResponse<AddressModel>

    /**
     * Get YueYue top card information
     * @return YueYue top card data including recommendation quotas
     */
    @GET("orange/chat/getYueyueTopCard")
    fun getYueYueTopCard(): Observable<BaseResponse<YueYueTopCardResponse?>>?

    /**
     * Get YueYue recommendation messages
     * @return List of recommendation phrases to display to the user
     */
    @GET("orange/chat/getYueyueRecMsg")
    fun getYueYueRecMsg(): Observable<BaseResponse<YueYueRecMsgResponse?>>?

    /**
     * 附近地点搜索
     * @param request 搜索请求参数
     * @return 附近地点搜索结果
     */
    @GET(ChatUrlConfig.URL_COMMON_NEARBY_SEARCH)
    suspend fun nearbySearch(@QueryMap request: Map<String, String>): BaseResponse<NearbySearchResponse>

    // URL_COMMON_GET_ADDRESS_URL
    @GET(ChatUrlConfig.URL_COMMON_GET_ADDRESS_URL)
    fun getAddressUrl(
        @Query("longitude") longitude: String, // 范围[-180,180]，小数点后不超过6位
        @Query("latitude") latitude: String // 范围[-180,180]，小数点后不超过6位
    ): Observable<BaseResponse<AddressPic>>

    /**
     * Get list of meeting cancellation reasons
     */
    @GET("orange/protect/meet/cancelReason/list")
    fun getCancelReasons(): Observable<BaseResponse<CancelMeetingReasonResponse>>

    /**
     * Cancel a meeting with reason
     */
    @FormUrlEncoded
    @POST("orange/protect/meet/cancel")
    fun cancelMeeting(
        @Field("recordId") recordId: String, // 加密业务id
        @Field("cancelReasonCode") cancelReasonCode: Int // 见面计划取消原因code
    ): Observable<BaseResponse<Any>>

    /**
     * Add a safety guardian
     * @param name Guardian's name
     * @param phone Guardian's encrypted phone number
     * @return Response
     */
    @FormUrlEncoded
    @POST(ChatUrlConfig.URL_SECURITY_GUARDIAN_ADD)
    fun addSafetyGuardian(
        @Field("name") name: String,
        @Field("phone") phone: String
    ): Observable<BaseResponse<AddSafetyGuardianResult>>

    /**
     * Delete a safety guardian
     * @param id Guardian's encrypted ID
     * @return Response
     */
    @FormUrlEncoded
    @POST(ChatUrlConfig.URL_SECURITY_GUARDIAN_DELETE)
    fun deleteSafetyGuardian(
        @Field("id") id: String
    ): Observable<BaseResponse<Any>>

    /**
     * Get list of safety guardians
     * @return List of guardians
     */
    @GET(ChatUrlConfig.URL_SECURITY_GUARDIAN_LIST)
    fun getGuardianList(): Observable<BaseResponse<GuardianListResponse>>

    /**
     * Get WeChat exchange details
     * @param chatId Chat ID of the conversation partner
     * @return WeChat exchange status and details
     */
    @GET(ChatUrlConfig.URL_WECHAT_EXCHANGE_DETAIL)
    fun getWechatExchangeDetail(@Query("chatId") chatId: String): Observable<BaseResponse<WechatExchangeDetailResponse>>

    /**
     * Refresh recommend user message
     * @param msgId Message ID of the recommendation
     * @return Response with any data
     */
    @FormUrlEncoded
    @POST("orange/chat/refreshRecommendUserMsg")
    fun refreshRecommendUserMsg(@Field("msgId") msgId: String): Observable<BaseResponse<Any>>
}