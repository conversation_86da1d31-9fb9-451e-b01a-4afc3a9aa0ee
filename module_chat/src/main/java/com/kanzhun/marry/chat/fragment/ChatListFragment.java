package com.kanzhun.marry.chat.fragment;

import static com.kanzhun.foundation.model.message.MessageConstants.MSG_DATA_SYNC_NOTICE;
import static com.kanzhun.foundation.model.message.MessageConstants.MSG_DATA_SYNC_NOTICE_FILM;
import static com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_CHRISTMAS;
import static com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_KZ;
import static com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_KZ_ACTIVITY;
import static com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_YUEYUE;
import static com.kanzhun.marry.chat.util.ChatListItemStatusKt.bindChatListItemStatus;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.PopupWindow;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.effective.android.panel.utils.DisplayUtil;
import com.facebook.drawee.generic.RoundingParams;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.util.widget.PopupUtils;
import com.kanzhun.common.views.menu.FloatMenu;
import com.kanzhun.common.views.menu.FloatMenuCallback;
import com.kanzhun.common.views.menu.MenuItem;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.SpKeyConstants;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.facade.TempTaskType;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.permission.PermissionHelper;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.foundation.utils.AppTheme;
import com.kanzhun.foundation.utils.BadgeUtils;
import com.kanzhun.foundation.utils.RecycleviewExposureUtils;
import com.kanzhun.foundation.utils.THEME;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.foundation.views.OLoadingEmptyView;
import com.kanzhun.http.chuck.internal.support.NotificationHelper;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.activity.ChatHistoryListActivity;
import com.kanzhun.marry.chat.api.response.ChatCommonResponse;
import com.kanzhun.marry.chat.callback.ChatListCallback;
import com.kanzhun.marry.chat.databinding.ChatFragmentChatListBinding;
import com.kanzhun.marry.chat.databinding.ChatItemChatHistoryBinding;
import com.kanzhun.marry.chat.databinding.ChatItemChatListBinding;
import com.kanzhun.marry.chat.databinding.ChatPopupChatListScreenBinding;
import com.kanzhun.marry.chat.dialog.DeleteRelationDialog;
import com.kanzhun.marry.chat.dialog.DeleteRelationType;
import com.kanzhun.marry.chat.point.ChatPointReporter;
import com.kanzhun.marry.chat.util.ChatListHandler;
import com.kanzhun.marry.chat.util.ChatSwipeMenuHandler;
import com.kanzhun.marry.chat.util.ChatUtils;
import com.kanzhun.marry.chat.viewmodel.ChatLikeRelatedViewModel;
import com.kanzhun.marry.chat.viewmodel.ChatListViewModel;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.kanzhun.utils.platform.Utils;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class ChatListFragment extends FoundationVMFragment<ChatFragmentChatListBinding, ChatListViewModel>
        implements ChatListCallback, OnLoadMoreListener {

    private BaseBinderAdapter adapter;

    private RecycleviewExposureUtils mRecycleviewExposureUtils;
    private ChatSwipeMenuHandler chatSwipeMenuHandler;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_fragment_chat_list;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }


    @Override
    protected void initFragment() {
        super.initFragment();
        initView();
        initData();
        initExpo();
    }

    private void initView() {
        if (AppTheme.INSTANCE.getTheme() == THEME.CHRISTMAS) {
            getDataBinding().idBG.setBackgroundResource(R.mipmap.theme_christmas_mask);
        }
        getDataBinding().flTitle.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        getDataBinding().smartRefreshLayout.setEnableRefresh(false);
        getDataBinding().smartRefreshLayout.setEnableLoadMore(false);
        getDataBinding().smartRefreshLayout.setOnLoadMoreListener(this);

        // Initialize the ChatSwipeMenuHandler
        chatSwipeMenuHandler = new ChatSwipeMenuHandler(activity, getDataBinding().rvList, conversation -> {
            showSureDeleteConversationDialog(conversation);
            return Unit.INSTANCE;
        });

        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(Conversation.class, new BaseDataBindingItemBinder<Conversation, ChatItemChatListBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.chat_item_chat_list;
            }

            @Override
            protected void bind(BinderDataBindingHolder<ChatItemChatListBinding> holder, ChatItemChatListBinding binding, Conversation item) {
                binding.setItem(item);
                binding.setGender(getViewModel().getSelfGender());
                binding.setSelfAvatar(getViewModel().getSelfAvatar());
                binding.clNoticeAvatars.setItem(item);
                if (item.showFilmRedPoint && String.valueOf(MSG_DATA_SYNC_NOTICE_FILM).equals(item.getChatId()) && TextUtils.equals(
                        SettingBuilder.getInstance()
                                .getUserSettingValue(UserSettingConfig.PERSONALITY_NOTIFY_FILM), "0")) {
                    binding.idRedPoint.setVisibility(View.VISIBLE);
                    binding.tvMessageCount.setVisibility(View.GONE);
                } else {
                    if (item.getUnreadCount() > 0 && item.getShield() == 0) {
                        // 显示没有被屏蔽的未读数气泡
                        binding.tvMessageCount.setVisibility(View.VISIBLE);
                    } else {
                        binding.tvMessageCount.setVisibility(View.GONE);
                    }
                    binding.idRedPoint.setVisibility(View.GONE);
                }

                // Setup swipe actions using the ChatSwipeMenuHandler
                chatSwipeMenuHandler.setupSwipeActions(binding, item);

                bindChatListItemStatus(binding.statusComposeView, item);

                if (item.getUnreadCount() > 0 && item.showChristmas && ServiceManager.getInstance().getTempTask().getTempTaskRepository() != null) {
                    ServiceManager.getInstance().getTempTask().getTempTaskRepository().handlerView(binding.idLottieAnimationView,
                            TempTaskType.Chat_LIST, "", "");
                } else {
                    binding.idLottieAnimationView.setVisibility(View.GONE);
                }

                Contact contact = ServiceManager.getInstance().getContactService().getContactById(item.getChatId());
                boolean showMeetUpTag = contact != null && contact.getModeType() == 2;
                binding.tvMeetupTags.setVisibility(showMeetUpTag ? View.VISIBLE : View.GONE);

                if (showMeetUpTag) {
                    int color = Color.parseColor("#FFDEB384");
                    RoundingParams roundingParams = RoundingParams.fromCornersRadius(5f);
                    roundingParams.setBorder(color, DisplayUtil.dip2px(activity, 2f));
                    roundingParams.setRoundAsCircle(true);
                    binding.ovPhoto.getHierarchy().setRoundingParams(roundingParams);
                } else {
                    binding.ovPhoto.getHierarchy().setRoundingParams(RoundingParams.asCircle());

                    if (contact != null) {
                        // //0-不展示，1-见面邀约中; 2-见面中;3-已见面
                        if (contact.getProtectMeetStatus() == 0) {
                            binding.tvMeetPlanMeet.setVisibility(View.GONE);
                        } else if (contact.getProtectMeetStatus() == 1) {
                            binding.tvMeetPlanMeet.setText("见面邀约中");
                            binding.tvMeetPlanMeet.setVisibility(View.VISIBLE);
                        } else if (contact.getProtectMeetStatus() == 2) {
                            binding.tvMeetPlanMeet.setVisibility(View.VISIBLE);
                            binding.tvMeetPlanMeet.setText("见面中");
                        } else if (contact.getProtectMeetStatus() == 3) {
                            binding.tvMeetPlanMeet.setVisibility(View.VISIBLE);
                            binding.tvMeetPlanMeet.setText("已见面");
                        }
                    }
                }
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<ChatItemChatListBinding> holder, @NonNull View view, Conversation data, int position) {
                // 不再直接处理点击事件，而是通过SwipeLayout的点击监听器处理
                super.onClick(holder, view, data, position);
            }

            @Override
            public boolean onLongClick(@NonNull BinderDataBindingHolder<ChatItemChatListBinding> holder, @NonNull View view, Conversation data, int position) {
                // 不再直接处理长按事件，而是通过SwipeLayout的长按监听器处理
                return false;
            }
        });
        adapter.addItemBinder(ChatCommonResponse.ChatCommonBean.class, new BaseDataBindingItemBinder<ChatCommonResponse.ChatCommonBean, ChatItemChatHistoryBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.chat_item_chat_history;
            }

            @Override
            protected void bind(BinderDataBindingHolder<ChatItemChatHistoryBinding> holder, ChatItemChatHistoryBinding binding, ChatCommonResponse.ChatCommonBean item) {
                binding.setItem(item);
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<ChatItemChatHistoryBinding> holder, @NonNull View view, ChatCommonResponse.ChatCommonBean data, int position) {
                if (data.lastMessage != null) {
                    ServiceManager.getInstance().getMessageService().insertMessages(Arrays.asList(data.lastMessage));
                }
                ExecutorFactory.execLocalTask(new Runnable() {
                    @Override
                    public void run() {
                        Conversation conversation = ServiceManager.getInstance().getConversationService().getConversation(data.chatId, MessageConstants.MSG_SINGLE_CHAT);
                        if (conversation == null) {
                            conversation = new Conversation();
                            conversation.setChatId(data.chatId);
                            conversation.setNickName(data.nickName);
                            conversation.setAvatar(data.tinyAvatar);
                        }
                        conversation.setRelationStatus(MessageConstants.MATCHING_STATUS_HISTORY);
                        ServiceManager.getInstance().getConversationService().updateConversation(conversation);
                        ChatPageRouter.jumpToSingleChatActivity(activity, data.chatId, data.avatar, data.nickName, false);
                        ChatPointReporter.Companion.reportConversationClick(data, conversation);
                    }
                });

            }

            @Override
            public boolean onLongClick(@NonNull BinderDataBindingHolder<ChatItemChatHistoryBinding> holder, @NonNull View view, ChatCommonResponse.ChatCommonBean data, int position) {
                showHistoryFloatMenu(data);
                return true;
            }
        });
        getDataBinding().rvList.setAdapter(adapter);

        // Add scroll listener to close all swipe menus when scrolling
        getDataBinding().rvList.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    chatSwipeMenuHandler.closeAllSwipeLayouts();
                }
            }
        });

        OLoadingEmptyView emptyView = getEmptyView();
        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) emptyView.getLayoutParams();
        layoutParams.gravity = Gravity.CENTER_HORIZONTAL;
        layoutParams.topMargin = QMUIDisplayHelper.dpToPx(56);
        emptyView.setLayoutParams(layoutParams);
        ChatPointReporter.Companion.reportConversationExpose("默认排序");

        getDataBinding().includeNotifyOpen.idOpenBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickNotifySetting();
            }
        });

        // Close all swipe menus when touching the root view
        getDataBinding().getRoot().setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    chatSwipeMenuHandler.closeAllSwipeLayouts();
                }
                return false;
            }
        });

    }

    @Override
    public void onResume() {
        super.onResume();
        isPause = false;
        doFirstExp();

        showOpenNotifyView();
    }

    private void showOpenNotifyView() {
        if (!isPause && (!NotificationHelper.checkNotifySetting(activity) || TextUtils.equals(
                SettingBuilder.getInstance()
                        .getUserSettingValue(UserSettingConfig.PERSONALITY_PUSH_115), "0"
        ))
                && System.currentTimeMillis() - SpManager.get().user().getLong(Constants.NOTIFY_TIPS_SHOW_TIME, 0) > Constants.S_ONE_DAY
                && unReadCount > 0) {
            getDataBinding().includeNotifyOpen.llNotifyOpen.setVisibility(View.VISIBLE);
            PointHelperKt.reportPoint("notification-guide-expo", new Function1<PointBean, Unit>() {
                @Override
                public Unit invoke(PointBean pointBean) {
                    pointBean.setSource("聊天页");
                    return null;
                }
            });
        } else {
            if (getDataBinding().includeNotifyOpen.llNotifyOpen.getVisibility() != View.VISIBLE) {
                getDataBinding().includeNotifyOpen.llNotifyOpen.setVisibility(View.GONE);
            }
        }
    }

    private void initData() {
        getViewModel().getAllConversations().observe(this, new Observer<List<Conversation>>() {
            @Override
            public void onChanged(List<Conversation> conversations) {
                if (getViewModel().getCurrScreen() == ChatListViewModel.SCREEN_DEFAULT) {
                    TLog.print("zl_log", "ChatListFragment: getAllConversations: onChanged: conversations=%s", GsonUtils.toJson(conversations));
                    updateListView(ChatListHandler.Companion.getList(conversations));
                }

            }
        });

        getViewModel().getUnreadConversations().observe(this, new Observer<List<Conversation>>() {
            @Override
            public void onChanged(List<Conversation> conversations) {
                if (getViewModel().getCurrScreen() == ChatListViewModel.SCREEN_UNREAD) {
                    updateListView(ChatListHandler.Companion.getList(conversations));
                }
            }
        });


//        getViewModel().getConversationTabSummaryInfo().observe(this, new Observer<ConversationTabSummaryInfo>() {
//            @Override
//            public void onChanged(ConversationTabSummaryInfo conversationTabSummaryInfo) {
//                updateTabView(conversationTabSummaryInfo);
//            }
//        });
        getViewModel().getConnectStatus().observe(this, new Observer<Byte>() {
            @Override
            public void onChanged(@Nullable Byte value) {
                if (!AccountHelper.getInstance().isLogin()) {
                    return;
                }
                String connectStr = ChatUtils.getConnectState(value);
                getDataBinding().tvConnectStatus.setText(connectStr);
                getDataBinding().ivNotConnected.setVisibility(!TextUtils.isEmpty(connectStr) ? View.VISIBLE : View.GONE);
            }
        });

        ServiceManager.getInstance().getConversationService().getUnReadCountLiveData().observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                if (integer == null) {
                    return;
                }
                BadgeUtils.setBadge(Utils.getApp(), integer);
                showOpenNotifyView();
            }
        });
        RxBus.getInstance().subscribe(this, Constants.POST_TAG_CHAT_HISTORY_SELECTED, new RxBus.Callback<Boolean>() {
            @Override
            public void onEvent(Boolean selected) {
                if (selected != null) {
                    getViewModel().getChatHistorySelected().set(selected);
                    if (selected) {
                        SpManager.putUserBoolean(SpKeyConstants.SP_KEY_CHAT_HISTORY_GUIDE_SHOWED, true);
                        getDataBinding().rvList.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                getDataBinding().rvList.scrollToPosition(adapter.getItemCount() - 1);

                            }
                        }, 200);
                    }
                }
            }
        });


        ServiceManager.getInstance().getConversationService().getContactSyncOver().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    if (adapter != null) adapter.notifyDataSetChanged();
                }
            }
        });

        ServiceManager.getInstance().getProfileService().getUserLiveData().observe(this, new Observer<User>() {
            @Override
            public void onChanged(User user) {
                if (user != null) {
                    getViewModel().setSelfAvatar(user.getTinyAvatar());
                    getViewModel().setSelfGender(user.getGender());
                }
            }
        });


        getViewModel().getChatHistoryLiveData().observe(this, new Observer<ChatCommonResponse>() {
            @Override
            public void onChanged(ChatCommonResponse response) {
                getDataBinding().smartRefreshLayout.finishRefresh();
                getDataBinding().smartRefreshLayout.finishLoadMore();
                if (getViewModel().getCurrScreen() != ChatListViewModel.SCREEN_HISTORY) {
                    return;
                }
                if (response == null || LList.isEmpty(response.chatList)) {
                    showEmptyNoData(getString(R.string.chat_list_empty));
                } else {
                    showEmptySuccess();
                    getDataBinding().smartRefreshLayout.setEnableLoadMore(response.hasMore == 1);
                }
                adapter.setList(response != null ? response.chatList : null);
            }
        });

        getViewModel().getMoreHistoryChatListLiveData().observe(this, new Observer<ChatCommonResponse>() {
            @Override
            public void onChanged(ChatCommonResponse response) {
                getDataBinding().smartRefreshLayout.finishLoadMore();
                getDataBinding().smartRefreshLayout.setEnableLoadMore(response.hasMore == 1);
                if (getViewModel().getCurrScreen() != ChatListViewModel.SCREEN_HISTORY) {
                    return;
                }
                if (response != null && !LList.isEmpty(response.chatList)) {
                    adapter.addData(response.chatList);
                }
            }
        });

        getViewModel().getRemoveHistoryChatLiveData().observe(this, new Observer<ChatCommonResponse.ChatCommonBean>() {
            @Override
            public void onChanged(ChatCommonResponse.ChatCommonBean chatCommonBean) {
                if (getViewModel().getCurrScreen() != ChatListViewModel.SCREEN_HISTORY) {
                    return;
                }
                if (chatCommonBean != null) {
                    adapter.remove(chatCommonBean);
                }
            }
        });


    }

    private void initExpo() {
        if (mRecycleviewExposureUtils != null) return;
        mRecycleviewExposureUtils = new RecycleviewExposureUtils(getDataBinding().rvList, new RecycleviewExposureUtils.IExposure() {
            @Override
            public void onExpose(int index) {
                if (isPause) return;
                if (adapter != null) {
                    List<Object> items = adapter.getData();
                    Object o = LList.getElement(items, index);
                    if (o instanceof Conversation) {
                        Conversation item = (Conversation) o;
                        if (item.getSystemId() == SYS_ID_OFFICIAL_KZ_ACTIVITY || item.getSystemId() == SYS_ID_OFFICIAL_KZ
                                || item.getSystemId() == SYS_ID_OFFICIAL_CHRISTMAS
                                || item.getSystemId() == SYS_ID_OFFICIAL_YUEYUE
                                || (TextUtils.equals(item.getChatId(), String.valueOf(MSG_DATA_SYNC_NOTICE)
                        ) || (TextUtils.equals(item.getChatId(), String.valueOf(MSG_DATA_SYNC_NOTICE_FILM)
                        )))) {
                            PointHelperKt.reportPoint("official-msg-drawer-expo", new Function1<PointBean, Unit>() {
                                @Override
                                public Unit invoke(PointBean pointBean) {
                                    pointBean.setActionp2(item.getUnreadCount() > 99 ? "99" : String.valueOf(item.getUnreadCount()));
                                    pointBean.setActionp3(item.getMsgId() + "");
                                    pointBean.setActionp4(item.getContent());
                                    pointBean.setType(ChatPointReporter.Companion.toConversationType(item));
                                    return null;
                                }
                            });
                        }
                    }

                }
            }
        });
    }

    boolean isPause;

    @Override
    public void onPause() {
        super.onPause();
        isPause = true;
        chatSwipeMenuHandler.closeAllSwipeLayouts();
    }

    public void clearExpFlag() {
        if (mRecycleviewExposureUtils != null) {
            mRecycleviewExposureUtils.clearFlag();
        }
    }

    private void updateListView(List<Conversation> conversationList) {
        if (conversationList == null) {
            return;
        }
        if (LList.isEmpty(conversationList)) {
            showEmptyNoData(getString(R.string.chat_list_empty));
        } else {
            showEmptySuccess();
        }
        adapter.setList(conversationList);
    }

    private void doFirstExp() {
        if (mRecycleviewExposureUtils != null && adapter.getData() != null && adapter.getData().size() > 0) {
            mRecycleviewExposureUtils.doFirstTrace();
        }
    }

    private void showSureDeleteConversationDialog(Conversation conversation) {
        new DeleteRelationDialog(requireActivity(), DeleteRelationType.MATCH).show(confirm -> {
            if (confirm) {
                ChatPointReporter.Companion.unFriendConfirmClick(conversation.getChatId(), 0);
                getViewModel().deleteConversation(conversation.getChatId(),
                        conversation.getType());
            }
            return null;
        });
    }

    @Override
    public void clickMyLike() {
//        Intent intent = ChatLikeRelatedActivity.createIntent(activity, ChatLikeRelatedViewModel.LikeRelatedType.TYPE_LIKE_RELATED_I_LIKE);
//        AppUtil.startActivity(activity, intent);
    }

    @Override
    public void clickChatHistory() {
        Intent intent = new Intent(activity, ChatHistoryListActivity.class);
        AppUtil.startActivity(activity, intent);
    }

    @Override
    public void clickLikeMe() {
        if (MultiClickUtil.isMultiClick()) return;
        if (getViewModel().getConversationTabSummaryInfo().getValue() != null && getViewModel().getConversationTabSummaryInfo().getValue().likeMeInfo.hasNew == 1) {
            getViewModel().resetLikeMeHasNew(0);
        }
//        Intent intent = ChatLikeRelatedActivity.createIntent(activity,
//                ChatLikeRelatedViewModel.LikeRelatedType.TYPE_LIKE_RELATED_LIKE_ME, getViewModel().getLikeMeCount());
//        AppUtil.startActivity(activity, intent);
        ChatPageRouter.jumpToLikeRelatedActivity(activity,
                ChatLikeRelatedViewModel.LikeRelatedType.TYPE_LIKE_RELATED_LIKE_ME, getViewModel().getLikeMeCount(), 1);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mRecycleviewExposureUtils.clearRunnable();
        RxBus.getInstance().unregister(this);
    }

    public void clickNotifySetting() {
        if (!NotificationHelper.checkNotifySetting(activity)) {
            PermissionHelper.requestNotificationPermission(getActivity(), null);
        } else {
            MePageRouter.jumpToMeNotifySettingActivity(activity);
        }
        PointHelperKt.reportPoint("notification-guide-click", new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setSource("聊天页");
                pointBean.setActionp2(!NotificationHelper.checkNotifySetting(activity) ? "引导开启系统通知" : "引导开启APP通知");
                return null;
            }
        });

        getDataBinding().includeNotifyOpen.llNotifyOpen.setVisibility(View.GONE);
        SpManager.putUserLong(Constants.NOTIFY_TIPS_SHOW_TIME, System.currentTimeMillis());

    }

    @Override
    public void clickScreen() {
        ChatPopupChatListScreenBinding binding = DataBindingUtil.inflate(LayoutInflater.from(activity), R.layout.chat_popup_chat_list_screen, null, false);
        PopupUtils popupUtils = new PopupUtils.Builder(activity).setContentView(binding.getRoot())
                .setWidth(ViewGroup.LayoutParams.WRAP_CONTENT).setHeight(ViewGroup.LayoutParams.WRAP_CONTENT).build();
        switch (getViewModel().getCurrScreen()) {
            case ChatListViewModel.SCREEN_DEFAULT:
                binding.tvDefault.setTextColor(getResources().getColor(R.color.common_color_191919));
                binding.tvDefault.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.chat_icon_chat_list_default_black, 0, 0, 0);
                break;
            case ChatListViewModel.SCREEN_UNREAD:
                binding.tvUnread.setTextColor(getResources().getColor(R.color.common_color_191919));
                binding.tvUnread.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.chat_icon_chat_list_unread_black, 0, 0, 0);
                break;
            case ChatListViewModel.SCREEN_HISTORY:
                binding.tvHistory.setTextColor(getResources().getColor(R.color.common_color_191919));
                binding.tvHistory.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.chat_icon_chat_list_history_black, 0, 0, 0);
                break;
            default:
                break;
        }
        binding.tvDefault.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popupUtils.dismiss();
                if (getViewModel().getCurrScreen() == ChatListViewModel.SCREEN_DEFAULT) {
                    return;
                }
                getViewModel().setCurrScreen(ChatListViewModel.SCREEN_DEFAULT);
                getDataBinding().tvScreen.setText(R.string.chat_list_screen_default);
                LiveData<List<Conversation>> liveData = getViewModel().getAllConversations();
                getDataBinding().smartRefreshLayout.setEnableLoadMore(false);
                updateListView(ChatListHandler.Companion.getList(liveData.getValue()));
                ChatPointReporter.Companion.reportConversationExpose("默认排序");

            }
        });
        binding.tvUnread.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popupUtils.dismiss();
                if (getViewModel().getCurrScreen() == ChatListViewModel.SCREEN_UNREAD) {
                    return;
                }
                getViewModel().setCurrScreen(ChatListViewModel.SCREEN_UNREAD);
                getDataBinding().tvScreen.setText(R.string.chat_list_screen_only_unread);
                LiveData<List<Conversation>> liveData = getViewModel().getUnreadConversations();
                getDataBinding().smartRefreshLayout.setEnableLoadMore(false);
                updateListView(ChatListHandler.Companion.getList(liveData.getValue()));
                ChatPointReporter.Companion.reportConversationExpose("只看未读");

            }
        });
        binding.tvHistory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                popupUtils.dismiss();
                if (getViewModel().getCurrScreen() == ChatListViewModel.SCREEN_HISTORY) {
                    return;
                }
                getViewModel().setCurrScreen(ChatListViewModel.SCREEN_HISTORY);
                getDataBinding().tvScreen.setText(R.string.chat_list_screen_history);
                adapter.setList(null);
                showEmptyLoading();
                getViewModel().loadChatHistoryList();
                ChatPointReporter.Companion.reportConversationExpose("历史消息");

            }
        });
        popupUtils.showAsDropDown(getDataBinding().ivScreen, 0, -QMUIDisplayHelper.dp2px(activity, 10));
    }

    private void showHistoryFloatMenu(ChatCommonResponse.ChatCommonBean data) {
        FloatMenu floatMenu = new FloatMenu(activity);
        floatMenu.setCallback(new FloatMenuCallback() {
            @Override
            public void onMenuItemClick(MenuItem menuItem) {
                floatMenu.dismiss();
                switch (menuItem.getType()) {
                    case 0:
                        showSureDeleteHistoryConversationDialog(data);
                        break;
                    default:
                        break;
                }
            }
        });
        List<MenuItem> items = new ArrayList<>();
        items.add(new MenuItem(0, getResources().getString(R.string.chat_delete_history_tips)));
        floatMenu.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {

            }
        });
        floatMenu.items(items);
        floatMenu.show(AppPageRouter.getMainPoint(activity));
    }

    private void showSureDeleteHistoryConversationDialog(ChatCommonResponse.ChatCommonBean data) {
        CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(activity)
                .setTitle(getString(R.string.chat_delete_history_conversation_title))
                .setContent(getString(R.string.chat_delete_history_conversation))
                .setPositiveText(getResources().getString(R.string.common_delete))
                .setNegativeText(getResources().getString(R.string.common_cancel))
                .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                    @Override
                    public void onPositiveClick(Dialog dialog, View view) {
                        getViewModel().deleteHistory(data);
                    }

                    @Override
                    public void onNegativeClick(Dialog dialog, View view) {

                    }
                });
        builder.create().show();
    }

    @Override
    public OLoadingEmptyView getEmptyView() {
        return getDataBinding().icEmpty.emptyView;
    }

    @Override
    public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
        getViewModel().loadMoreChatHistoryList();
    }

    private int unReadCount;

    public void setUnreadCount(int count) {
        this.unReadCount = count;
    }


}