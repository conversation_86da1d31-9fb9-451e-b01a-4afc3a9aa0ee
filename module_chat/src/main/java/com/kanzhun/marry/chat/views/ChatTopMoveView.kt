package com.kanzhun.marry.chat.views

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.router.ChatPageRouterKT
import com.kanzhun.foundation.router.ChatPageRouterKT.jumpToSafetyGuardActivity
import com.kanzhun.marry.chat.R
import com.petterp.floatingx.imp.FxAppLifecycleProvider.Companion.getTopActivity
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.techwolf.lib.tlog.TLog

/**
 * 实现一个通用的view-group 大小 宽度：屏幕宽度-32.dp 居中显示  高度:155 具体绘制的内容内容为用户自己添加进去的内容
 * 包含以下功能
 * 1、出场动画  增加一个Y轴从-120dp 到0的移动效果300ms 缩放从70%到100% 用时600ms 透明度从0%到100% 用时300ms
 * 2、大卡片允许点击 点击后用户自己处理 toast提示"大卡片"
 * 3、大卡片允许拖拽 当按下卡片缩小到90%
 * 4、大卡片拖转到屏幕边缘 移出屏幕1/2 取消手势拖转效果 手势结束 view变成45*45大小 并吸附到屏幕左边或右边
    增加飞出动画：透明度从100%到0% 用时300ms 缩放从100%到7% 用时300ms
    增加飞入动画：透明度从0%到100% 用时300ms 缩放从7%到100% 用时300ms
 * 5、大卡片拖拽松手后 大卡片移动回原始位置  大卡片不允许上下移动
 * 6、小卡片允许点击 点击后变成大view
 * 7、小卡片吸附的位置在屏幕两侧，左或者右固定位置 不允许上下移动 和拖拽
 * 8、小卡片当吸附到左边时候使用有R.drawable.bg_floating_card_collapsed_left  右边使用 R.drawable.bg_floating_card_collapsed_right  当松手时 要先判断是否吸附到左边还是右边 然后使用对应的背景图

----------------
现在需求变更 从以上效果修改为下：

* 实现一个通用的view-group 大小 宽度：屏幕宽度-32.dp 居中显示  高度:160 具体绘制的内容内容为用户自己添加进去的内容
 * 包含以下功能
 * 1、出场动画  增加一个Y轴从-120dp 到0的移动效果300ms 缩放从70%到100% 用时600ms 透明度从0%到100% 用时300ms 只view第一次执行一次这个动画 之后不用这个动画
 * 2、大卡片允许点击 点击后用户自己处理
 *
 * 4、大卡片左滑或者右滑后 大卡片执行飞出动画  透明度从100%到0% 用时300ms 缩放从100%到7% 用时300ms
        小卡片执行渐变动画 从100%到0% 用时300ms 并吸附到屏幕左边或右边

 * 6、小卡片允许点击 点击后变成大view
    小卡片执行渐变动画 从100%到0% 用时300ms
    大卡片执行飞入动画  透明度从0%到100% 用时300ms 缩放从7%到100% 用时300ms

 * 7、小卡片吸附的位置在屏幕两侧，左或者右固定位置 不允许上下移动 和拖拽
 * 8、小卡片当吸附到左边时候使用有R.drawable.bg_floating_card_collapsed_left  右边使用 R.drawable.bg_floating_card_collapsed_right  当松手时 要先判断是否吸附到左边还是右边 然后使用对应的背景图

 */
class ChatTopMoveView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        // 常量定义
        private const val ANIMATION_DURATION = 300L
        private const val EXPAND_HEIGHT_DP = 160
        private const val COLLAPSE_SIZE_DP = 45
        private const val DRAG_SCALE = 0.9f // 修改为90%
        private const val MARGIN_DP = 16 // 左右边距，即32dp/2
        private const val FADE_DURATION = 200L // 淡入淡出动画时长
    }

    // 状态变量
    private var isCollapsed = false  // 是否为小卡片状态
    private var isDragging = false   // 是否正在拖拽
    private var isTouchBlocked = false // 防止触摸事件重入
    private var hasCollapseRequest = false // 标记是否已请求折叠，防止UP事件将其变回
    private var isCollapsing = false  // 标记是否正在执行折叠动画，防止重复触发
    private var isFirstAppearance = true // 标记是否是首次显示
    private var lastX = 0f           // 上次触摸的X坐标
    private var dragDeltaX = 0f      // 拖拽X方向移动距离
    private var initialMarginLeft = 0 // 初始左边距
    private var initialMarginTop = 0  // 初始顶部边距
    private var initialMarginRight = 0 // 初始右边距
    private var screenWidth = 0      // 屏幕宽度
    private var originalWidth = 0    // 原始宽度
    private var originalHeight = 0   // 原始高度
    private var myClickListener: OnClickListener? = null // 自定义点击监听器

    // 增加辅助视图用于动画过渡
    private var smallCardView: FrameLayout? = null // 小卡片视图
    private var largeCardView: FrameLayout? = null // 大卡片视图

    init {
        // 获取屏幕宽度
        screenWidth = context.resources.displayMetrics.widthPixels

        // 设置原始尺寸
        originalWidth = screenWidth - dpToPx(32)
        originalHeight = dpToPx(EXPAND_HEIGHT_DP)

        // 初始动画状态
        alpha = 0f
        scaleX = 0f
        scaleY = 0f

        // 等待视图绘制完成后获取初始margin并设置布局
        post {
            // 保存初始margin值
            saveInitialMargins()
        }
    }

    /**
     * 保存初始margin值
     */
    private fun saveInitialMargins() {
        val lp = layoutParams
        if (lp is ViewGroup.MarginLayoutParams) {
            initialMarginLeft = lp.leftMargin
            initialMarginTop = lp.topMargin
            initialMarginRight = lp.rightMargin
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()

        // 再次保存初始margin，以防初始化时尚未完全布局
        saveInitialMargins()
    }

    /**
     * 获取当前的MarginLayoutParams，如果不是则创建一个
     */
    private fun getOrCreateMarginLayoutParams(): ViewGroup.MarginLayoutParams {
        val lp = layoutParams
        return if (lp is ViewGroup.MarginLayoutParams) {
            lp
        } else {
            ViewGroup.MarginLayoutParams(lp)
        }
    }

    /**
     * 居中显示大卡片
     */
    private fun centerLargeCard() {
        val lp = getOrCreateMarginLayoutParams()
        lp.width = originalWidth
        lp.height = originalHeight

        // 保持原始topMargin不变
        lp.topMargin = initialMarginTop

        // 计算居中的leftMargin
        val newLeftMargin = (screenWidth - originalWidth) / 2
        lp.leftMargin = newLeftMargin

        // 应用新的布局参数
        layoutParams = lp
        requestLayout()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 根据当前状态计算尺寸
        val width = if (isCollapsed) dpToPx(COLLAPSE_SIZE_DP) else originalWidth
        val height = if (isCollapsed) dpToPx(COLLAPSE_SIZE_DP) else originalHeight

        // 设置自身尺寸
        setMeasuredDimension(width, height)

        // 测量子View（全部填满）
        val childWidthSpec = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY)
        val childHeightSpec = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
        for (i in 0 until childCount) {
            getChildAt(i).measure(childWidthSpec, childHeightSpec)
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        // 所有子View填满自己
        for (i in 0 until childCount) {
            getChildAt(i).layout(0, 0, measuredWidth, measuredHeight)
        }
    }

    override fun setOnClickListener(l: OnClickListener?) {
        // 保存自定义点击监听器
        this.myClickListener = l
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        // 触摸事件被阻止时直接拦截
        if (isTouchBlocked) {
            return true
        }

        // 折叠请求标记存在时拦截后续事件
        if (hasCollapseRequest) {
            if (event.action == MotionEvent.ACTION_DOWN) {
                hasCollapseRequest = false
            } else {
                return true
            }
        }

        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        // 折叠请求标记存在时强制拦截
        if (hasCollapseRequest) {
            return true
        }

        // 小卡片状态
        if (isCollapsed) {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> return true
                MotionEvent.ACTION_UP -> {
                    // 防止重入
                    isTouchBlocked = true
                    // 小卡片点击变大
                    expandCard()
                    // 延迟恢复触摸
                    postDelayed({ isTouchBlocked = false }, ANIMATION_DURATION + 50)
                    return true
                }
            }
            return super.onTouchEvent(event)
        }

        // 大卡片处理
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 重置状态
                hasCollapseRequest = false
                isDragging = false
                dragDeltaX = 0f

                // 记录初始触摸位置
                lastX = event.rawX

                // 不在这里缩小，只在确认拖拽时缩小
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                val deltaX = event.rawX - lastX

                // 开始拖拽（移动超过10px）
                if (!isDragging && Math.abs(deltaX) > 10) {
                    isDragging = true
                    // 确认拖拽时应用缩放效果
                    animate().scaleX(DRAG_SCALE).scaleY(DRAG_SCALE).setDuration(100).start()
                }

                if (isDragging && !isCollapsing) {  // 避免在折叠中继续拖拽
                    // 累计拖拽距离
                    dragDeltaX += deltaX
                    lastX = event.rawX

                    // 更新位置 - 使用margin
                    updateCardPositionDuringDrag(dragDeltaX)

                    // 检查是否拖出屏幕一半
                    val lp = getOrCreateMarginLayoutParams()
                    val currentLeftMargin = lp.leftMargin

                    // 计算拖出屏幕的距离
                    val leftOut = -currentLeftMargin
                    val rightOut = (currentLeftMargin + originalWidth) - screenWidth

                    if (leftOut > originalWidth / 2) {
                        // 标记正在折叠，防止重复触发
                        isCollapsing = true
                        // 标记已请求折叠
                        hasCollapseRequest = true
                        isTouchBlocked = true
                        // 左侧折叠
                        collapseToEdge(true)
                        // 延迟恢复触摸
                        postDelayed({
                            isTouchBlocked = false
                            isCollapsing = false  // 重置折叠中状态
                        }, ANIMATION_DURATION + 150)
                        return true
                    } else if (rightOut > originalWidth / 2) {
                        // 标记正在折叠，防止重复触发
                        isCollapsing = true
                        // 标记已请求折叠
                        hasCollapseRequest = true
                        isTouchBlocked = true
                        // 右侧折叠 - 直接设置到最终位置，避免闪烁
                        collapseToEdgeDirect(false)
                        // 延迟恢复触摸
                        postDelayed({
                            isTouchBlocked = false
                            isCollapsing = false  // 重置折叠中状态
                        }, ANIMATION_DURATION + 150)
                        return true
                    }

                    return true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                // 已请求折叠则不再处理
                if (hasCollapseRequest) {
                    return true
                }

                // 拖拽结束处理
                if (isDragging) {
                    handleDragEnd()
                    isDragging = false
                    dragDeltaX = 0f
                } else {
                    // 点击事件
                    // myClickListener?.onClick(this)
                    onExpandedClick(event)
                }
                return true
            }
        }

        return super.onTouchEvent(event)
    }

    /**
     * 拖拽过程中更新卡片位置
     */
    private fun updateCardPositionDuringDrag(deltaX: Float) {
        val lp = getOrCreateMarginLayoutParams()

        // 计算新的左边距 - 基于初始居中位置
        val centerLeftMargin = (screenWidth - originalWidth) / 2
        val newLeftMargin = (centerLeftMargin + deltaX).toInt()

        // 更新左边距
        lp.leftMargin = newLeftMargin

        // 保留原始的上边距
        lp.topMargin = initialMarginTop

        // 应用新布局
        layoutParams = lp
        requestLayout()
    }

    /**
     * 播放入场动画
     */
    private fun playEntranceAnimation() {
        // 确保大卡片居中
        centerLargeCard()

        // 设置初始Y轴位置为-120dp
        translationY = -dpToPx(120).toFloat()

        // 创建Y轴平移动画，从-120dp到0
        val translateYAnim = ObjectAnimator.ofFloat(this, "translationY", -dpToPx(120).toFloat(), 0f)
        translateYAnim.duration = 300L

        // 创建透明度动画，从0到1
        val alphaAnim = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f)
        alphaAnim.duration = 300L

        // 创建缩放动画，从70%到100%
        val scaleXAnim = ObjectAnimator.ofFloat(this, "scaleX", 0.7f, 1f)
        val scaleYAnim = ObjectAnimator.ofFloat(this, "scaleY", 0.7f, 1f)
        scaleXAnim.duration = 600L
        scaleYAnim.duration = 600L

        // 组合所有动画
        val animSet = AnimatorSet()
        animSet.playTogether(translateYAnim, alphaAnim, scaleXAnim, scaleYAnim)
        animSet.start()
    }

    /**
     * 处理拖拽结束
     */
    private fun handleDragEnd() {
        // 已折叠则不处理
        if (hasCollapseRequest || isCollapsed) {
            return
        }

        // 计算当前位置
        val lp = getOrCreateMarginLayoutParams()
        val currentLeftMargin = lp.leftMargin

        // 计算与左右边缘的距离
        val leftDistance = currentLeftMargin
        val rightDistance = screenWidth - (currentLeftMargin + originalWidth)

        if (leftDistance < originalWidth / 3 || rightDistance < originalWidth / 3) {
            // 靠近边缘，折叠
            hasCollapseRequest = true
            isTouchBlocked = true
            collapseToEdge(leftDistance < rightDistance)
            postDelayed({ isTouchBlocked = false }, ANIMATION_DURATION + 50)
        } else {
            // 回到中心位置
            returnToCenter()
        }
    }

    /**
     * 折叠到边缘
     */
    private fun collapseToEdge(isLeft: Boolean) {
        // 设置状态
        isCollapsed = true

        // 获取尺寸
        val collapsedSize = dpToPx(COLLAPSE_SIZE_DP)

        // 飞出动画 - 大卡片淡出并缩小
        val alphaAnim = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f)
        alphaAnim.duration = 300L

        val scaleXAnim = ObjectAnimator.ofFloat(this, "scaleX", 1f, 0.07f)
        val scaleYAnim = ObjectAnimator.ofFloat(this, "scaleY", 1f, 0.07f)
        scaleXAnim.duration = 300L
        scaleYAnim.duration = 300L

        val animSet = AnimatorSet()
        animSet.playTogether(alphaAnim, scaleXAnim, scaleYAnim)
        animSet.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                // 设置背景
                collapsedCard?.setBackgroundResource(
                    if (isLeft) R.drawable.bg_floating_card_collapsed_left
                    else R.drawable.bg_floating_card_collapsed_right
                )

                // 更新布局参数
                val lp = getOrCreateMarginLayoutParams()
                lp.width = collapsedSize
                lp.height = collapsedSize

                // 设置边距 - 保留原始topMargin
                lp.topMargin = initialMarginTop

                // 左右边缘位置
                if (isLeft) {
                    lp.leftMargin = initialMarginLeft // 靠左边
                    lp.rightMargin = 0
                } else {
                    lp.leftMargin = screenWidth - collapsedSize - initialMarginRight // 靠右边
                    lp.rightMargin = initialMarginRight
                }

                // 应用布局
                layoutParams = lp
                requestLayout()

                // 切换卡片显示
                expandedCard?.visibility = View.GONE
                collapsedCard?.visibility = View.VISIBLE

                // 更新小卡片UI
                updateImage(isLeft)

                // 设置初始值用于飞入动画
                scaleX = 0.07f
                scaleY = 0.07f
                alpha = 0f

                // 飞入动画 - 小卡片淡入并放大
                val flyInAlphaAnim = ObjectAnimator.ofFloat(this@ChatTopMoveView, "alpha", 0f, 1f)
                flyInAlphaAnim.duration = 300L

                val flyInScaleXAnim = ObjectAnimator.ofFloat(this@ChatTopMoveView, "scaleX", 0.07f, 1f)
                val flyInScaleYAnim = ObjectAnimator.ofFloat(this@ChatTopMoveView, "scaleY", 0.07f, 1f)
                flyInScaleXAnim.duration = 300L
                flyInScaleYAnim.duration = 300L

                val flyInAnimSet = AnimatorSet()
                flyInAnimSet.playTogether(flyInAlphaAnim, flyInScaleXAnim, flyInScaleYAnim)
                flyInAnimSet.start()
            }
        })
        animSet.start()
    }

    /**
     * 直接折叠到边缘，不使用中间动画，避免闪烁
     */
    private fun collapseToEdgeDirect(isLeft: Boolean) {
        // 设置状态
        isCollapsed = true

        // 获取尺寸
        val collapsedSize = dpToPx(COLLAPSE_SIZE_DP)

        // 计算最终位置
        val finalLeftMargin = if (isLeft) {
            initialMarginLeft
        } else {
            screenWidth - collapsedSize - initialMarginRight
        }

        // 先准备好布局参数
        val lp = getOrCreateMarginLayoutParams()

        // 保存原始位置数据，用于动画计算
        val originalLeft = lp.leftMargin
        val originalWidth = this.originalWidth

        // 设置背景
        collapsedCard?.setBackgroundResource(
            if (isLeft) R.drawable.bg_floating_card_collapsed_left
            else R.drawable.bg_floating_card_collapsed_right
        )

        // 切换卡片显示，准备好小卡布局，但先设置为不可见
        expandedCard?.visibility = View.GONE
        collapsedCard?.visibility = View.VISIBLE

        // 更新小卡片UI
        updateImage(isLeft)

        // 创建综合动画：位置、大小和透明度同时变化
        val animSet = AnimatorSet()

        // 淡出动画
        val alphaOut = ObjectAnimator.ofFloat(this, "alpha", 1f, 0.3f)
        alphaOut.duration = 150L

        // 缩小动画
        val scaleXOut = ObjectAnimator.ofFloat(this, "scaleX", 1f, 0.5f)
        val scaleYOut = ObjectAnimator.ofFloat(this, "scaleY", 1f, 0.5f)
        scaleXOut.duration = 150L
        scaleYOut.duration = 150L

        // 第一阶段：淡出并缩小
        val outAnimSet = AnimatorSet()
        outAnimSet.playTogether(alphaOut, scaleXOut, scaleYOut)

        outAnimSet.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                // 动画结束后直接切换到最终位置和尺寸
                lp.width = collapsedSize
                lp.height = collapsedSize
                lp.topMargin = initialMarginTop
                lp.leftMargin = finalLeftMargin
                lp.rightMargin = if (isLeft) 0 else initialMarginRight

                // 应用布局
                layoutParams = lp
                requestLayout()

                // 设置第二阶段初始值
                scaleX = 0.5f
                scaleY = 0.5f
                alpha = 0.3f

                // 第二阶段：淡入并放大到正常大小
                val alphaIn = ObjectAnimator.ofFloat(this@ChatTopMoveView, "alpha", 0.3f, 1f)
                val scaleXIn = ObjectAnimator.ofFloat(this@ChatTopMoveView, "scaleX", 0.5f, 1f)
                val scaleYIn = ObjectAnimator.ofFloat(this@ChatTopMoveView, "scaleY", 0.5f, 1f)

                alphaIn.duration = 150L
                scaleXIn.duration = 150L
                scaleYIn.duration = 150L

                val inAnimSet = AnimatorSet()
                inAnimSet.playTogether(alphaIn, scaleXIn, scaleYIn)
                inAnimSet.start()
            }
        })

        outAnimSet.start()
    }

    /**
     * 从小卡片展开为大卡片
     */
    private fun expandCard() {
        // 更新状态
        isCollapsed = false

        // 飞出动画 - 小卡片淡出
        val alphaAnim = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f)
        alphaAnim.duration = 300L

        val scaleXAnim = ObjectAnimator.ofFloat(this, "scaleX", 1f, 0.07f)
        val scaleYAnim = ObjectAnimator.ofFloat(this, "scaleY", 1f, 0.07f)
        scaleXAnim.duration = 300L
        scaleYAnim.duration = 300L

        val animSet = AnimatorSet()
        animSet.playTogether(alphaAnim, scaleXAnim, scaleYAnim)
        animSet.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                // 清除背景
                background = null

                // 恢复原始尺寸
                val lp = getOrCreateMarginLayoutParams()
                lp.width = originalWidth
                lp.height = originalHeight

                // 计算居中位置
                val centerLeftMargin = (screenWidth - originalWidth) / 2

                // 设置边距
                lp.topMargin = initialMarginTop
                lp.leftMargin = centerLeftMargin

                // 应用布局
                layoutParams = lp
                requestLayout()

                // 切换卡片显示
                expandedCard?.visibility = View.VISIBLE
                collapsedCard?.visibility = View.GONE

                // 设置初始状态用于飞入动画
                scaleX = 0.07f
                scaleY = 0.07f
                alpha = 0f

                // 飞入动画 - 大卡片淡入并放大
                val flyInAlphaAnim = ObjectAnimator.ofFloat(this@ChatTopMoveView, "alpha", 0f, 1f)
                flyInAlphaAnim.duration = 300L

                val flyInScaleXAnim = ObjectAnimator.ofFloat(this@ChatTopMoveView, "scaleX", 0.07f, 1f)
                val flyInScaleYAnim = ObjectAnimator.ofFloat(this@ChatTopMoveView, "scaleY", 0.07f, 1f)
                flyInScaleXAnim.duration = 300L
                flyInScaleYAnim.duration = 300L

                val flyInAnimSet = AnimatorSet()
                flyInAnimSet.playTogether(flyInAlphaAnim, flyInScaleXAnim, flyInScaleYAnim)
                flyInAnimSet.start()
            }
        })
        animSet.start()
    }

    /**
     * 拖拽后回到原位置（居中）
     */
    private fun returnToCenter() {
        // 动画回到居中位置
        val lp = getOrCreateMarginLayoutParams()

        // 计算居中的左边距
        val centerLeftMargin = (screenWidth - originalWidth) / 2

        // 创建值动画，从当前leftMargin动画到居中位置
        val currentLeftMargin = lp.leftMargin
        val valueAnimator = ValueAnimator.ofInt(currentLeftMargin, centerLeftMargin)
        valueAnimator.duration = ANIMATION_DURATION
        valueAnimator.addUpdateListener { animator ->
            val value = animator.animatedValue as Int
            lp.leftMargin = value
            layoutParams = lp
        }

        // 缩放动画
        val scaleAnimator = AnimatorSet()
        val scaleXAnim = ObjectAnimator.ofFloat(this, "scaleX", scaleX, 1f)
        val scaleYAnim = ObjectAnimator.ofFloat(this, "scaleY", scaleY, 1f)
        scaleAnimator.playTogether(scaleXAnim, scaleYAnim)
        scaleAnimator.duration = ANIMATION_DURATION

        // 同时播放两个动画
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(valueAnimator, scaleAnimator)
        animatorSet.start()
    }

    /**
     * DP转PX
     */
    private fun dpToPx(dp: Int): Int {
        return (dp * resources.displayMetrics.density).toInt()
    }

    private var rootView: View? = null
    //  扩展的卡片
    private var expandedCard: View? = null
    //  折叠的卡片
    private var collapsedCard: View? = null
    private var idBottomLayout: View? = null

    private fun initViews(context: Context?, btnStr: String?, style: Int, count: Int): View {
        rootView = LayoutInflater.from(context).inflate(R.layout.layout_floating_card2, null)
        rootView?.setLayoutParams(
            LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            )
        )

        updateView(btnStr, style, count)
        return rootView!!
    }

    fun updateView(btnStr: String?, style: Int, count: Int) {
        val contextRef = context
        if (contextRef == null) return
        if (rootView == null)return
        expandedCard = rootView?.findViewById<View?>(R.id.expanded_card)
        collapsedCard = rootView?.findViewById<View?>(R.id.collapsed_card)
        val idBottom = rootView!!.findViewById<TextView?>(R.id.idBottom)
        val idBg = rootView!!.findViewById<View?>(R.id.idBG)

        val screenWidth: Int = contextRef.getResources().getDisplayMetrics().widthPixels

        val expandedParams = LayoutParams(
            screenWidth - QMUIDisplayHelper.dp2px(contextRef, 32),
            QMUIDisplayHelper.dp2px(contextRef, EXPAND_HEIGHT_DP)
        )
        idBg.setLayoutParams(expandedParams)
        idBottom.setText(btnStr)
        val idIcon1 = rootView?.findViewById<View?>(R.id.idIcon1)
        val idIcon2 = rootView?.findViewById<View?>(R.id.idIcon2)
        val idIconContext = rootView?.findViewById<TextView?>(R.id.idIconContext)
        val idBottomText = rootView?.findViewById<View?>(R.id.idBottomText)
        val idLabel = rootView?.findViewById<View?>(R.id.idLabel)
        val idCardBg = rootView?.findViewById<ImageView?>(R.id.idCardBg)
        idBottomLayout = rootView?.findViewById<View?>(R.id.idBottomLayout)
        val idContent = rootView?.findViewById<TextView?>(R.id.idContent)
        val idCollapsedCardIcon = rootView?.findViewById<ImageView?>(R.id.idCollapsedCardIcon)
        if (style == 1) {
            idBg.setBackgroundResource(R.drawable.bg_floating_card_big_card_bg_blue)
            idCardBg?.setBackgroundResource(R.drawable.icon_floating_protect_card_bg_blue)
            idIcon1?.setVisibility(VISIBLE)
            idIcon2?.setVisibility(VISIBLE)
            idIconContext?.setVisibility(VISIBLE)
            idBottomText?.setVisibility(GONE)
            idLabel?.setVisibility(GONE)
            idContent?.setText("安全守护中")

            idIconContext?.setText(if (count >= 2) "已设置两位亲友守护" else "已设置一位亲友守护")
            if (count == 1) {
                val lp = idIconContext?.getLayoutParams() as ConstraintLayout.LayoutParams
                lp.setMarginStart(QMUIDisplayHelper.dp2px(contextRef, 40))
                idIcon2?.setVisibility(GONE)
                idIconContext.setLayoutParams(lp)
            } else {
                val lp = idIconContext?.getLayoutParams() as ConstraintLayout.LayoutParams
                lp.setMarginStart(QMUIDisplayHelper.dp2px(contextRef, 56))
                idIcon2?.setVisibility(VISIBLE)
                idIconContext.setLayoutParams(lp)
            }
            idCollapsedCardIcon?.setImageResource(R.drawable.icon_floating_protect_card_small_icon_blue)
        } else {
            idCollapsedCardIcon?.setImageResource(R.drawable.icon_floating_protect_card_small_icon_orange)
            idCardBg?.setBackgroundResource(R.drawable.icon_floating_protect_card_bg_orange)
            idContent?.setText("安全守护功能")
            idBg.setBackgroundResource(R.drawable.bg_floating_card_big_card_bg)
            idBottomText?.setVisibility(VISIBLE)
            idIcon1?.setVisibility(GONE)
            idIcon2?.setVisibility(GONE)
            idIconContext?.setVisibility(GONE)
            idLabel?.setVisibility(VISIBLE)
        }
        expandedCard?.visible()
        collapsedCard?.gone()
    }

    fun updateImage(isLeft: Boolean) {
        if (isLeft) {
            collapsedCard?.setBackgroundResource(R.drawable.bg_floating_card_collapsed_left)
        } else {
            collapsedCard?.setBackgroundResource(R.drawable.bg_floating_card_collapsed_right)
        }
    }

    private var recordId: String? = null
    private var peerId: String? = null

    fun show(
        btnStr: String,
        style: Int,
        count: Int,
        recordId: String,
        peerId: String
    ) {
        isShowing = true
        visible()
        this.recordId = recordId
        this.peerId = peerId
        addView(initViews(context,btnStr,style,count), LayoutParams(LayoutParams.MATCH_PARENT,LayoutParams.MATCH_PARENT))

        // 初始时显示大卡片
        expandedCard?.visibility = View.VISIBLE
        collapsedCard?.visibility = View.GONE

        // 只有首次显示时执行入场动画
        if (isFirstAppearance) {
            // 初始动画状态
            alpha = 0f
            scaleX = 0f
            scaleY = 0f

            // 触发入场动画
            playEntranceAnimation()

            // 标记已执行首次显示
            isFirstAppearance = false
        } else {
            // 非首次显示，保持完全不透明和原始大小
            alpha = 1f
            scaleX = 1f
            scaleY = 1f

            // 确保大卡片居中
            centerLargeCard()
        }
    }

    fun onExpandedClick(event: MotionEvent) {
        //判断点击位置是否在idBottomLayout内
        if (idBottomLayout != null) {
            // 获取idBottomLayout在屏幕中的坐标位置
            val location = IntArray(2)
            idBottomLayout!!.getLocationOnScreen(location)


            // 计算idBottomLayout的边界
            val left = location[0].toFloat()
            val top = location[1].toFloat()
            val right = left + idBottomLayout!!.getWidth()
            val bottom = top + idBottomLayout!!.getHeight()


            // 判断点击事件的坐标是否在idBottomLayout的范围内
            if (event.getRawX() >= left && event.getRawX() <= right && event.getRawY() >= top && event.getRawY() <= bottom) {
                // 点击在底部区域内，跳转到见面详情页面
                val topActivity = getTopActivity()
                if (topActivity != null) {
                    ChatPageRouterKT.jumpToMeetingLocationActivity(
                        topActivity,
                        recordId!!,
                        PageSource.NONE
                    )
                }
            } else {
                // 点击在其他区域，跳转到安全守护页面
                val topActivity = getTopActivity()
                if (topActivity != null) {
                    jumpToSafetyGuardActivity(topActivity, peerId?:"", PageSource.GUIDE_CARD)
                }
            }
        } else {
            // idBottomLayout为空时默认跳转到安全守护页面
            val topActivity = getTopActivity()
            if (topActivity != null) {
                jumpToSafetyGuardActivity(topActivity, peerId?:"", PageSource.GUIDE_CARD)
            }
        }
    }

    var isShowing: Boolean = false
}