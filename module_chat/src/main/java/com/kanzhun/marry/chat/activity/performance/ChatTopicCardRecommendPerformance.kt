package com.kanzhun.marry.chat.activity.performance

import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.model.message.ChatMessage
import com.kanzhun.foundation.model.message.CommonCardInfo
import com.kanzhun.foundation.model.message.MessageConstants.MSG_COMMON_CARD_TYPE_TOPIC_GAME
import com.kanzhun.foundation.model.message.MessageForCommonCard
import com.kanzhun.marry.chat.databinding.ChatFragmentChatBinding
import com.kanzhun.marry.chat.viewmodel.ChatViewModel
import org.json.JSONObject

/**
 * 话题卡片推荐逻辑
 */
class ChatTopicCardRecommendPerformance(
    private val chatViewModel: ChatViewModel,
    private val fragment: Fragment,
    private val mBinding: ChatFragmentChatBinding
) : AbsPerformance() {

    private var messageForCommonCard: MessageForCommonCard? = null
    private val handler = Handler(Looper.getMainLooper())
    private val resetTextRunnable = Runnable { resetTitleText() }
    private var originalText: CharSequence? = null

    // 重置标题文字为原始文字
    private fun resetTitleText() {
        originalText?.let {
            mBinding.titleBar.tvName.text = it
            mBinding.titleBar.llMood.visible()
            mBinding.titleBar.flIcon.visible()
            mBinding.titleBar.composeViewDaysSpent.visible()
            originalText = null
        }
    }

    fun getTopicCardAsMessage(): MessageForCommonCard? {
        return messageForCommonCard
    }

    fun needShowTopicCard(messageList: List<ChatMessage>?): Boolean {
        var needAdd = true
        if (!messageList.isNullOrEmpty()) {
            messageList.lastOrNull()?.let {
                //时间不超过6小时，则不添加
                if (System.currentTimeMillis() - it.time < 6 * 60 * 60 * 1000L) {
                    needAdd = false
                }
            }
        }

        return messageForCommonCard != null && needAdd
    }

    fun unableShowTopicCard() {
        if (messageForCommonCard != null) {
            messageForCommonCard = null
            //重新加载一次数据
            chatViewModel.requestMessages(false)
        }
    }

    override fun onCustom() {
        super.onCustom()
        fragment.liveEventObserve(LivedataKeyCommon.EVENT_KEY_TOPIC_CARD_RECOMMEND) { extend: String? ->
            if (!extend.isNullOrBlank()) {
                try {
                    val extendData = JSONObject(extend)
                    val userId = extendData.optString("userId")
                    if (chatViewModel.chatId != userId) {
                        return@liveEventObserve
                    }
                    messageForCommonCard = MessageForCommonCard().apply {
                        this.chatId = userId
                        this.commonCardInfo = CommonCardInfo(
                            extendData.optString("sourceId"),
                            MSG_COMMON_CARD_TYPE_TOPIC_GAME,
                            extendData.optString("title"),
                            extendData.optString("content")
                        )
                    }

                    //重新加载一次数据
                    chatViewModel.requestMessages(false)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        fragment.liveEventObserve(LivedataKeyCommon.EVENT_KEY_CHAT_MEETING_PLAN_TIP) { extend: String? ->
            if (!extend.isNullOrBlank()) {
                try {
                    val extendData = JSONObject(extend)
                    val chatId = extendData.optString("chatId")
                    if (chatViewModel.chatId != chatId) {
                        return@liveEventObserve
                    }

                    //重新加载一次顶部数据
                    chatViewModel.requestPlanCard()

                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        fragment.liveEventObserve(LivedataKeyCommon.EVENT_SYNC_CHAT_SEND_STATUS) { extend: String? ->
            if (!extend.isNullOrBlank()) {
                try {
                    val extendData = JSONObject(extend)
                    val userId = extendData.optString("userId")
                    val i = extendData.optInt("type",0)// 1:文字 2 语音
                    if (chatViewModel.chatId != userId) {
                        return@liveEventObserve
                    }

                    // 保存当前文字，只有在originalText为null时才保存，避免覆盖原始文字
                    if (originalText == null) {
                        originalText = mBinding.titleBar.tvName.text
                    }
                    
                    // 取消之前的定时任务（如果有）
                    handler.removeCallbacks(resetTextRunnable)

                    when(i){
                        1 ->{
                            mBinding.titleBar.tvName.text = "对方正在输入…"
                            mBinding.titleBar.llMood.gone()
                            mBinding.titleBar.flIcon.gone()
                            mBinding.titleBar.composeViewDaysSpent.gone()
                        }
                        2 ->{
                            mBinding.titleBar.tvName.text = "对方正在说话…"
                            mBinding.titleBar.llMood.gone()
                            mBinding.titleBar.flIcon.gone()
                            mBinding.titleBar.composeViewDaysSpent.gone()
                        }
                    }

                    // 2秒后重置文字
                    handler.postDelayed(resetTextRunnable, 2000)

                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        // 避免内存泄漏
        handler.removeCallbacks(resetTextRunnable)
    }
}