package com.kanzhun.marry.chat.util

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.marry.chat.R

fun bindChatListItemStatus(
    statusComposeView: ComposeView, // 置顶、屏蔽情况下的各种样式（置顶、屏蔽、气泡红点）
    conversation: Conversation
) {
    if (conversation.sort > 0 || conversation.shield == 1) {
        statusComposeView.visible()
        statusComposeView.onSetWindowContent { Status(conversation = conversation) }
    } else {
        statusComposeView.gone()
    }
}

@Composable
fun Status(conversation: Conversation) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        if (conversation.sort > 0) {
            Image(
                painter = painterResource(id = R.mipmap.chat_ic_sort_top),
                contentDescription = "image description",
                modifier = Modifier
                    .width(12.dp)
                    .height(12.dp)
            )
        }

        if (conversation.shield == 1) {
            Image(
                painter = painterResource(id = R.mipmap.chat_ic_notice_shield),
                contentDescription = "image description",
                modifier = Modifier
                    .width(12.dp)
                    .height(12.dp)
            )

            if (conversation.unreadCount > 0) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_notice_shield_dot),
                    contentDescription = "image description",
                    modifier = Modifier
                        .width(8.dp)
                        .height(8.dp)
                )
            }
        }
    }
}

@Preview
@Composable
private fun StatusPreview() {
    Status(conversation = Conversation().apply {
        sort = 1024
        shield = 1
        unreadCount = 128
    })
}