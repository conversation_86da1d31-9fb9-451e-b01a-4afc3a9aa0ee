package com.kanzhun.marry.chat.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ui.activity.BaseActivity
import com.kanzhun.foundation.kotlin.ktx.simpleGet
import com.kanzhun.foundation.kotlin.ktx.simplePost
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.api.KChatApi
import com.kanzhun.marry.chat.api.response.WechatExchangeDetailResponse
import com.kanzhun.utils.T
import kotlinx.coroutines.launch

/**
 * ViewModel for WeChat Exchange functionality
 */
class ChangeWXViewModel : BaseViewModel() {

    /**
     * UI State for WeChat Exchange screen
     */
    data class WechatExchangeUiState(
        val isLoading: Boolean = false,
        val wechatId: String = "",
        val chatId: String = "",
        val status: Int = 1, // 1-未解锁，2-已解锁
        val msgCount: Int = 0,
        val maxCount: Int = 0,
        val description: String = "",
        val parts: List<WechatExchangeDetailResponse.ProtocolPart> = emptyList(),
        val remainingEdits: Int = 0,
        val totalEdits: Int = 0,
        val error: String = "",
    )

    // UI state for the screen
    var uiState by mutableStateOf(WechatExchangeUiState())
        private set

    /**
     * Initialize ViewModel with chat ID
     * @param chatId ID of the chat partner
     */
    fun initialize(chatId: String) {
        uiState = uiState.copy(chatId = chatId)
        loadWechatExchangeDetails(chatId)
    }

    /**
     * Update WeChat ID in UI state
     * @param newValue New WeChat ID value
     */
    fun updateWechatId(newValue: String,onSuccess: () -> Unit,onError:(String)-> Unit) {
        "orange/wechat/exchange/saveWechat".simplePost(
            map = mapOf("wechatId" to newValue),
            autoErrorToast = true,
            simpleRequestCallback = object : SimpleRequestCallback() {

                override fun onSuccess() {
                    onSuccess()
                    refresh()
                }

                override fun dealFail(reason: ErrorReason?) {
                    onError(reason?.errReason?:"")

                }

            })

    }

    /**
     * Load WeChat exchange details from server
     * @param chatId Chat partner ID
     */
    private fun loadWechatExchangeDetails(chatId: String) {
        uiState = uiState.copy(isLoading = true, error = "")

        val api = RetrofitManager.getInstance().createApi(KChatApi::class.java)
        HttpExecutor.execute(
            api.getWechatExchangeDetail(chatId),
            object : BaseRequestCallback<WechatExchangeDetailResponse>() {
                override fun onSuccess(data: WechatExchangeDetailResponse) {
                    uiState = uiState.copy(
                        isLoading = false,
                        status = data.status,
                        msgCount = data.msgCount,
                        maxCount = data.maxCount,
                        description = data.description,
                        parts = data.parts,
                        remainingEdits = data.reduceUpdateCount,
                        totalEdits = data.totalUpdateCount,
                        wechatId = data.wechatId ?: "",
                        error = "",
                        chatId = chatId
                    )
                }

                override fun dealFail(reason: ErrorReason?) {
                    uiState = uiState.copy(
                        isLoading = false,
                        error = reason?.errReason ?: "加载失败，请重试"
                    )
                    T.ss(uiState.error)
                }
            }
        )
    }

    /**
     * Submit WeChat ID to server
     */
    fun submitWechatId(activity: BaseActivity,onSuccess: () -> Unit = {}) {
        if (uiState.wechatId.isBlank()) {
            return
        }

        "orange/wechat/exchange/checkUser".simpleGet(
            map = mapOf("chatId" to uiState.chatId),
            simpleRequestCallback = object : SimpleRequestCallback() {
                override fun onSuccess() {
                    submitChangeWx(onSuccess)
                }

                override fun dealFail(reason: ErrorReason?) {
                    //error code 1335 对方不支持交换微信号  1336 对方曾命中平台安全风险监测
                    when (reason?.errCode) {
                        1335 -> {
                            T.ss(reason.errReason)
                        }

                        1336 -> {
                            activity.showTwoButtonDialog(title = "发送交换确认", content = "对方曾命中平台安全风险监测，确定向Ta发送微信吗？", positiveText = "取消", negativeText = "确认发送", positiveButtonClick = {}, negativeButtonClick = {
                                submitChangeWx(onSuccess)
                            })
                        }

                        else -> {
                            T.ss(reason?.errReason)
                        }
                    }
                }
            })


    }

    private fun submitChangeWx(onSuccess: () -> Unit) {
        "orange/wechat/exchange/sendWechat".simplePost(
            map = mapOf("chatId" to uiState.chatId),
            autoErrorToast = true,
            simpleRequestCallback = object : SimpleRequestCallback() {
                override fun onSuccess() {
                    onSuccess()
                }

                override fun dealFail(reason: ErrorReason?) {
                }

            })
    }

    /**
     * Refresh WeChat exchange details
     */
    fun refresh() {
        loadWechatExchangeDetails(uiState.chatId)
    }
} 