package com.kanzhun.marry.chat.activity.christmas

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.kanzhun.common.base.compose.ui.ptr.OPtrIndicator
import com.kanzhun.common.base.compose.ui.ptr.PullToRefresh
import com.kanzhun.common.base.compose.ui.ptr.rememberPullToRefreshState
import com.kanzhun.common.kotlin.constant.HomeTabType
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.sendObjectLiveEvent
import com.kanzhun.common.photo.zoomable.previewer.PreviewerState
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.CardType
import com.kanzhun.foundation.model.message.ChatMessage
import com.kanzhun.foundation.model.message.MessageForChristmasCard
import com.kanzhun.foundation.model.message.MessageForTime
import com.kanzhun.foundation.model.message.OgChristmasCard
import com.kanzhun.foundation.model.message.OgccExchange
import com.kanzhun.foundation.model.message.OgccFindCp
import com.kanzhun.foundation.model.message.OgccFindCp.ImageInfo
import com.kanzhun.foundation.model.message.OgccGuessCp
import com.kanzhun.foundation.model.message.OgccTaskPost
import com.kanzhun.foundation.model.message.OgccWelcome
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.activity.Time
import com.kanzhun.marry.chat.api.response.GetLimitedActivityQuotaResponse
import com.techwolf.lib.tlog.TLog

private const val TAG = "SantaMessageList"

data class SantaMessageState(
    var chatMessages: List<ChatMessage> = mutableStateListOf<ChatMessage>(),
    var isPullToRefreshData: Boolean = false,
    var isRefreshing: Boolean = false,
    var isInformalUser: Boolean = false,
    var isResetUser: Boolean = false,
    var isAuthBlock: Boolean = false,
)

@Composable
fun SantaMessageList(
    modifier: Modifier = Modifier,

    contact: Contact? = Contact(),

    activityQuotaState: GetLimitedActivityQuotaResponse = GetLimitedActivityQuotaResponse(),

    santaMessageState: SantaMessageState,
    onRefresh: () -> Unit = {},

    findCpChanged: (findCp: OgccFindCp) -> Unit = {},
    onPreviewImage: (previewIndex: Int, images: List<ImageInfo>, previewerState: PreviewerState) -> Unit = { _, _, _ -> },

    onGuessCp: (guessCpMessage: MessageForChristmasCard, taskId: String, chooseUserId: String) -> Unit = { _, _, _ -> },
    onGuessCpRight: () -> Unit = {},

    onAbnormalResetBtnClick: () -> Unit = { },
    onAbnormalAuthBlockBtnClick: () -> Unit = { },

    inPreview: Boolean = false
) {
    val chatListState =
        rememberLazyListState(initialFirstVisibleItemIndex = santaMessageState.chatMessages.size)

    var preSantaMessageSize by remember { mutableIntStateOf(0) }

    LaunchedEffect(santaMessageState.chatMessages) {
        try {
            if (santaMessageState.isPullToRefreshData) {
                chatListState.scrollToItem(
                    (santaMessageState.chatMessages.size - preSantaMessageSize + 1)
                        .coerceAtMost(santaMessageState.chatMessages.size - 1).coerceAtLeast(0)
                )
                santaMessageState.isPullToRefreshData = false
            } else {
                if (preSantaMessageSize < santaMessageState.chatMessages.size) { // 如果是下拉刷新，不滑动列表
                    chatListState.scrollToItem(chatListState.layoutInfo.totalItemsCount)
                }
            }
        } catch (e: Throwable) {
            TLog.error(TAG, "chatListState.scrollToItem: $e")
        }
    }

    LaunchedEffect(santaMessageState) {
        preSantaMessageSize = santaMessageState.chatMessages.size
    }

    PullToRefresh(
        state = rememberPullToRefreshState(isRefreshing = santaMessageState.isRefreshing),
        onRefresh = {
            onRefresh()
        },
        indicator = { state, refreshTriggerDistance, _ ->
            OPtrIndicator(refreshTriggerDistance, state) // Using default WHITE color
        }
    ) {
        LazyColumn(
            modifier = modifier,
            state = chatListState,
            contentPadding = PaddingValues(
                start = 16.dp,
                end = 32.dp,
                top = 18.dp,
                bottom = 60.dp
            ),
            verticalArrangement = Arrangement.spacedBy(25.dp)
        ) {
            items(items = santaMessageState.chatMessages) { chatMessage ->
                if (chatMessage is MessageForChristmasCard) {
                    val isUserHidden =
                        if (inPreview) {
                            false
                        } else {
                            val isHidden by ServiceManager.getInstance().settingService.matchVisibleLiveData.observeAsState(
                                false
                            )
                            isHidden
                        }

                    val context = LocalContext.current
                    if (santaMessageState.isInformalUser) {
                        AbnormalInformal(
                            onBtnClick = {
                                // 未完善用户，跳转 F4
                                sendObjectLiveEvent(
                                    LivedataKeyCommon.EVENT_KEY_MAIN_TAB,
                                    HomeTabType.ME
                                )

                                reportAbnormalClick(status = "1")
                            },
                            contact = contact
                        )
                    } else if (santaMessageState.isResetUser) {
                        AbnormalInformal(
                            onBtnClick = {
                                onAbnormalResetBtnClick()

                                reportAbnormalClick(status = "1")
                            },
                            contact = contact
                        )
                    } else if (santaMessageState.isAuthBlock) {
                        AbnormalInformal(
                            onBtnClick = {
                                onAbnormalAuthBlockBtnClick()

                                reportAbnormalClick(status = "1")
                            },
                            contact = contact
                        )
                    } else if (isUserHidden) {
                        AbnormalHidden(
                            onBtnClick = {
                                MePageRouter.jumpToPrivacySettingActivity(context)

                                reportAbnormalClick(status = "2")
                            },
                            contact = contact
                        )
                    } else {
                        ChristmasCard(
                            contact = contact,

                            activityQuotaState = activityQuotaState,

                            chatMessage = chatMessage,

                            findCpChanged = findCpChanged,
                            onPreviewImage = onPreviewImage,

                            onGuessCp = { taskId, chooseUserId ->
                                onGuessCp(chatMessage, taskId, chooseUserId)
                            },
                            onGuessCpRight = onGuessCpRight
                        )
                    }
                } else if (chatMessage is MessageForTime) {
                    Time(messageForTime = chatMessage)
                }
            }
        }
    }
}

private fun reportAbnormalClick(status: String) {
    reportPoint("christ-hidecard-click") {
        type = status // 记录当前隐藏状态：1:非正式，2:隐身
    }
}

@Composable
private fun ChristmasCard(
    contact: Contact?,

    activityQuotaState: GetLimitedActivityQuotaResponse,

    chatMessage: MessageForChristmasCard,

    findCpChanged: (findCp: OgccFindCp) -> Unit = {},
    onPreviewImage: (previewIndex: Int, images: List<ImageInfo>, previewerState: PreviewerState) -> Unit = { _, _, _ -> },

    onGuessCp: (taskId: String, chooseUserId: String) -> Unit = { _, _ -> },
    onGuessCpRight: () -> Unit = {},
) {
    val christmasCard = chatMessage.getOgChristmasCard()
    val cardType = christmasCard?.christmasCardType ?: 0
    when (cardType) {
        CardType.WELCOME.type -> {
            if (christmasCard is OgccWelcome) {
                Welcome(welcomeCard = christmasCard, contact = contact)
            }
        }

        CardType.TASK_PRE.type -> {
            if (christmasCard is OgChristmasCard) {
                TaskPre(taskPreCard = christmasCard, contact = contact)
            }
        }

        CardType.FIND_CP.type -> {
            if (christmasCard is OgccFindCp) {
                FindCp(
                    findCp = christmasCard,
                    onPreviewImage = onPreviewImage,
                    findCpChanged = findCpChanged,
                    contact = contact,
                )
            }
        }

        CardType.GUESS_CP.type -> {
            if (christmasCard is OgccGuessCp) {
                GuessCp(
                    guessCp = christmasCard,
                    onGuessCp = onGuessCp,
                    onGuessCpRight = onGuessCpRight,
                    contact = contact,
                )
            }
        }

        CardType.TASK_POST.type -> {
            if (christmasCard is OgccTaskPost) {
                TaskPost(taskPost = christmasCard, contact = contact)
            }
        }

        CardType.TASK_END.type -> {
            if (christmasCard is OgChristmasCard) {
                TaskEnd(christmasCard = christmasCard, contact = contact)
            }
        }

        CardType.HIDE_TASK.type -> {
            if (christmasCard is OgChristmasCard) {
                HideTask(christmasCard = christmasCard, contact = contact)
            }
        }

        CardType.EXCHANGE.type -> {
            if (christmasCard is OgccExchange) {
                Exchange(
                    activityQuotaState = activityQuotaState,
                    exchange = christmasCard,
                    contact = contact,
                )
            }
        }

        else -> {
        }
    }
}

@Preview
@Composable
private fun PreviewSantaMessageList() {
    SantaMessageList(
        santaMessageState = SantaMessageState(chatMessages = MOCK_MESSAGES),
        inPreview = true
    )
}

@Preview
@Composable
private fun PreviewSantaMessageListInformalUser() {
    SantaMessageList(
        santaMessageState = SantaMessageState(chatMessages = MOCK_MESSAGES, isInformalUser = true),
        inPreview = true
    )
}

val MOCK_MESSAGES = mutableStateListOf<ChatMessage>().apply {
    add(MessageForChristmasCard().apply {
        setOgChristmasCard(OgccWelcome().apply {
            christmasCardType = CardType.WELCOME.type

            title = "Hi Seven！很高兴看到你"
            content =
                "今年圣诞节，我准备了 \u200b寻找CP\u2060 和 \u200b猜猜ta是谁\u2060 等有趣的游戏\n" +
                    "你可以获得 \u200b糖果券\u2060，用它兑换更多推荐嘉宾，也可以兑换圣诞限定的精美礼品。\n" +
                    "\u200b作为见面礼，我免费赠送了你三个糖果券，快去糖果商店看看吧~\u2060\n" +
                    "希望你可以喜欢我给你准备的这些圣诞礼物，也希望你可以和其他小伙伴一起，回忆。"
        })
    })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(OgChristmasCard().apply {
            christmasCardType = CardType.TASK_PRE.type

            title = "猜猜ta是谁"
            content = "我将会为你提供一些线索帮你寻找"
        })
    })

    add(MessageForTime().apply { time = 1723219306491L })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(MOCK_FIND_CP_IMAGE)
    })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(MOCK_GUESS_CP)
    })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(OgccTaskPost().apply {
            christmasCardType = CardType.TASK_POST.type

            title = "你的今日CP可能会在这些页面出现哦～"
        })
    })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(OgChristmasCard().apply {
            christmasCardType = CardType.TASK_END.type

            content = "恭喜你完成了所有任务，快去领取你的礼物吧！"
        })
    })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(OgChristmasCard().apply {
            christmasCardType = CardType.HIDE_TASK.type

            title =
                "恭喜你完成了今日的全部任务，我还在app的各个地方埋了多个彩蛋等你去解锁，找到对应页面点击圣诞元素小图标，即可获得糖果券，快去试试吧～"
            content = "可以去个人信息预览页看看哦～"
        })
    })

    add(MessageForTime().apply { time = 1733219306491L })

    add(MessageForChristmasCard().apply {
        setOgChristmasCard(OgccExchange().apply {
            christmasCardType = CardType.EXCHANGE.type

            content = "去糖果商店逛逛吧去糖果商店逛逛吧去糖果商店逛逛吧～"
            btnText = "糖果商店"
        })
    })
}