package com.kanzhun.marry.chat.activity.christmas

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.rememberAsyncImagePainter
import coil.size.Size
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.common.photo.image.previewer.ImagePreviewer
import com.kanzhun.common.photo.zoomable.pager.PagerGestureScope
import com.kanzhun.common.photo.zoomable.previewer.PreviewerState
import com.kanzhun.common.photo.zoomable.previewer.rememberPreviewerState
import com.kanzhun.common.util.LDate
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.MessageForChristmasCard
import com.kanzhun.foundation.model.message.MessageForTime
import com.kanzhun.foundation.model.message.OgccFindCp
import com.kanzhun.foundation.model.message.OgccFindCp.ImageInfo
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.api.response.GetLimitedActivityQuotaResponse
import kotlinx.coroutines.launch

// 1 圣诞开聊卡片 2 任务前置提示卡片 3 找cp卡片 4 猜cp卡片 5 任务后提示卡片 6 任务完成提示卡片 7 隐藏任务卡 8 兑换提示卡片
@Composable
fun SantaClausScreen(
    modifier: Modifier = Modifier,

    contact: Contact?,

    activityQuotaState: GetLimitedActivityQuotaResponse,

    santaMessageState: SantaMessageState,
    onRefresh: () -> Unit = {},

    findCpChanged: (findCp: OgccFindCp) -> Unit = {},

    onGuessCp: (guessCpMessage: MessageForChristmasCard, taskId: String, chooseUserId: String) -> Unit = { _, _, _ -> },
    onGuessCpRight: () -> Unit = {},

    onAbnormalResetBtnClick: () -> Unit = {},
    onAbnormalAuthBlockBtnClick: () -> Unit = {},

    onBackPressed: () -> Unit = {},
    onTitleClick: () -> Unit = {},

    inPreview: Boolean = false,
) {
    Box(modifier = modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = R.mipmap.chat_ic_noise),
            contentDescription = "image description",
            contentScale = ContentScale.Companion.Crop,
            modifier = Modifier.Companion
                .background(color = Color(0xFF114838))
                .fillMaxSize()
        )

        var previewIndex by remember { mutableIntStateOf(-1) }
        var previewImages by remember { mutableStateOf(emptyList<ImageInfo>()) }
        val ps = rememberPreviewerState(pageCount = { 0 })
        var previewerState: PreviewerState by remember { mutableStateOf(ps) }

        BackHandler {
            if (previewIndex >= 0) {
                previewIndex = -1
            } else {
                onBackPressed()
            }
        }

        Column(
            modifier = Modifier.Companion
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            SantaTitleBar(
                contact = contact,
                activityQuotaState = activityQuotaState,
                onBackPressed = onBackPressed,
                onTitleClick = onTitleClick
            )

            SantaMessageList(
                contact = contact,

                activityQuotaState = activityQuotaState,

                santaMessageState = santaMessageState,
                onRefresh = onRefresh,

                findCpChanged = findCpChanged,
                onPreviewImage = { index, images, state ->
                    previewIndex = index
                    previewImages = images
                    previewerState = state
                },

                onGuessCp = onGuessCp,
                onGuessCpRight = onGuessCpRight,

                onAbnormalResetBtnClick = onAbnormalResetBtnClick,
                onAbnormalAuthBlockBtnClick = onAbnormalAuthBlockBtnClick,

                inPreview = inPreview
            )
        }

        PreviewImage(
            previewerState = previewerState,
            previewIndex = previewIndex,
            images = previewImages,
            onClosePreview = {
                previewIndex = -1
            }
        )
    }
}

@Composable
private fun PreviewImage(
    previewerState: PreviewerState = rememberPreviewerState(pageCount = { 0 }) { },
    previewIndex: Int = -1,
    images: List<ImageInfo> = emptyList(),
    onClosePreview: () -> Unit = {}
) {
    val scope = rememberCoroutineScope()

    LaunchedEffect(previewIndex) {
        if (previewIndex >= 0 && previewIndex < images.size) {
            scope.launch {
                previewerState.enterTransform(index = previewIndex)
            }
        } else {
            scope.launch {
                previewerState.exitTransform()
            }
        }
    }

    ImagePreviewer(
        state = previewerState,
        detectGesture = PagerGestureScope(
            onTap = {
                onClosePreview()
            }
        ),
        imageLoader = { index ->
            val image = images[index]

            val data = image.url

            val imageRequest =
                coil.request.ImageRequest.Builder(LocalContext.current)
                    .data(data)
                    .size(Size.ORIGINAL)
                    .build()

            val painter = rememberAsyncImagePainter(imageRequest)

            Pair(painter, painter.intrinsicSize)
        },
        pageDecoration = { page, innerPage ->
            val res = innerPage()

            Box(
                modifier = Modifier
                    .fillMaxSize()
            ) {
                if (page >= 0 && page < images.size) {
                    val image = images[page]

                    if (!image.title.isNullOrEmpty()) {
                        Text(
                            text = image.title ?: "",
                            color = Color(0xFFFFFFFF),
                            maxLines = 3,
                            fontSize = 20.sp,
                            overflow = TextOverflow.Ellipsis,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .background(color = colorResource(R.color.common_black_30))
                                .padding(16.dp)
                                .fillMaxWidth()
                                .align(alignment = Alignment.BottomCenter)
                        )
                    }
                }
            }

            res
        },
    )
}

@Composable
fun SantaMessage(
    modifier: Modifier = Modifier,
    contact: Contact?,
    inExceptionalSituation: Boolean = false,
    messageCard: @Composable RowScope.() -> Unit
) {
    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(10.dp)) {
        SantaAvatar(contact = contact)

        if (inExceptionalSituation) {
            Abnormal(contact = contact)
        } else {
            messageCard()
        }
    }
}

@Composable
fun SantaAvatar(
    contact: Contact?,
    modifier: Modifier = Modifier
) {
    val avatar = contact?.avatar
    if (avatar.isNullOrEmpty()) {
        Image(
            painter = painterResource(id = R.mipmap.chat_ic_santa_claus),
            contentDescription = "image description",
            modifier = modifier.size(40.dp),
        )
    } else {
        OImageView2(
            imageUrl = avatar,
            modifier = modifier.size(40.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewSantaClausScreen() {
    SantaClausScreen(
        contact = Contact().apply {
            nickName = "圣诞老人"
        },
        activityQuotaState = GetLimitedActivityQuotaResponse(),
        santaMessageState = SantaMessageState(chatMessages = MOCK_MESSAGES),
        inPreview = true
    )
}