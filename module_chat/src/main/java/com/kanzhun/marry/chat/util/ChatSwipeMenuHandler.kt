package com.kanzhun.marry.chat.util

import android.annotation.SuppressLint
import android.app.Activity
import android.text.TextUtils
import androidx.core.view.isNotEmpty
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ExecutorFactory
import com.kanzhun.common.util.LText
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.message.MessageConstants.MATCHING_STATUS_DATING
import com.kanzhun.foundation.model.message.MessageConstants.MATCHING_STATUS_LOVERS
import com.kanzhun.foundation.model.message.MessageConstants.MSG_CONTACT_TYPE_USER
import com.kanzhun.foundation.model.message.MessageConstants.MSG_DATA_SYNC_NOTICE
import com.kanzhun.foundation.model.message.MessageConstants.MSG_DATA_SYNC_NOTICE_FILM
import com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_CHRISTMAS
import com.kanzhun.foundation.model.message.MessageConstants.SYS_ID_OFFICIAL_YUEYUE
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.api.ChatApi
import com.kanzhun.marry.chat.databinding.ChatItemChatListBinding
import com.kanzhun.marry.chat.dialog.DeleteRelationDialog
import com.kanzhun.marry.chat.dialog.DeleteRelationType
import com.kanzhun.marry.chat.point.ChatPointReporter
import com.kanzhun.marry.chat.views.SwipeLayout
import com.kanzhun.utils.T

/**
 * Handler for chat list swipe menu actions
 * Manages swipe actions for chat list items including block/unblock and pin/unpin functionality
 */
class ChatSwipeMenuHandler(
    private val activity: Activity,
    private val recyclerView: RecyclerView,
    private val showFloatMenuCallback: (Conversation) -> Unit
) {
    /**
     * Setup swipe actions for chat list items
     */
    @SuppressLint("SetTextI18n")
    fun setupSwipeActions(binding: ChatItemChatListBinding, conversation: Conversation) {
        // Get the swipe layout and menu views
        val swipeLayout = binding.swipeLayout

        // 只有当systemId为SYS_ID_OFFICIAL_YUEYUE时才启用侧滑功能
        val chatId = LText.getInt(conversation.chatId)
        val disableSwipe =
            // Disable swipe for specific chat IDs
            chatId == MSG_DATA_SYNC_NOTICE // 点赞列表
                || chatId == MSG_DATA_SYNC_NOTICE_FILM // 胶片列表
        swipeLayout.setSwipeEnabled(!disableSwipe)

        // Update button text based on current shield status
        if (conversation.shield == 1) {
            binding.swipeMenu.tvBlockMonthly.text = "取消屏蔽"
        } else {
            binding.swipeMenu.tvBlockMonthly.text = "屏蔽"
        }

        // Set up click listeners for the menu options
        binding.swipeMenu.tvBlockMonthly.setOnClickListener { v ->
            fun requestShield(shieldType: Int) {
                // Call the API to update shield status using HttpExecutor
                val responseObservable =
                    RetrofitManager.getInstance().createApi(ChatApi::class.java)
                        .updateShield(conversation.chatId, shieldType)
                HttpExecutor.requestSimple(
                    responseObservable,
                    object : SimpleRequestCallback(true) {
                        override fun onSuccess() {
                            ExecutorFactory.execLocalTask {
                                // Update local conversation shield status
                                conversation.shield = shieldType
                                ServiceManager.getInstance().conversationService.updateConversation(
                                    conversation
                                )
                                ExecutorFactory.runOnUiThread {
                                    if (shieldType == 1) {
                                        binding.swipeMenu.tvBlockMonthly.text = "取消屏蔽"
                                    } else {
                                        binding.swipeMenu.tvBlockMonthly.text = "屏蔽"
                                    }
                                }
                            }
                        }

                        override fun dealFail(reason: ErrorReason) {
                            ExecutorFactory.runOnUiThread {
                                if (shieldType == 1) {
                                    T.ss("屏蔽失败，请稍后重试")
                                } else {
                                    T.ss("取消屏蔽失败，请稍后重试")
                                }
                            }
                        }
                    })
            }

            if (conversation.shield == 0) { // 用户想要屏蔽
                shouldShowSureShieldConversationDialog(
                    conversation,
                    onConfirm = {
                        // Handle cancel action
//                        swipeLayout.closeMenu()

                        requestShield(shieldType = 1)
                    },
                    onCancel = {
                        // Handle cancel action
                        swipeLayout.closeMenu()
                    }
                )
            } else { // 用户想要解除屏蔽
//                swipeLayout.closeMenu()

                requestShield(shieldType = 0)
            }
        }

        // Update button text based on current sort status (assuming sort > 0 means pinned)
        if (conversation.sort > 0) {
            binding.swipeMenu.tvCancelPin.text = "取消置顶"
        } else {
            binding.swipeMenu.tvCancelPin.text = "置顶"
        }

        binding.swipeMenu.tvCancelPin.setOnClickListener { v ->
            // Handle pin/unpin action
//            swipeLayout.closeMenu()

            // Determine the pin type based on current status
            val pinType =
                if (conversation.sort > 0) 0 else 1 // 1 for pin (top), 0 for unpin (cancel top)

            // Call the API to update pin status using HttpExecutor
            HttpExecutor.requestSimple(
                RetrofitManager.getInstance().createApi(ChatApi::class.java)
                    .updateTop(conversation.chatId, pinType),
                object : SimpleRequestCallback(true) {
                    override fun onSuccess() {
                        ExecutorFactory.execLocalTask {
                            // Update local conversation sort status
                            conversation.sort = if (pinType > 0) 1 else 0
                            ServiceManager.getInstance().conversationService.updateConversation(
                                conversation
                            )

                            ExecutorFactory.runOnUiThread {
                                // Update the button text based on new pin status
                                if (pinType == 1) {
                                    binding.swipeMenu.tvCancelPin.text = "取消置顶"
                                    T.ss("已置顶")
                                } else {
                                    binding.swipeMenu.tvCancelPin.text = "置顶"
                                    T.ss("已取消聊天置顶")
                                }
                            }
                        }
                    }

                    override fun dealFail(reason: ErrorReason) {
                        ExecutorFactory.runOnUiThread {
                            if (pinType == 1) {
                                T.ss("置顶失败，请稍后重试")
                            } else {
                                T.ss("取消置顶失败，请稍后重试")
                            }
                        }
                    }
                }
            )
        }

        // 设置取消匹配按钮点击事件
        val contact =
            ServiceManager.getInstance().contactService.getContactById(conversation.chatId)
        if ((contact != null && contact.userType == MSG_CONTACT_TYPE_USER)
            && conversation.relationStatus != MATCHING_STATUS_DATING
            && conversation.relationStatus != MATCHING_STATUS_LOVERS
        ) {
            binding.swipeMenu.tvCancelMatch.visibility = android.view.View.VISIBLE

            // 设置取消匹配按钮点击事件
            binding.swipeMenu.tvCancelMatch.setOnClickListener { v ->
                swipeLayout.closeMenu()
                showFloatMenuCallback(conversation)
            }
        } else {
            binding.swipeMenu.tvCancelMatch.visibility = android.view.View.GONE
        }

        // Set swipe listener to handle swipe events
        swipeLayout.setSwipeListener(object : SwipeLayout.SwipeListener {
            override fun onSwipeStart() {
                // Close any other open swipe layouts
                closeOtherSwipeLayouts(swipeLayout)
            }

            override fun onSwipeEnd(isOpen: Boolean) {
                // Report analytics when menu is opened
            }
        })

        // 设置点击事件监听器
        swipeLayout.setOnClickListener(object : SwipeLayout.OnClickListener {
            override fun onClick() {
                val systemId = conversation.systemId

                when (systemId) {
                    SYS_ID_OFFICIAL_CHRISTMAS.toLong() -> {
                        // 进入圣诞活动
                        ChatPageRouter.jumpToSantaClausActivity(activity, PageSource.CHAT_LIST)
                    }

                    SYS_ID_OFFICIAL_YUEYUE.toLong() -> {
                        // 进入小红娘月月
                        ChatPageRouter.jumpToYueYueActivity(
                            activity,
                            conversation.chatId ?: "",
                            PageSource.CHAT_LIST
                        )
                    }

                    else -> {
                        // 点赞列表
                        val id = LText.getLong(conversation.chatId)
                        if (!TextUtils.isEmpty(conversation.jumpProto) && (id >= MSG_DATA_SYNC_NOTICE && id <= 119999)) {
                            ProtocolHelper.parseProtocol(conversation.jumpProto)
                        } else {
                            val contact =
                                ServiceManager.getInstance().contactService.getContactById(
                                    conversation.chatId
                                )
                            if (contact == null) {
                                T.ss("数据获取中...")
                                return
                            }
                            ChatPageRouter.jumpToSingleChatActivity(
                                activity,
                                conversation.chatId,
                                systemId
                            )
                        }
                    }
                }

                ChatPointReporter.Companion.reportConversationClick(conversation)
            }
        })
    }

    /**
     * Close all other open swipe layouts except the current one
     */
    fun closeOtherSwipeLayouts(currentLayout: SwipeLayout) {
        if (recyclerView.isNotEmpty()) {
            for (i in 0 until recyclerView.childCount) {
                val itemView = recyclerView.getChildAt(i)
                if (itemView != null) {
                    val dataBinding = DataBindingUtil.getBinding<ViewDataBinding>(itemView)
                    if (dataBinding is ChatItemChatListBinding) {
                        val binding = dataBinding
                        if (binding.swipeLayout !== currentLayout && binding.swipeLayout.isMenuOpen) {
                            binding.swipeLayout.closeMenu()
                        }
                    }
                    // No need to handle ChatItemChatHistoryBinding as it doesn't have swipeLayout
                }
            }
        }
    }

    /**
     * Close all open swipe layouts
     */
    fun closeAllSwipeLayouts() {
        if (recyclerView.isNotEmpty()) {
            for (i in 0 until recyclerView.childCount) {
                val itemView = recyclerView.getChildAt(i)
                if (itemView != null) {
                    val dataBinding = DataBindingUtil.getBinding<ViewDataBinding>(itemView)
                    if (dataBinding is ChatItemChatListBinding) {
                        val binding = dataBinding
                        if (binding.swipeLayout.isMenuOpen) {
                            binding.swipeLayout.closeMenu()
                        }
                    }
                    // No need to handle ChatItemChatHistoryBinding as it doesn't have swipeLayout
                }
            }
        }
    }

    private fun shouldShowSureShieldConversationDialog(
        conversation: Conversation,
        onConfirm: () -> Unit,
        onCancel: () -> Unit
    ) {
        // 单独给月月弹框挽留
        if (conversation.systemId.toInt() == SYS_ID_OFFICIAL_YUEYUE) {
            DeleteRelationDialog(
                context = activity,
                relationType = DeleteRelationType.SHIELD
            ).show { confirm: Boolean ->
                if (confirm) {
                    onConfirm()
                } else {
                    onCancel()
                }
                null
            }
        } else {
            // 其他消息，直接屏蔽
            onConfirm()
        }
    }
}
