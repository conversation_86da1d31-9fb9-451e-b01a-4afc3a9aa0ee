package com.kanzhun.marry.chat.fragment.singlechat;

import static com.kanzhun.foundation.utils.point.PointHelperKt.reportPoint;
import static com.kanzhun.marry.chat.bindadapter.ChatBindingAdapterHelper.bindSameActivityView;
import static com.kanzhun.marry.chat.bindadapter.TacitTestComposableKt.prepareAnimatingTacitTestCard;
import static com.kanzhun.marry.chat.fragment.singlechat.ChatMeetingPlanHandlerKt.bindMeetingPlanView;
import static com.kanzhun.marry.chat.fragment.singlechat.ChatMeetingPlanHandlerKt.handlerChatMeetingPlan;
import static com.kanzhun.marry.chat.views.MeetingPlanTipKt.scrollToTaskCardMessage;

import android.content.DialogInterface;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kanzhun.common.base.AllBaseActivity;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.dialog.CommonLayoutDialog;
import com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.LDate;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.bean.MomentListItemBean;
import com.kanzhun.foundation.api.bean.VideoStoryBean;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.kotlin.common.performance.PerformManager;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.media.IMeetingService;
import com.kanzhun.foundation.media.MediaConstants;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.CommonCardInfo;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForActivityMedia;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForCommonCard;
import com.kanzhun.foundation.model.message.MessageForDateCard;
import com.kanzhun.foundation.model.message.MessageForHint;
import com.kanzhun.foundation.model.message.MessageForLike;
import com.kanzhun.foundation.model.message.MessageForLinkCall;
import com.kanzhun.foundation.model.message.MessageForLoveCard;
import com.kanzhun.foundation.model.message.MessageForMood;
import com.kanzhun.foundation.model.message.MessageForText;
import com.kanzhun.foundation.page.VideoAudioFloatPageManager;
import com.kanzhun.foundation.player.OPlayerHelper;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.router.SocialPageRouter;
import com.kanzhun.foundation.ui.Navigation;
import com.kanzhun.foundation.utils.HiMessage;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.foundation.utils.ModelCopyUtil;
import com.kanzhun.foundation.utils.RecycleviewExposureUtils;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.localNotify.MeetingPlanProtectNotificationManager;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.activity.performance.ChatBottomLayoutPerformance;
import com.kanzhun.marry.chat.activity.performance.ChatDialogPerformance;
import com.kanzhun.marry.chat.activity.performance.ChatItemExposePerformance;
import com.kanzhun.marry.chat.activity.performance.ChatMomentRecommendPerformance;
import com.kanzhun.marry.chat.activity.performance.ChatRecommendEmotionPerformance;
import com.kanzhun.marry.chat.activity.performance.ChatRestrictPerformance;
import com.kanzhun.marry.chat.activity.performance.ChatTopicCardRecommendPerformance;
import com.kanzhun.marry.chat.adapter.ChatAdapter;
import com.kanzhun.marry.chat.api.response.ProtectMeetChatCard;
import com.kanzhun.marry.chat.bindadapter.ChatBindingAdapter;
import com.kanzhun.marry.chat.databinding.ChatFragmentChatBinding;
import com.kanzhun.marry.chat.databinding.ChatLayoutLikeAbFaceDetailDialogBinding;
import com.kanzhun.marry.chat.databinding.ChatLayoutLikeAvatarDetailDialogBinding;
import com.kanzhun.marry.chat.databinding.ChatLayoutLikePhotoStoryDetailDialogBinding;
import com.kanzhun.marry.chat.databinding.ChatLayoutLikeTextAnswerDetailDialogBinding;
import com.kanzhun.marry.chat.databinding.ChatLayoutLikeVideoStoryDialogBinding;
import com.kanzhun.marry.chat.databinding.ChatViewChatInputBinding;
import com.kanzhun.marry.chat.dialog.LoveStatusPopup;
import com.kanzhun.marry.chat.fragment.SingleChatBaseFragment;
import com.kanzhun.marry.chat.model.ChatClickOptionBean;
import com.kanzhun.marry.chat.point.ChatPointReporter;
import com.kanzhun.marry.chat.viewmodel.ChatViewModel;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.platform.Utils;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.sankuai.waimai.router.Router;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnLoadMoreListener;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by guofeng
 * on 2019/3/12.
 */
public class SingleChatFragment extends SingleChatBaseFragment<ChatFragmentChatBinding, ChatViewModel> implements IChatEventCallback, Observer<HiMessage> {
    private static final String TAG = "SingleChatFragment";

    private boolean hasMoreMessage = true;

    private ChatBottomLayoutPerformance chatBottomLayoutPerformance;

    //推荐话题卡片相关逻辑
    private ChatTopicCardRecommendPerformance chatTopicCardRecommendPerformance;

    //动态推荐相关逻辑
    private ChatMomentRecommendPerformance chatMomentRecommendPerformance;

    // 聊天页获取限制开聊红条详情
    private ChatRestrictPerformance chatRestrictPerformance;

    private ChatItemExposePerformance chatItemExposePerformance;

    private ChatEventRegister chatEventRegister;//用来监听各种事件，通过回调来避免引用各种类和常量

    private PerformManager performManager;

    private RecycleviewExposureUtils mRecycleviewExposureUtils;

    @Override
    protected void onDraftSetDone() {
        super.onDraftSetDone();
        if (chatBottomLayoutPerformance != null) {
            chatBottomLayoutPerformance.onDraftSetDone();
        }
    }

    @Override
    public ChatViewChatInputBinding getViewChatInputBinding() {
        return getDataBinding().vgChatInput;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_fragment_chat;
    }

    @Override
    public RecyclerView getContentRecyclerView() {
        return getDataBinding().recyclerView;
    }

    @Override
    protected EditText getEditText() {
        return getDataBinding().vgChatInput.etInput;
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        performManager = new PerformManager(this);
        chatItemExposePerformance = new ChatItemExposePerformance(getDataBinding(), getViewModel());
        ViewGroup.MarginLayoutParams titleParams = (ViewGroup.MarginLayoutParams) getDataBinding().titleBar.getRoot().getLayoutParams();
        titleParams.topMargin = StatusBarKt.getStatusBarHeight(requireActivity());

        if (!TextUtils.isEmpty(nickName)) {
            getDataBinding().titleBar.setTitle(nickName);
        }

        if (!TextUtils.isEmpty(moodIcon) && !TextUtils.isEmpty(moodTitle)) {
            getDataBinding().titleBar.setMoodUrl(moodIcon);
            getDataBinding().titleBar.setMoodTitle(moodTitle);
        }

        getContentRecyclerView().setItemAnimator(null);
        getContentRecyclerView().setAdapter(adapter);
        if (getViewModel().isOfficialSystemActivity()) {
            getContentRecyclerView().setBackgroundColor(activity.getColor(R.color.common_color_F5F5F5));
            getDataBinding().llMain.setBackgroundColor(getContext().getColor(R.color.common_color_F5F5F5));
            getDataBinding().titleBar.idBarRoot.setBackgroundColor(getContext().getColor(R.color.common_color_F5F5F5));
            getDataBinding().llHeader.setBackgroundColor(getContext().getColor(R.color.common_color_F5F5F5));
            getDataBinding().titleBar.tvOfficial.setText("官方活动");
        } else {
            getDataBinding().llMain.setBackgroundColor(activity.getColor(R.color.common_color_F4F4F6));
        }

        adapter.setChatViewModel(getViewModel());
        ServiceManager.getInstance().getContactService().registerContactChangeListener(this, getViewModel().getChatId());
        ((LinearLayoutManager) getContentRecyclerView().getLayoutManager()).setStackFromEnd(true);
        adapter.registerChangeListener(this);
        initChatPerformance();

        getDataBinding().refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                getDataBinding().refreshLayout.finishLoadMore(true);
                getViewModel().requestMessages(false);
            }
        });

        getDataBinding().refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(RefreshLayout refreshLayout) {
                getDataBinding().refreshLayout.finishRefresh(true); //结束刷新
                getViewModel().requestMessages(true);
            }
        });

        initData();

        mRecycleviewExposureUtils = new RecycleviewExposureUtils(getDataBinding().recyclerView, new RecycleviewExposureUtils.IExposure() {
            @Override
            public void onExpose(int index) {
                if (isPause) return;
                if (adapter != null) {
                    List<ChatMessage> items = adapter.getData();
                    ChatMessage item = LList.getElement(items, index);
                    if (item instanceof MessageForActivityMedia && !item.isExp) {
                        MessageForActivityMedia i = (MessageForActivityMedia) item;
                        item.isExp = true;
                        if (i.getOgActivityMedia() != null) {
                            reportPoint("official-activities-card-expo", new Function1<PointBean, Unit>() {
                                @Override
                                public Unit invoke(PointBean pointBean) {
                                    pointBean.setActionp2(i.getOgActivityMedia().getAdminPushId() + "");
                                    pointBean.setActionp3(i.getOgActivityMedia().getPushType() + "");
                                    return null;
                                }
                            });
                        }

                    }
                }
            }
        });

        if (getViewModel().isMeetingPlan()) {
            handlerChatMeetingPlan(getDataBinding(), requireContext());
        }
    }

    @Override
    public void onChanged(@Nullable HiMessage hiMessage) {
        getViewModel().requestPlanCard();

        if (hiMessage == null) {
            hiMessage = new HiMessage(new ArrayList<>(), false, true);
        }
        List<ChatMessage> messageList = hiMessage.getChatMessages();
        if (messageList == null) {
            messageList = new ArrayList<>();
        } else {
            if (getViewModel().isOfficialSystemActivity()) {
                messageList = GetOfficialActivityMessages.Companion.getOfficialActivityMessages(messageList);
            }
        }

        int size = messageList.size();
        boolean hasAddLocalCard = false;
        TLog.info(TAG, "onChanged: size = " + size + ",hasMore = " + hiMessage.hasMore() + ",lastest = " + hiMessage.isLatest());
        TLog.print("zl_log", "SingleChatFragment: onChanged(hiMessage)=%s, size=%s, isLastest()=%s", "通知刷新列表UI", size, hiMessage.isLatest());
        getDataBinding().refreshLayout.setEnableRefresh(hiMessage.hasMore());
        if (size == 0) {
            hiMessage.setLatest(true);
            getViewModel().setHasLastestMessage(true);
            hasAddLocalCard = addLocalMessage(hiMessage, messageList);
            adapter.submitList(messageList);
            if (mRecycleviewExposureUtils != null) {
                mRecycleviewExposureUtils.doFirstTrace();
            }
            getDataBinding().refreshLayout.setEnableLoadMore(false);
            return;
        }

        hasMoreMessage = hiMessage.hasMore();
        if (!LList.isEmpty(messageList)) { //初次初始化时，hiMessage = new HiMessage();传递的lastest为false
            if (!hiMessage.isLatest()) {
                hiMessage.setLatest(messageList.get(messageList.size() - 1).getSeq() >= ServiceManager.getInstance().getMessageService().getMaxShowSeq(getViewModel().getChatId(), MessageConstants.MSG_SINGLE_CHAT));
            }
        } else {
            hiMessage.setLatest(true);
        }

        for (int i = 0; i < messageList.size(); i++) {
            if (messageList instanceof MessageForHint) {
                TLog.print(TAG, "onChange %s:%s", i, messageList.get(i).getExtStr());
            }
        }
        getViewModel().setHasLastestMessage(hiMessage.isLatest());
        hasAddLocalCard = addLocalMessage(hiMessage, messageList);

        prepareAnimatingTacitTestCard(messageList);

        adapter.submitList(messageList);
        if (mRecycleviewExposureUtils != null) {
            mRecycleviewExposureUtils.doFirstTrace();
        }

        if (size > 0) {
            getViewModel().updateRead();
        }

        if (hiMessage.isLatest()) {
            getDataBinding().refreshLayout.setEnableLoadMore(false);
        } else {
            getDataBinding().refreshLayout.setEnableLoadMore(true);
        }
        if (hasAddLocalCard) {
            getDataBinding().recyclerView.post(new Runnable() {
                @Override
                public void run() {
                    chatItemExposePerformance.firstExpose();
                }
            });
        }
    }

    boolean isPause;

    @Override
    public void onResume() {
        super.onResume();
        isPause = false;
        getViewModel().getMessage().observe(this, this);
    }

    @Override
    public void onPause() {
        super.onPause();
        isPause = true;
        getViewModel().getMessage().removeObserver(this);
    }

    /**
     * 添加本地构造的消息
     */
    private boolean addLocalMessage(@Nullable HiMessage hiMessage, List<ChatMessage> messageList) {
        boolean addedUserCard = addHeaderUserCard(hiMessage, messageList);
        boolean addedMomentRecommendCard = addDynamicRecommendCard(hiMessage, messageList);
        // 若同时满足触发动态更新卡片和聊天话题卡片，展示优先级：动态更新卡片>聊天话题卡片
        boolean addedTopicHintCard = !addedMomentRecommendCard && addRecommendCard(hiMessage, messageList);
        return addedUserCard || (addedMomentRecommendCard || addedTopicHintCard);
    }

    private boolean addHeaderUserCard(@Nullable HiMessage hiMessage, List<ChatMessage> messageList) {
        boolean add = false;
        if (hiMessage == null) {
            add = true;
        } else {
            add = !hiMessage.hasMore();
        }
        if (messageList.isEmpty()) {
            add = true;
        }
        if (!getViewModel().isSysChatType() && getViewModel().getHeadUserCard().getChatUserCard() != null
                && add) {
            messageList.add(0, getViewModel().getHeadUserCard());
            getViewModel().getUserCardAdded().set(true);
        } else {
            getViewModel().getUserCardAdded().set(false);
            add = false;
        }
        return add;
    }

    private boolean addRecommendCard(@Nullable HiMessage hiMessage, List<ChatMessage> messageList) {
        if (chatTopicCardRecommendPerformance.needShowTopicCard(messageList)) {
            messageList.add(chatTopicCardRecommendPerformance.getTopicCardAsMessage());
            return true;
        }
        return false;
    }

    private boolean addDynamicRecommendCard(@Nullable HiMessage hiMessage, List<ChatMessage> messageList) {
        try {
            if (chatMomentRecommendPerformance.needShowMomentRecommend() && !messageList.isEmpty()) {
                MessageForCommonCard card = chatMomentRecommendPerformance.getMomentRecommendCardAsMessage();
                if (card == null) {
                    return false;
                }

                int size = messageList.size();
                if (card.getSeq() == 0) {
                    card.setSeq(messageList.get(size - 1).getSeq());
                }

                int index = -1;
                for (int i = size - 1; i >= 0; i--) {
                    if (messageList.get(i).getSeq() <= card.getSeq()) {
                        index = i + 1;
                        break;
                    }
                }

                if (index >= 0) {
                    if (index < size) {
                        messageList.add(index, card);
                    } else {
                        messageList.add(card);
                    }

                    if (!chatMomentRecommendPerformance.hasExposed()) {
                        reportPoint("chat-dynamics-update-expo", pointBean -> null);
                        chatMomentRecommendPerformance.setHasExposed(true);
                    }
                    return true;
                }

                return false;
            }
            return false;
        } catch (Throwable e) {
            TLog.error(TAG, "addDynamicRecommendCard: " + e);
            return false;
        }
    }

    private void initChatPerformance() {
        //底部输入框相关逻辑
        chatBottomLayoutPerformance = new ChatBottomLayoutPerformance(requireActivity(), this, getDataBinding(), getViewModel());
        performManager.addPerformance(chatBottomLayoutPerformance);

        //推荐话题卡片相关逻辑
        chatTopicCardRecommendPerformance = new ChatTopicCardRecommendPerformance(getViewModel(), this, getDataBinding());
        performManager.addPerformance(chatTopicCardRecommendPerformance);

        //动态推荐相关逻辑
        chatMomentRecommendPerformance = new ChatMomentRecommendPerformance(getViewModel(), this);
        performManager.addPerformance(chatMomentRecommendPerformance);

        chatRestrictPerformance = new ChatRestrictPerformance(getViewModel(), this);
        performManager.addPerformance(chatRestrictPerformance);

        //推荐表情相关逻辑
        performManager.addPerformance(new ChatRecommendEmotionPerformance(this, (AllBaseActivity) activity, getViewModel(), getDataBinding()));

        //各类事件监听的封装
        chatEventRegister = new ChatEventRegister(this, getViewModel(), getDataBinding());
        performManager.addPerformance(chatEventRegister);

        performManager.addPerformance(new ChatDialogPerformance(getViewModel(), this));

        performManager.onCustom();
    }

    @Override
    public void smoothToBottom() {
        getContentRecyclerView().postDelayed(new Runnable() {
            @Override
            public void run() {
                getContentRecyclerView().scrollToPosition(adapter.getItemCount() - 1);

            }
        }, 100);
    }

    @Override
    protected void onRecyclerScrolled(RecyclerView recyclerView, int dx, int dy) {
        showOrHideStickyMatchInfo();
    }

    @Override
    protected void onRecyclerScrollStateChanged(RecyclerView recyclerView, int newState) {

    }

    @Override
    protected ViewDataBinding getLayoutReplyTipsBinding() {
        return getDataBinding().layoutReplyTips;
    }

    private void initData() {
        getViewModel().getContactLiveData().observe(this, new Observer<Contact>() {
            @Override
            public void onChanged(Contact contact) {
                if (contact == null) {
                    return;
                }
                getDataBinding().titleBar.setTitle(contact.getNickName());
                getDataBinding().titleBar.setImgUrl(contact.getTinyAvatar());


//                contact.setMoodIcon("https://lengjing-cdn.zhipin.com/system/public/P2b-9c9HFUa-CeiwdnzhCW-0TNdFv9OmFo9X-bNB9On6uOeNnzmo7tI1jvUYFmng_B44mHH8cecij69wO9J4-w~~.png");
//                contact.setMoodTitle("开心");
                getDataBinding().titleBar.setMoodTitle(contact.getMoodTitle());
                getDataBinding().titleBar.setMoodUrl(contact.getMoodIcon());
                TLog.debug("zl_log", "SingleChatFragment: contact onChanged()=%s", "联系人变化");

                // ******* 增加限制开聊处置
                // https://zhishu.zhipin.com/wiki/PqSFzSz0QsM
                // 通过刷新 Contact，刷新提示条
                if (!getViewModel().isSysChatType() && chatRestrictPerformance != null && !TextUtils.isEmpty(chatRestrictPerformance.getRestrictChatTip())) { // 同时存在本人和对侧用户都被下发处置，上方都有红条展示时，优先展示用户本人的红条
                    getDataBinding().layoutReportTips.getRoot().setVisibility(View.VISIBLE);
                    getDataBinding().layoutReportTips.getRoot().setOnClickListener(v -> {
                        chatRestrictPerformance.addLimitChat();

                        reportPoint("function-limit-guidebar-click", pointBean -> {
                            pointBean.setPeer_id(contact.getUserId());
                            return null;
                        });
                    });
                    getViewModel().getTips().setStringContent(chatRestrictPerformance.getRestrictChatTip());

                    reportPoint("function-limit-guidebar-expo", pointBean -> {
                        pointBean.setPeer_id(contact.getUserId());
                        return null;
                    });
                } else if (contact.getStatus() == Constants.CONTACT_STATUS_BAN) {
                    getDataBinding().layoutReportTips.getRoot().setVisibility(View.VISIBLE);
                    getViewModel().getTips().setStringContent(getResources().getString(R.string.chat_report_tip));
                } else if (contact.getProfileLocked() == Account.PROFILE_LOCKED) {
                    getDataBinding().layoutReportTips.getRoot().setVisibility(View.VISIBLE);
                    getViewModel().getTips().setStringContent(getResources().getString(R.string.chat_profile_locked));
                } else {
                    getDataBinding().layoutReportTips.getRoot().setVisibility(View.GONE);
                }

                if (getViewModel().isSysChatType()) {
                    getDataBinding().titleBar.setRight(false);
                    getDataBinding().llFooter.setVisibility(View.GONE);
                    getDataBinding().titleBar.tvOfficial.setVisibility(View.VISIBLE);
                    int bottom = QMUIDisplayHelper.dp2px(activity, 80);
                    getContentRecyclerView().setPadding(0, 0, 0, bottom);
                    getContentRecyclerView().setClipToPadding(false);

                    getDataBinding().llHeader.setVisibility(View.GONE);
                } else {
                    initUserStatus(contact.getStatus());
                }
            }
        });
        ServiceManager.getInstance().getProfileService().getUserLiveData().observe(this, new Observer<User>() {
            @Override
            public void onChanged(User user) {
                if (user != null) {
                    boolean locked = user.getProfileLocked() == Account.PROFILE_LOCKED;
                    getViewModel().getUserLocked().set(locked);
                    if (locked) {
                        QMUIKeyboardHelper.hideKeyboard(getDataBinding().vgChatInput.etInput);
                        clickCloseReply();
                    }
                    //根据是否完成新手任务，阻断
                    getDataBinding().vgChatInput.vClickBlock.setVisibility(getViewModel().isFinishEduOrCompanyAuth() ? View.GONE : View.VISIBLE);
                }
            }
        });
        getViewModel().uerInfoResult.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    getDataBinding().setViewModel(getViewModel());
                    showOrHideStickyMatchInfo();
                }
            }
        });
        getViewModel().protectMeetChatCard.observe(this, new Observer<ProtectMeetChatCard>() {
            @Override
            public void onChanged(ProtectMeetChatCard protectMeetChatCard) {
                if (protectMeetChatCard != null) {
                    int style = 0;
                    if (protectMeetChatCard.getSecurityGuardian() != null && protectMeetChatCard.getSecurityGuardian().getOpenStatus() == 1 && protectMeetChatCard.getSecurityGuardian().getCount() > 0) {
                        style = 1;
                    }

                    int count = protectMeetChatCard.getSecurityGuardian() == null ? 0 : protectMeetChatCard.getSecurityGuardian().getCount();
                    String btnStr = LDate.getMeetingData(protectMeetChatCard.getMeetTime()) + "," + protectMeetChatCard.getAddress();

                    getDataBinding().idChatTopMoveView.setVisibility(View.VISIBLE);
                    if (getDataBinding().idChatTopMoveView.isShowing()) {
//                        MeetingPlanProtectNotificationManager.getInstance(getContext()).updateView(btnStr, style, count);
                        getDataBinding().idChatTopMoveView.updateView( btnStr, style, count);
                        int finalStyle = style;
                        PointHelperKt.reportPoint("O2-seeyou-safeseecard-show", new Function1<PointBean, Unit>() {
                            @Override
                            public Unit invoke(PointBean pointBean) {
                                pointBean.setActionp2(finalStyle == 0  ? "未设置" : "已设置");
                                pointBean.setPeer_id(getViewModel().getChatId());
                                pointBean.setActionp3(btnStr);
                                return null;
                            }
                        });
                    } else {
//                        MeetingPlanProtectNotificationManager.getInstance(getContext()).initData(SingleChatActivity.class, btnStr, style, count, protectMeetChatCard.getRecordId()).show();
                        getDataBinding().idChatTopMoveView.show( btnStr, style, count, protectMeetChatCard.getRecordId(), getViewModel().getChatId());
                        int finalStyle = style;
                        PointHelperKt.reportPoint("O2-seeyou-safeseecard-show", new Function1<PointBean, Unit>() {
                            @Override
                            public Unit invoke(PointBean pointBean) {
                                pointBean.setActionp2(finalStyle == 0  ? "未设置" : "已设置");
                                pointBean.setPeer_id(getViewModel().getChatId());
                                pointBean.setActionp3(btnStr);
                                return null;
                            }
                        });
                    }
                } else {
                    getDataBinding().idChatTopMoveView.setVisibility(View.GONE);
//                    MeetingPlanProtectNotificationManager.getInstance(getContext()).stop();
                }
            }
        });
        getViewModel().mMeetupChatPlanCardResponse.observe(this, meetupChatPlanCardResponse -> {
            if (getDataBinding().layoutReportTips.getRoot().getVisibility() == View.VISIBLE) {
                getDataBinding().composeViewMeetingPlanTip.setVisibility(View.GONE);
            } else {
                ConstraintLayout.LayoutParams layoutParams = (ConstraintLayout.LayoutParams) getDataBinding().titleBar.tvName.getLayoutParams();
                int marginHorizontal = meetupChatPlanCardResponse != null ? QMUIDisplayHelper.dp2px(activity, 132) : 100;
                layoutParams.setMarginStart(marginHorizontal);
                layoutParams.setMarginEnd(marginHorizontal);
                getDataBinding().titleBar.tvName.setLayoutParams(layoutParams);

                bindMeetingPlanView(
                        getDataBinding().composeViewMeetingPlanTip,
                        getDataBinding().titleBar.composeViewDaysSpent,
                        meetupChatPlanCardResponse,
                        getViewModel(),
                        (taskType, msgId) -> {
                            scrollToTaskCardMessage(SingleChatFragment.this, taskType, msgId);
                            return Unit.INSTANCE;
                        }
                );
            }
        });
        getViewModel().getChatUserInfo().observe(this, chatUserInfo -> {
            // 新增需求：若当前用户被限制开聊，则不展示活动条
            if (getDataBinding().layoutReportTips.getRoot().getVisibility() == View.VISIBLE) {
                getDataBinding().composeViewCoeventTip.setVisibility(View.GONE);
            } else {
                bindSameActivityView(getDataBinding().composeViewCoeventTip, chatUserInfo, getViewModel());
            }
        });
        getViewModel().requestUserInfo();
        getViewModel().requestPlanCard();

    }

    /**
     * Contact 用户状态变化后修改
     */
    private void initUserStatus(int status) {
        if (getViewModel().getStatus() == status) {
            return;
        }
        getViewModel().setStatus(status);
        if (getViewModel().getStatus() == Constants.CONTACT_STATUS_BAN) {
            getDataBinding().layoutReportTips.getRoot().setVisibility(View.VISIBLE);
        }
        getDataBinding().titleBar.setRight(true);
        if (getViewModel().getStatus() == Constants.CONTACT_STATUS_DELETED) {
            //已注销
            getViewModel().setIsFriend(false);
            getDataBinding().tvNoSendTips.setText(R.string.chat_user_has_deleted_tips);
            Conversation conversation = getViewModel().getConversation();
            if (conversation != null) {
                if (conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_MY_LIKE
                        || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_LIKE_YOU
                        || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_HISTORY
                        || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_DELETE) {
                    getDataBinding().titleBar.setRight(false);
                }
            } else {
                getDataBinding().titleBar.setRight(false);
            }
        }
    }

    @Override
    protected void onConversationChanged(@NonNull Conversation conversation) {
        super.onConversationChanged(conversation);
        if (conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_HISTORY) {//解除好友关系
            getViewModel().setIsFriend(false);
            getDataBinding().tvNoSendTips.setText(R.string.chat_user_has_deleted_friend_tips);
        } else if (conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_MY_LIKE) {//单方面喜欢
            getViewModel().setIsFriend(true);
            getDataBinding().tvNoSendTips.setText(R.string.chat_user_not_friend_tips);
        }
        if (getViewModel().isSysChatType() || getViewModel().isMeetingPlan()) {
            getViewModel().icon.set("");
            getDataBinding().titleBar.ivLoveStatus.setVisibility(View.GONE);
        } else {
            if (conversation.getIconShowType() == 1 || conversation.getIconShowType() == 2) {
                getViewModel().icon.set(conversation.getIcon());
            } else {
                getViewModel().icon.set("");
                getDataBinding().titleBar.ivLoveStatus.setVisibility(View.GONE);
            }

        }

        getDataBinding().titleBar.setMoodTitle(conversation.getMoodTitle());
        getDataBinding().titleBar.setMoodUrl(conversation.getMoodIcon());

    }

    @Override
    protected void setUserStatusUI(Conversation conversation) {
        Conversation conversation1 = getViewModel().getConversation();
        if (conversation1 != null && conversation1.getRelationStatus() == conversation.getRelationStatus()) {
            return;
        }
        if (getViewModel().isSysChatType()) {
            return;
        }
        getDataBinding().titleBar.setRight(true);
        if (getViewModel().getStatus() == Constants.CONTACT_STATUS_DELETED) {
            //已注销
            if (conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_MY_LIKE
                    || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_LIKE_YOU
                    || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_HISTORY
                    || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_DELETE) {
                getDataBinding().titleBar.setRight(false);
            }
        }
    }

    @Override
    protected void onMessageSendSuccess() {
        chatTopicCardRecommendPerformance.unableShowTopicCard();
        hideBottomEmotionTips();
    }

    /**
     * 隐藏底部表情提示
     */
    private void hideBottomEmotionTips() {
        if (getViewModel().getShowEmotionTips().get()) {
            getViewModel().getShowEmotionTips().set(false);
        }
    }

    protected void onlySmoothToBottom(int delay) {
        getDataBinding().recyclerView.postDelayed(new Runnable() {
            @Override
            public void run() {
                getDataBinding().recyclerView.scrollToPosition(adapter.getItemCount() - 1);

            }
        }, delay);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mRecycleviewExposureUtils.clearRunnable();
        ServiceManager.getInstance().getContactService().unregisterContactChangeListener(this);
//        if( MeetingPlanProtectNotificationManager.getInstance(getContext()).isShowing()){
//        MeetingPlanProtectNotificationManager.getInstance(getContext()).stop();
//        }
//        handler.removeCallbacksAndMessages(null);
    }

    @Override
    public void clickMessageUrlParse(MessageForText messageForText) {

    }

    @Override
    public void onReplyClick(View view, ChatMessage chatMessage) {

    }

    @Override
    public void onReWrite(ChatMessage chatMessage) {
        ExecutorFactory.execLocalTask(new Runnable() {
            @Override
            public void run() {
                ChatMessage message = ServiceManager.getInstance().getMessageService().getChatMessage(-chatMessage.getMid());
                ExecutorFactory.execMainTask(new Runnable() {
                    @Override
                    public void run() {
                        smoothToBottom();
                        if (message instanceof MessageForText) {
                            int index = getDataBinding().vgChatInput.etInput.getSelectionStart();
                            Editable editable = getDataBinding().vgChatInput.etInput.getText();
                            editable.insert(index, message.getContent());
                        }
                    }
                });
            }
        });
    }

    @Override
    public void clickAvatar(String sender, String securityId) {
        jumpUserDetail(sender, securityId);
    }

    @Override
    public void onChatClickOptionItemClick(View view, ChatClickOptionBean chatClickOptionBean) {

    }

    @Override
    public void clickToInfoEdit() {
        MePageRouter.jumpToMeInfoEditActivity(activity, "");
    }

    private void jumpUserDetail(String sender, String securityId) {
        if (pageSource == PageSource.F2_LIKE_ME_CHILD_FRAGMENT) {
            MePageRouter.jumpToInfoPreviewActivity(activity, sender, "", PageSource.CHAT, "", "", "", securityId, "", true);
        } else {
            MePageRouter.jumpToInfoPreviewActivity(activity, sender, "", PageSource.CHAT, "", "", "", securityId, "", false);
        }
    }

    @Override
    public void clickBlock() {
        MePageRouter.showNoviceChatBlockDialog(getActivity(), PageSource.CHAT, userId);
    }

    @Override
    public void onLinkCall(View view, MessageForLinkCall messageForLinkCall) {
        if (getViewModel().userProfileLocked()) {
            return;
        }
        if (getViewModel().contactLocked()) {
            T.ss(R.string.common_contact_video_chat_notice);
        }
        if (getViewModel().isShowVoiceItem() && VideoAudioFloatPageManager.getInstance().hasNoFloatPage()) {
            IMeetingService iMeetingService = Router.getService(IMeetingService.class, MediaConstants.ENGINE_LISTENER_KEY);
            iMeetingService.postLocalOverTimeMessage();
            if (iMeetingService.alive()) {
                return;
            }
            iMeetingService.setAlive(true);
            Intent intent = Navigation.getVideoChatActivity(activity);
            intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_TYPE, messageForLinkCall.getLinkType());
            intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_INVITATION, String.valueOf(getViewModel().getChatId()));
            AppUtil.startActivity(activity, intent);
        }
    }

    @Override
    public void clickDateCard(View view, MessageForDateCard messageForDateCard) {
        if (messageForDateCard.getDateCardInfo().getStatus() != 40) {// 过期不可点
            ChatPageRouter.jumpToDateCardDetailActivity(activity, String.valueOf(messageForDateCard.getMid()), messageForDateCard.getSender());
        }
    }

    @Override
    public void clickLoveCard(View view, MessageForLoveCard messageForLoveCard) {
        if (messageForLoveCard.getLoveCardInfo().getReplyStatus() != 40) {// 过期不可点
            ChatPageRouter.jumpToLoveCardDetailActivity(activity, String.valueOf(messageForLoveCard.getMid()), messageForLoveCard.getSender());
        }
    }

    @Override
    public void onLikeCardClick(View view, MessageForLike messageForLike) {
        if (true) { // 组件不需要支持交互，静态展示即可
            return;
        }

        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        View contentView = null;
        switch (messageForLike.getResourceType()) {
            case Constants.TYPE_LIKE_AVATAR: {
                ChatLayoutLikeAvatarDetailDialogBinding binding = ChatLayoutLikeAvatarDetailDialogBinding.inflate(
                        LayoutInflater.from(activity.getApplicationContext()), (ViewGroup) getDataBinding().getRoot(), false);
                binding.setLikeAvatar(messageForLike.getLikeInfo().likeAvatar);
                boolean isFriend = !MessageUtils.isAuthor(messageForLike);
                String userId;
                if (isFriend) {
                    userId = AccountHelper.getInstance().getUserId();
                } else {
                    userId = messageForLike.getChatId();
                }
                ChatBindingAdapter.setNickName(binding.tvNick, userId);

                String liveVideo = messageForLike.getLikeInfo().likeAvatar.liveVideo;
                if (!TextUtils.isEmpty(liveVideo)) {
                    String playVideoUrl = StringUtil.getPlayVideoUrl(liveVideo);
                    //表示是视频封面
                    messageForLike.oPlayerHelper = new OPlayerHelper(Utils.getApp(), new OPlayerHelper.CallBack() {
                        @Override
                        public void onFirstFrame() {
                            binding.ivAvatar.setVisibility(View.GONE);
                        }
                    });
                    messageForLike.oPlayerHelper.setLooper(true);
                    messageForLike.oPlayerHelper.setPlayView(binding.svVideo);
                    messageForLike.oPlayerHelper.setMute(true);
                    messageForLike.oPlayerHelper.setPlayUrl(playVideoUrl);
                    messageForLike.oPlayerHelper.start();
                }
                contentView = binding.getRoot();
            }
            break;
            case Constants.TYPE_LIKE_ABOUT_ME_PIC:
            case Constants.TYPE_LIKE_HOBBY_PIC:
            case Constants.TYPE_LIKE_STORY: {
                if (TextUtils.isEmpty(messageForLike.getLikeInfo().story.video)) {
                    ChatLayoutLikePhotoStoryDetailDialogBinding binding = ChatLayoutLikePhotoStoryDetailDialogBinding.inflate(LayoutInflater.from(activity.getApplicationContext()),
                            (ViewGroup) getDataBinding().getRoot(), false);
                    binding.setItem(messageForLike.getLikeInfo().story);
                    contentView = binding.getRoot();
                } else {
                    ChatLayoutLikeVideoStoryDialogBinding binding = ChatLayoutLikeVideoStoryDialogBinding.inflate(LayoutInflater.from(activity.getApplicationContext()),
                            (ViewGroup) getDataBinding().getRoot(), false);
                    //视频故事
                    VideoStoryBean storyBean = ModelCopyUtil.VideoStoryBeanCopy(messageForLike.getLikeInfo().story);
                    storyBean.video = StringUtil.getPlayVideoUrl(storyBean.video);
                    getViewModel().likeVideoStoryBean = storyBean;
                    binding.setItem(storyBean);

                    storyBean.oPlayerHelper = new OPlayerHelper(Utils.getApp(), new OPlayerHelper.CallBack() {
                        @Override
                        public void onFirstFrame() {
                            binding.ivPhoto.setVisibility(View.GONE);
                        }
                    });
                    storyBean.oPlayerHelper.setLooper(true);
                    storyBean.oPlayerHelper.startCache(storyBean.video);
                    storyBean.oPlayerHelper.setPlayView(binding.svVideo);

                    storyBean.oPlayerHelper.setMute(storyBean.silence.get());
                    storyBean.oPlayerHelper.setPlayUrl(storyBean.video);
                    storyBean.oPlayerHelper.start();

                    binding.ivVoice.setOnClickListener(v -> {
                        OPlayerHelper player = storyBean.oPlayerHelper;
                        if (player != null) {
                            boolean newVoiceStatus = !storyBean.silence.get();
                            storyBean.silence.set(newVoiceStatus);
                            storyBean.oPlayerHelper.setMute(newVoiceStatus);
                        }
                    });

                    contentView = binding.getRoot();
                }

            }
            break;
            case Constants.TYPE_LIKE_VOICE:
                getViewModel().onPlayAudio(messageForLike).observe(this, status -> {
                    messageForLike.setVoiceStatus(status);
                    switch (status) {
                        case MessageForAudio.STATUS_VOICE_PLAYING:
                            View llVoice = view.findViewById(R.id.ll_voice);
                            if (llVoice instanceof RelativeLayout) {
                                mPlayWrapper.setMessageForAudio(messageForLike, status, (RelativeLayout) llVoice);
                            }
                            break;
                        case MessageForAudio.STATUS_VOICE_PAUSE:
                            mPlayWrapper.setPlay(status);
                            break;
                        case MessageForAudio.STATUS_VOICE_START:
                            messageForLike.setReadPercent(0.0f);
                            mPlayWrapper.setPlay(status);
                            break;
                        default:
                            break;
                    }
                });
                break;
            case Constants.TYPE_LIKE_TEXT: {
                ChatLayoutLikeTextAnswerDetailDialogBinding binding = ChatLayoutLikeTextAnswerDetailDialogBinding.inflate(LayoutInflater.from(activity.getApplicationContext()),
                        (ViewGroup) getDataBinding().getRoot(), false);
                binding.setItem(messageForLike.getLikeInfo().answer);
                contentView = binding.getRoot();
            }
            break;
            case Constants.TYPE_LIKE_A_B_FACE: {
                ChatLayoutLikeAbFaceDetailDialogBinding binding = ChatLayoutLikeAbFaceDetailDialogBinding.inflate(LayoutInflater.from(activity.getApplicationContext()),
                        (ViewGroup) getDataBinding().getRoot(), false);
                binding.faceView.setData(messageForLike.getLikeInfo().abFace);
                contentView = binding.getRoot();
                break;
            }
            default:
                break;
        }
        if (contentView != null) {
            showLikeContentDetailDialog(contentView, messageForLike);
        }
    }

    @Override
    public void clickOfficialActivity(View view, MessageForActivityMedia messageForOgActivityMedia) {

    }

    @Override
    public void clickMood(View view, MessageForMood messageForMood) {

    }

    private void showLikeContentDetailDialog(View contentView, MessageForLike messageForLike) {
        CommonLayoutDialog dialog = new CommonLayoutDialog.Builder(activity)
                .setContentView(contentView)
                .setCancelable(true)
                .setCanceledOnTouchOutside(true)
                .createDialog();
        dialog.show();
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (messageForLike.getResourceType() == Constants.TYPE_LIKE_STORY) {
                    if (!TextUtils.isEmpty(messageForLike.getLikeInfo().story.video)) {
                        if (getViewModel().likeVideoStoryBean != null) {
                            getViewModel().likeVideoStoryBean.oPlayerHelper.onDestroy();
                        }
                    }
                } else if (messageForLike.getResourceType() == Constants.TYPE_LIKE_AVATAR) {
                    if (messageForLike.oPlayerHelper != null) {
                        messageForLike.oPlayerHelper.onDestroy();
                    }
                }
            }
        });
    }

    @Override
    public void onContactChange(String userId) {
        getViewModel().refreshContact();
    }

    @Override
    public void onChange() {

    }

    @Override
    public void onItemRangeInserted() {
        List<ChatMessage> messageList = adapter.getData();
        int size = messageList.size();
        if (size > 0 && getDataBinding() != null) {
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) getContentRecyclerView().getLayoutManager();
            if (linearLayoutManager.findLastVisibleItemPosition() == -1 || linearLayoutManager.findLastVisibleItemPosition() >= (size - 5)) {
                if (!isChatOptionShow) {
                    smoothToBottom();
                }
            }
        }
    }

    @Override
    protected void onScrollIdle() {
        super.onScrollIdle();
        hidePanelAndKeyboard();
        //item曝光埋点
        chatItemExposePerformance.onExpose(false);
    }

    /**
     * 显示或隐藏置顶的匹配信息
     */
    private void showOrHideStickyMatchInfo() {
        RecyclerView.LayoutManager layoutManager = getDataBinding().recyclerView.getLayoutManager();
        if (layoutManager != null && getViewModel().getNeedShowPinInfo().get()) {
            int firstVisibleItemPosition = ((LinearLayoutManager) layoutManager).findFirstVisibleItemPosition();
            if (firstVisibleItemPosition == 0) {
                getViewModel().getCanShowPinInfo().set(false);
            } else {
                getViewModel().getCanShowPinInfo().set(true);
            }
        }
    }

    @Override
    public void hidePanelAndKeyboard() {
        super.hidePanelAndKeyboard();
        chatBottomLayoutPerformance.hidePanelAndKeyboard();
    }

    public boolean onBackPress() {
        return chatBottomLayoutPerformance.hookOnBackPressed();
    }

    @Override
    public void clickLoveIcon(View v) {
        ChatPointReporter.Companion.reportChatRelationshipLevelClick(getViewModel().getChatId(), getViewModel().icon.get());
        new LoveStatusPopup(v.getContext(), v).show();
    }

    @Override
    public void clickTitle(View view) {
        if (!getViewModel().isSysChatType()) {
            ChatPointReporter.Companion.reportChatUserDetailClick("顶部昵称", getViewModel().getChatId());
            jumpUserDetail(getViewModel().getChatId(), getViewModel().getSecurityId());
//            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onTopicGameSendSuccess() {
        onMessageSendSuccess();
    }

    @Override
    public void onMomentRecommendCardClick(View view, MessageForCommonCard messageForCommonCard) {
        CommonCardInfo commonCardInfo = messageForCommonCard.getCommonCardInfo();

        String userId = null;
        if (commonCardInfo != null) {
            String extend = commonCardInfo.getExtend();
            if (extend != null) {
                MomentListItemBean bean = GsonUtils.fromJson(extend, MomentListItemBean.class);
                if (bean != null) {
                    userId = bean.userId;
                }
            }
        }

        if (userId != null) {
            // 点击整个模块跳转对方的动态主页
            SocialPageRouter.jumpToUserDynamicActivity(view.getContext(), userId);

            chatMomentRecommendPerformance.unableShowMoment();

            reportPoint("chat-dynamics-update-click", pointBean -> null);
        }
    }

    @NonNull
    public ChatAdapter getChatAdapter() {
        return adapter;
    }
}
