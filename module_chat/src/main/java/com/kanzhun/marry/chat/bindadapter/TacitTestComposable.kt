package com.kanzhun.marry.chat.bindadapter

import androidx.activity.ComponentActivity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandIn
import androidx.compose.animation.fadeIn
import androidx.compose.animation.scaleIn
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.attachOwners
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.message.ChatMessage
import com.kanzhun.foundation.model.message.MessageConstants
import com.kanzhun.foundation.model.message.MessageForTacitTestCard
import com.kanzhun.foundation.model.message.TYPE_TACIT_TEST_CARD_LAUNCH
import com.kanzhun.foundation.model.message.TYPE_TACIT_TEST_CARD_RESULT
import com.kanzhun.foundation.model.message.TYPE_TACIT_TEST_CARD_UNLOCK
import com.kanzhun.foundation.model.message.TacitTestCard
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.MessageUtils
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.viewmodel.SingleChatViewModel
import com.kanzhun.utils.T
import kotlinx.coroutines.delay

private fun inAnimating(tacitTestCard: TacitTestCard): Boolean {
    return tacitTestCard.inAnimating
}

//region Binder
fun bindTacitTest(
    composeView: ComposeView,
    message: MessageForTacitTestCard,
) {
    composeView.apply {
        onSetWindowContent {
            val singleChatViewModel = viewModel<SingleChatViewModel>()
            val contact by singleChatViewModel.contactLiveData.observeAsState(Contact().apply {})
            val conversation by ServiceManager.getInstance().getConversationService()
                .findByUid(message.chatId, MessageConstants.MSG_SINGLE_CHAT)
                .observeAsState(Conversation())
            TacitTestCard(
                conversation = conversation,
                contact = contact,
                message = message,
                isFriend = !MessageUtils.isAuthor(message)
            )
        }

        val activity = context as? ComponentActivity
        activity?.let { owner ->
            attachOwners(owner = owner, targetView = composeView)
        }
    }
}

fun prepareAnimatingTacitTestCard(messageList: List<ChatMessage>) {
    for (message in messageList.reversed()) {
        if (message is MessageForTacitTestCard) {
            val tacitTestCard = message.tacitTestCard
            tacitTestCard?.inAnimating = true
            break
        }
    }
}
//endregion

//region Composable
@Composable
fun TacitTestCard(
    conversation: Conversation = Conversation(),
    contact: Contact? = Contact().apply {},
    message: MessageForTacitTestCard,
    isFriend: Boolean = false,
    inPreview: Boolean = false
) {
    val tacitTestCard = message.tacitTestCard

    // 该卡片动画播放完毕，清除标记位
//    LaunchedEffect(Unit) {
//        delay(3000)
//        if (tacitTestCard?.isAnimating == true) {
//            tacitTestCard.isAnimating = false
//        }
//    }

    if (tacitTestCard?.cardType == TYPE_TACIT_TEST_CARD_UNLOCK) {
        TacitTestUnlockCard(
            conversation = conversation,
            contact = contact,
            card = tacitTestCard,
            inPreview = inPreview
        )
    } else if (tacitTestCard?.cardType == TYPE_TACIT_TEST_CARD_LAUNCH) {
        val context = LocalContext.current
        if (isFriend) {
            TacitTestLaunchFromCard(
                conversation = conversation,
                contact = contact,
                card = tacitTestCard,
                inPreview = inPreview,
                onClickAvatar = {
                    MePageRouter.jumpToInfoPreviewActivity(
                        context = context as FragmentActivity,
                        userId = message.sender,
                        lid = "",
                        pageSource = PageSource.CHAT,
                        thirdId = "",
                        thirdType = "",
                        sourceType = "",
                        securityId = contact?.securityId ?: "",
                        protocolFrom = "",
                        sendLikedToFinish = false
                    )
                }
            )
        } else {
            TacitTestLaunchToCard(
                conversation = conversation,
                contact = contact,
                card = tacitTestCard,
                inPreview = inPreview,
                onClickAvatar = {
                    MePageRouter.jumpToInfoPreviewActivity(
                        context = context as FragmentActivity,
                        userId = message.sender,
                        lid = "",
                        pageSource = PageSource.CHAT,
                        thirdId = "",
                        thirdType = "",
                        sourceType = "",
                        securityId = contact?.securityId ?: "",
                        protocolFrom = "",
                        sendLikedToFinish = false
                    )
                }
            )
        }
    } else if (tacitTestCard?.cardType == TYPE_TACIT_TEST_CARD_RESULT) {
        TacitTestResultCard(
            conversation = conversation,
            contact = contact,
            card = tacitTestCard,
            inPreview = inPreview
        )
    }
}

@Composable
private fun TacitTestUnlockCard(
    conversation: Conversation = Conversation(),
    contact: Contact?,
    card: TacitTestCard,
    inPreview: Boolean = false
) {
    ConstraintLayout(
        modifier = Modifier
            .wrapContentWidth()
            .width(width = 311.dp)
            .noRippleClickable {
                openH5(conversation, card.firstJumpUrl)
            }
    ) {
        val (bg, titleBg, title, desc, avatars, btnLight, gloveBg, glove) = createRefs()

        Image(
            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_unlock_bg),
            contentDescription = "image description",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.constrainAs(bg) {
                start.linkTo(parent.start)
                end.linkTo(parent.end, margin = 8.dp)
                top.linkTo(parent.top, margin = 26.dp)
                bottom.linkTo(parent.bottom)

                width = Dimension.fillToConstraints
                height = Dimension.fillToConstraints
            }
        )

        if (inPreview) {
            Image(
                painter = painterResource(id = R.mipmap.chat_ic_tacit_test_unlock_title),
                contentDescription = "image description",
                modifier = Modifier
                    .size(width = 184.dp, height = 66.dp)
                    .constrainAs(titleBg) {
                        start.linkTo(anchor = bg.start, margin = 8.dp)
                        top.linkTo(parent.top)
                    }
            )
        }
        val titleComposition by rememberLottieComposition(
            spec = LottieCompositionSpec.Asset(
                "tacit/unlock/title_pink.json"
            ),
        )
        val titleProgress by animateLottieCompositionAsState(composition = titleComposition)
        LottieAnimation(
            composition = titleComposition,
            progress = { if (inAnimating(card)) titleProgress else 1f },
            modifier = Modifier
                .size(width = 184.dp, height = 66.dp)
                .constrainAs(title) {
                    start.linkTo(anchor = bg.start, margin = 8.dp)
                    top.linkTo(parent.top)
                }
        )

        Text(
            text = "双方共同完成问答，看看你们的默契值有多少～",
            style = TextStyle(
                fontSize = 13.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF292929),
            ),
            modifier = Modifier.constrainAs(desc) {
                start.linkTo(bg.start, 20.dp)
                top.linkTo(title.bottom, margin = 2.dp)
                end.linkTo(glove.start, margin = 12.dp)

                width = Dimension.fillToConstraints
            }
        )

        Row(
            Modifier
                .background(color = Color(0xFF54F2BB), shape = RoundedCornerShape(size = 32.dp))
                .padding(horizontal = 4.dp, vertical = 3.dp)
                .constrainAs(avatars) {
                    top.linkTo(desc.bottom, margin = 12.dp)
                    start.linkTo(desc.start)
                    bottom.linkTo(parent.bottom, 20.dp)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 先男后女
            Box(
                modifier = Modifier.size(36.dp)
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_male_avatar),
                    contentDescription = "image description",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.size(36.dp)
                )

                OImageView2(
                    imageUrl = getMaleAvatar(contact = contact, inPreview = inPreview),
                    modifier = Modifier
                        .clip(CircleShape)
                        .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = CircleShape)
                        .size(36.dp)
                )
            }

            Box(
                modifier = Modifier
                    .offset(x = (-8).dp)
                    .size(36.dp)
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_female_avatar),
                    contentDescription = "image description",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.size(36.dp)
                )

                OImageView2(
                    imageUrl = getFemaleAvatar(contact = contact, inPreview = inPreview),
                    modifier = Modifier
                        .clip(CircleShape)
                        .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = CircleShape)
                        .size(36.dp)
                )
            }

            Text(
                text = "GO",
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(700),
                    color = Color(0xFF292929),
                ),
                modifier = Modifier.padding(horizontal = 8.dp)
            )

            Image(
                painter = painterResource(id = R.mipmap.chat_ic_tacit_test_unlock_arrow),
                contentDescription = "image description",
                modifier = Modifier.padding(end = 16.dp)
            )
        }

        val btnLightComposition by rememberLottieComposition(
            spec = LottieCompositionSpec.Asset(
                "tacit/unlock/btn_light.json"
            ),
        )
        var isBtnLightPlaying by remember { mutableStateOf(false) }
        LaunchedEffect(Unit) {
            delay(1000)
            isBtnLightPlaying = true
        }
        val btnLightProgress by animateLottieCompositionAsState(
            composition = btnLightComposition,
            isPlaying = isBtnLightPlaying,
        )
        LottieAnimation(
            composition = btnLightComposition,
            progress = { if (inAnimating(card)) btnLightProgress else 1f },
            modifier = Modifier
                .clip(shape = RoundedCornerShape(size = 32.dp))
                .constrainAs(btnLight) {
                    top.linkTo(avatars.top)
                    start.linkTo(avatars.start)
                    bottom.linkTo(avatars.bottom)
                    end.linkTo(avatars.end)

                    width = Dimension.fillToConstraints
                }
        )

        if (inPreview) {
            Image(
                painter = painterResource(id = R.mipmap.chat_ic_tacit_test_unlock_glove),
                contentDescription = "image description",
                modifier = Modifier
                    .size(140.dp)
                    .constrainAs(gloveBg) {
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = 22.dp)
                    }
            )
        }
        val paishouComposition by rememberLottieComposition(
            spec = LottieCompositionSpec.Asset(
                "tacit/unlock/paishou.json"
            ),
        )
        val paishouProgress by animateLottieCompositionAsState(paishouComposition)
        LottieAnimation(
            composition = paishouComposition,
            progress = { if (inAnimating(card)) paishouProgress else 1f },
            modifier = Modifier
                .size(140.dp)
                .constrainAs(glove) {
                    end.linkTo(parent.end)
                    top.linkTo(parent.top, margin = 22.dp)
                }
        )
    }
}

private fun openH5(conversation: Conversation, url: String?) {
    if (AccountHelper.getInstance().isUserProfileLocked) {
        T.ss(R.string.chat_user_locked_title)
        return
    }

    if (conversation.relationStatus == MessageConstants.MATCHING_STATUS_HISTORY) {
        T.ss(R.string.chat_matching_status_history_tips)
        return
    }

    ProtocolHelper.parseProtocol(url)
}

fun getMyAvatar(inPreview: Boolean = false): String =
    if (inPreview) "" else if (AccountHelper.getInstance().isLogin()) {
        val userInfo = AccountHelper.getInstance().getUserInfo()
        (userInfo?.tinyAvatar ?: "").ifEmpty { userInfo?.avatar ?: "" }
    } else {
        ""
    }

fun getFriendAvatar(contact: Contact?): String =
    (contact?.tinyAvatar ?: "").ifEmpty { contact?.avatar ?: "" }

@Composable
private fun TacitTestLaunchFromCard(
    conversation: Conversation = Conversation(),
    contact: Contact?,
    card: TacitTestCard,
    inPreview: Boolean = false,
    onClickAvatar: () -> Unit = {},
) {
    val hasFinished = card.replyStatus == 2
    TacitTestLaunchCard(
        contact = contact,
        isFriend = true,
        inPreview = inPreview,
        onClickAvatar = onClickAvatar
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .noRippleClickable {
                    if (!hasFinished) {
                        openH5(conversation, card.secondJumpUrl)
                    }
                }
        ) {
            ConstraintLayout(
                modifier = Modifier
                    .wrapContentWidth()
                    .width(width = 276.dp)
            ) {
                val (bg, hashBg, hash, starBg, star, launch, titleBg, title, desc, fromAvatar, toAvatar, toAvatarBtnLight2, arrowBg, arrow) = createRefs()

                Image(
                    painter = painterResource(id = if (hasFinished) R.mipmap.chat_ic_tacit_test_launch_bg_gray else R.mipmap.chat_ic_tacit_test_launch_bg),
                    contentDescription = "image description",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .constrainAs(bg) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            top.linkTo(parent.top, margin = 24.dp)
                            bottom.linkTo(parent.bottom)

                            width = Dimension.fillToConstraints
                            height = Dimension.fillToConstraints
                        }
                )

                if (hasFinished) {
                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_arrow_gray),
                        contentDescription = "image description",
                        modifier = Modifier
                            .size(width = 43.dp, height = 53.dp)
                            .constrainAs(arrow) {
                                start.linkTo(title.end, margin = (-28).dp)
                                top.linkTo(title.top, margin = 36.dp)
                                bottom.linkTo(title.bottom)
                            }
                    )

                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_title_gray),
                        contentDescription = "image description",
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier
                            .size(width = 166.dp, height = 66.dp)
                            .constrainAs(title) {
                                start.linkTo(anchor = launch.end)
                                bottom.linkTo(anchor = launch.bottom, margin = (-6).dp)
                            }
                    )
                } else {
                    if (inPreview) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_arrow),
                            contentDescription = "image description",
                            modifier = Modifier
                                .size(width = 43.dp, height = 53.dp)
                                .constrainAs(arrowBg) {
                                    start.linkTo(title.end, margin = (-28).dp)
                                    top.linkTo(title.top, margin = 36.dp)
                                    bottom.linkTo(title.bottom)
                                }
                        )

                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_title),
                            contentDescription = "image description",
                            contentScale = ContentScale.FillBounds,
                            modifier = Modifier
                                .size(width = 166.dp, height = 66.dp)
                                .constrainAs(titleBg) {
                                    start.linkTo(title.start)
                                    top.linkTo(title.top)
                                    end.linkTo(title.end)
                                    bottom.linkTo(title.bottom)

                                    width = Dimension.fillToConstraints
                                    height = Dimension.fillToConstraints
                                }
                        )
                    }

                    var shouldAnimateArrow by remember { mutableStateOf(false) }
                    LaunchedEffect(Unit) {
                        if (inAnimating(card)) {
                            delay(400)
                        }
                        shouldAnimateArrow = true
                    }
                    if (shouldAnimateArrow) {
                        val arrowComposition by rememberLottieComposition(
                            spec = LottieCompositionSpec.Asset(
                                "tacit/launch/pink_arrow.json"
                            ),
                        )
                        val arrowProgress by animateLottieCompositionAsState(composition = arrowComposition)
                        LottieAnimation(
                            composition = arrowComposition,
                            progress = { if (inAnimating(card)) arrowProgress else 1f },
                            modifier = Modifier
                                .size(width = 43.dp, height = 53.dp)
                                .constrainAs(arrow) {
                                    start.linkTo(title.end, margin = (-28).dp)
                                    top.linkTo(title.top, margin = 36.dp)
                                    bottom.linkTo(title.bottom)
                                }
                        )
                    }

                    val titleComposition by rememberLottieComposition(
                        spec = LottieCompositionSpec.Asset(
                            "tacit/launch/title_orange.json"
                        ),
                    )
                    val titleProgress by animateLottieCompositionAsState(composition = titleComposition)
                    LottieAnimation(
                        composition = titleComposition,
                        progress = { if (inAnimating(card)) titleProgress else 1f },
                        modifier = Modifier
                            .size(width = 166.dp, height = 66.dp)
                            .constrainAs(title) {
                                start.linkTo(anchor = launch.end)
                                bottom.linkTo(anchor = launch.bottom, margin = (-6).dp)
                            }
                    )
                }

                Image(
                    painter = painterResource(id = if (hasFinished) R.mipmap.chat_ic_tacit_test_launch_me_gray else R.mipmap.chat_ic_tacit_test_launch_me),
                    contentDescription = "image description",
                    modifier = Modifier
                        .size(width = 64.dp, height = 21.dp)
                        .constrainAs(launch) {
                            start.linkTo(anchor = bg.start, margin = 24.dp)
                            top.linkTo(bg.top, margin = 24.dp)
                        }
                )

                if (hasFinished) {
                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_hash_gray),
                        contentDescription = "image description",
                        modifier = Modifier
                            .size(36.dp)
                            .constrainAs(hash) {
                                end.linkTo(launch.end, margin = (-6).dp)
                                bottom.linkTo(anchor = launch.top, margin = 6.dp)
                            }
                    )

                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_star_gray),
                        contentDescription = "image description",
                        modifier = Modifier
                            .size(18.dp)
                            .constrainAs(star) {
                                start.linkTo(title.end, margin = (-6).dp)
                                bottom.linkTo(anchor = bg.top)
                            }
                    )
                } else {
                    if (inPreview) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_hash),
                            contentDescription = "image description",
                            modifier = Modifier
                                .size(36.dp)
                                .constrainAs(hashBg) {
                                    end.linkTo(launch.end, margin = (-6).dp)
                                    bottom.linkTo(anchor = launch.top, margin = 6.dp)
                                }
                        )

                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_star),
                            contentDescription = "image description",
                            modifier = Modifier
                                .size(18.dp)
                                .constrainAs(starBg) {
                                    start.linkTo(title.end, margin = (-6).dp)
                                    bottom.linkTo(anchor = bg.top)
                                }
                        )
                    }

                    val chatComposition by rememberLottieComposition(
                        spec = LottieCompositionSpec.Asset(
                            "tacit/launch/chat.json"
                        ),
                    )
                    val chatProgress by animateLottieCompositionAsState(composition = chatComposition)
                    LottieAnimation(
                        composition = chatComposition,
                        progress = { if (inAnimating(card)) chatProgress else 1f },
                        modifier = Modifier
                            .size(36.dp)
                            .constrainAs(hash) {
                                end.linkTo(launch.end, margin = (-6).dp)
                                bottom.linkTo(anchor = launch.top, margin = 6.dp)
                            }
                    )

                    val starComposition by rememberLottieComposition(
                        spec = LottieCompositionSpec.Asset(
                            "tacit/launch/star.json"
                        ),
                    )
                    val starProgress by animateLottieCompositionAsState(composition = starComposition)
                    LottieAnimation(
                        composition = starComposition,
                        progress = { if (inAnimating(card)) starProgress else 1f },
                        modifier = Modifier
                            .size(18.dp)
                            .constrainAs(star) {
                                start.linkTo(title.end, margin = (-6).dp)
                                bottom.linkTo(anchor = bg.top)
                            }
                    )
                }

                Text(
                    text = "来测测我们的默契值有多少吧～",
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF5E5E5E),
                    ),
                    modifier = Modifier.constrainAs(desc) {
                        start.linkTo(launch.start)
                        top.linkTo(launch.bottom, margin = 12.dp)

                        width = Dimension.fillToConstraints
                    }
                )

                Row(
                    Modifier
                        .constrainAs(fromAvatar) {
                            top.linkTo(desc.bottom, margin = 26.dp)
                            start.linkTo(desc.start)
                            bottom.linkTo(parent.bottom, 24.dp)
                        },
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    OImageView2(
                        imageUrl = getFriendAvatar(contact = contact),
                        modifier = Modifier
                            .clip(CircleShape)
                            .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = CircleShape)
                            .size(40.dp)
                            .background(
                                color = colorResource(id = R.color.common_color_000000_10),
                                shape = CircleShape
                            )
                            .alpha(if (hasFinished) 0.5f else 1f)
                    )

                    Image(
                        painter = painterResource(id = if (hasFinished) R.mipmap.chat_ic_tacit_test_submit_gray else R.mipmap.chat_ic_tacit_test_submit),
                        contentDescription = "image description",
                        modifier = Modifier.size(width = 58.dp, height = 28.dp)
                    )
                }

                Row(
                    Modifier
                        .conditional(!hasFinished) {
                            background(
                                color = Color(0xFF54F2BB),
                                shape = RoundedCornerShape(size = 32.dp)
                            )
                        }
                        .padding(horizontal = 4.dp, vertical = 3.dp)
                        .constrainAs(toAvatar) {
                            top.linkTo(fromAvatar.top)
                            start.linkTo(fromAvatar.end, 20.dp)
                            bottom.linkTo(fromAvatar.bottom)
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OImageView2(
                        imageUrl = getMyAvatar(inPreview = inPreview),
                        modifier = Modifier
                            .clip(CircleShape)
                            .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = CircleShape)
                            .size(40.dp)
                            .background(
                                color = colorResource(id = R.color.common_color_000000_10),
                                shape = CircleShape
                            )
                            .alpha(if (hasFinished) 0.5f else 1f)
                    )

                    if (hasFinished) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_submit_gray),
                            contentDescription = "image description",
                            modifier = Modifier.size(width = 58.dp, height = 28.dp)
                        )
                    } else {
                        Text(
                            text = "去作答",
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(600),
                                color = Color(0xFF292929),
                            ),
                            modifier = Modifier.padding(
                                start = 4.dp,
                                end = 14.dp
                            )
                        )
                    }
                }

                var isBtnLight2Playing by remember { mutableStateOf(false) }
                LaunchedEffect(Unit) {
                    delay(600)
                    isBtnLight2Playing = true
                }
                val btnLight2Composition by rememberLottieComposition(
                    spec = LottieCompositionSpec.Asset(
                        "tacit/launch/btn_light2.json"
                    ),
                )
                val btnLight2Progress by animateLottieCompositionAsState(
                    composition = btnLight2Composition,
                    isPlaying = isBtnLight2Playing,
                )
                LottieAnimation(
                    composition = btnLight2Composition,
                    progress = { if (inAnimating(card)) btnLight2Progress else 1f },
                    modifier = Modifier
                        .constrainAs(toAvatarBtnLight2) {
                            top.linkTo(toAvatar.top)
                            start.linkTo(toAvatar.start)
                            bottom.linkTo(toAvatar.bottom)
                            end.linkTo(toAvatar.end)

                            width = Dimension.fillToConstraints
                        },
                )
            }

            if (!hasFinished) {
                Text(
                    text = "Ta发起了默契考验，点击去作答",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF7F7F7F),
                        textAlign = TextAlign.Right,
                    ),
                    modifier = Modifier
                        .padding(top = 8.dp)
                )
            }
        }
    }
}

@Composable
private fun TacitTestLaunchToCard(
    conversation: Conversation = Conversation(),
    contact: Contact?,
    card: TacitTestCard,
    inPreview: Boolean = false,
    onClickAvatar: () -> Unit = {},
) {
    val hasFinished = card.replyStatus == 2
    TacitTestLaunchCard(
        contact = contact,
        isFriend = false,
        inPreview = inPreview,
        onClickAvatar = onClickAvatar
    ) {
        Column(
            modifier = Modifier
                .weight(1f),
            horizontalAlignment = Alignment.End
        ) {
            ConstraintLayout(
                modifier = Modifier
                    .wrapContentWidth()
                    .width(width = 276.dp)
                    .noRippleClickable {
                        if (!hasFinished) {
                            openH5(conversation, card.firstJumpUrl)
                        }
                    }
            ) {
                val (bg, hashBg, hash, starBg, star, launch, titleBg, title, desc, fromAvatar, toAvatar, arrowBg, arrow) = createRefs()

                Image(
                    painter = painterResource(id = if (hasFinished) R.mipmap.chat_ic_tacit_test_launch_bg_gray else R.mipmap.chat_ic_tacit_test_launch_bg),
                    contentDescription = "image description",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .constrainAs(bg) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            top.linkTo(parent.top, margin = 24.dp)
                            bottom.linkTo(parent.bottom)

                            width = Dimension.fillToConstraints
                            height = Dimension.fillToConstraints
                        }
                )

                if (hasFinished) {
                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_arrow_gray),
                        contentDescription = "image description",
                        modifier = Modifier
                            .size(width = 43.dp, height = 53.dp)
                            .constrainAs(arrow) {
                                start.linkTo(title.end, margin = (-28).dp)
                                top.linkTo(title.top, margin = 36.dp)
                                bottom.linkTo(title.bottom)
                            }
                    )

                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_title_gray),
                        contentDescription = "image description",
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier
                            .size(width = 166.dp, height = 66.dp)
                            .constrainAs(title) {
                                start.linkTo(anchor = launch.end)
                                bottom.linkTo(anchor = launch.bottom, margin = (-6).dp)
                            }
                    )
                } else {
                    if (inPreview) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_arrow),
                            contentDescription = "image description",
                            modifier = Modifier
                                .size(width = 43.dp, height = 53.dp)
                                .constrainAs(arrowBg) {
                                    start.linkTo(title.end, margin = (-28).dp)
                                    top.linkTo(title.top, margin = 36.dp)
                                    bottom.linkTo(title.bottom)
                                }
                        )

                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_title),
                            contentDescription = "image description",
                            contentScale = ContentScale.FillBounds,
                            modifier = Modifier
                                .size(width = 166.dp, height = 66.dp)
                                .constrainAs(titleBg) {
                                    start.linkTo(anchor = launch.end)
                                    bottom.linkTo(anchor = launch.bottom, margin = (-6).dp)
                                }
                        )
                    }

                    var shouldAnimateArrow by remember { mutableStateOf(false) }
                    LaunchedEffect(Unit) {
                        if (inAnimating(card)) {
                            delay(400)
                        }
                        shouldAnimateArrow = true
                    }
                    if (shouldAnimateArrow) {
                        val arrowComposition by rememberLottieComposition(
                            spec = LottieCompositionSpec.Asset(
                                "tacit/launch/pink_arrow.json"
                            ),
                        )
                        val arrowProgress by animateLottieCompositionAsState(composition = arrowComposition)
                        LottieAnimation(
                            composition = arrowComposition,
                            progress = { if (inAnimating(card)) arrowProgress else 1f },
                            modifier = Modifier
                                .size(width = 43.dp, height = 53.dp)
                                .constrainAs(arrow) {
                                    start.linkTo(title.end, margin = (-28).dp)
                                    top.linkTo(title.top, margin = 36.dp)
                                    bottom.linkTo(title.bottom)
                                }
                        )
                    }

                    val titleComposition by rememberLottieComposition(
                        spec = LottieCompositionSpec.Asset(
                            "tacit/launch/title_orange.json"
                        ),
                    )
                    val titleProgress by animateLottieCompositionAsState(composition = titleComposition)
                    LottieAnimation(
                        composition = titleComposition,
                        progress = { if (inAnimating(card)) titleProgress else 1f },
                        modifier = Modifier
                            .size(width = 166.dp, height = 66.dp)
                            .constrainAs(title) {
                                start.linkTo(anchor = launch.end)
                                bottom.linkTo(anchor = launch.bottom, margin = (-6).dp)
                            }
                    )
                }

                Image(
                    painter = painterResource(id = if (hasFinished) R.mipmap.chat_ic_tacit_test_launch_me_gray else R.mipmap.chat_ic_tacit_test_launch_me),
                    contentDescription = "image description",
                    modifier = Modifier
                        .size(width = 64.dp, height = 21.dp)
                        .constrainAs(launch) {
                            start.linkTo(anchor = bg.start, margin = 24.dp)
                            top.linkTo(bg.top, margin = 24.dp)
                        }
                )

                if (hasFinished) {
                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_hash_gray),
                        contentDescription = "image description",
                        modifier = Modifier.constrainAs(hash) {
                            end.linkTo(launch.end, margin = (-6).dp)
                            bottom.linkTo(anchor = launch.top, margin = 6.dp)
                        }
                    )

                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_star_gray),
                        contentDescription = "image description",
                        modifier = Modifier
                            .size(18.dp)
                            .constrainAs(star) {
                                start.linkTo(title.end, margin = (-6).dp)
                                bottom.linkTo(anchor = bg.top)
                            }
                    )
                } else {
                    if (inPreview) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_hash),
                            contentDescription = "image description",
                            modifier = Modifier
                                .size(36.dp)
                                .constrainAs(hashBg) {
                                    end.linkTo(launch.end, margin = (-6).dp)
                                    bottom.linkTo(anchor = launch.top, margin = 6.dp)
                                }
                        )

                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_launch_star),
                            contentDescription = "image description",
                            modifier = Modifier
                                .size(18.dp)
                                .constrainAs(starBg) {
                                    start.linkTo(title.end, margin = (-6).dp)
                                    bottom.linkTo(anchor = bg.top)
                                }
                        )
                    }

                    val chatComposition by rememberLottieComposition(
                        spec = LottieCompositionSpec.Asset(
                            "tacit/launch/chat.json"
                        ),
                    )
                    val chatProgress by animateLottieCompositionAsState(composition = chatComposition)
                    LottieAnimation(
                        composition = chatComposition,
                        progress = { if (inAnimating(card)) chatProgress else 1f },
                        modifier = Modifier
                            .size(36.dp)
                            .constrainAs(hash) {
                                end.linkTo(launch.end, margin = (-6).dp)
                                bottom.linkTo(anchor = launch.top, margin = 6.dp)
                            }
                    )

                    val starComposition by rememberLottieComposition(
                        spec = LottieCompositionSpec.Asset(
                            "tacit/launch/star.json"
                        ),
                    )
                    val starProgress by animateLottieCompositionAsState(composition = starComposition)
                    LottieAnimation(
                        composition = starComposition,
                        progress = { if (inAnimating(card)) starProgress else 1f },
                        modifier = Modifier
                            .size(18.dp)
                            .constrainAs(star) {
                                start.linkTo(title.end, margin = (-6).dp)
                                bottom.linkTo(anchor = bg.top)
                            }
                    )
                }

                Text(
                    text = "来测测我们的默契值有多少吧～",
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF292929),
                    ),
                    modifier = Modifier.constrainAs(desc) {
                        start.linkTo(launch.start)
                        top.linkTo(launch.bottom, margin = 12.dp)

                        width = Dimension.fillToConstraints
                    }
                )

                Row(
                    Modifier
                        .constrainAs(fromAvatar) {
                            top.linkTo(desc.bottom, margin = 26.dp)
                            start.linkTo(desc.start)
                            bottom.linkTo(parent.bottom, 24.dp)
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OImageView2(
                        imageUrl = getFriendAvatar(contact),
                        modifier = Modifier
                            .clip(CircleShape)
                            .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = CircleShape)
                            .size(40.dp)
                            .background(
                                color = colorResource(id = R.color.common_color_000000_10),
                                shape = CircleShape
                            )
                            .alpha(if (hasFinished) 0.5f else 1f)
                    )

                    if (hasFinished) {
                        Image(
                            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_submit_gray),
                            contentDescription = "image description",
                            modifier = Modifier.size(width = 58.dp, height = 28.dp)
                        )
                    } else {
                        Text(
                            text = "待作答",
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF292929),
                            ),
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )
                    }
                }

                Row(
                    Modifier
                        .padding(horizontal = 4.dp, vertical = 3.dp)
                        .constrainAs(toAvatar) {
                            top.linkTo(fromAvatar.top)
                            start.linkTo(fromAvatar.end, 20.dp)
                            bottom.linkTo(fromAvatar.bottom)
                        },
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    OImageView2(
                        imageUrl = getMyAvatar(inPreview = inPreview),
                        modifier = Modifier
                            .clip(CircleShape)
                            .border(width = 2.dp, color = Color(0xFFFFFFFF), shape = CircleShape)
                            .size(40.dp)
                            .background(
                                color = colorResource(id = R.color.common_color_000000_10),
                                shape = CircleShape
                            )
                            .alpha(if (hasFinished) 0.5f else 1f)
                    )

                    Image(
                        painter = painterResource(id = if (hasFinished) R.mipmap.chat_ic_tacit_test_submit_gray else R.mipmap.chat_ic_tacit_test_submit),
                        contentDescription = "image description",
                        modifier = Modifier.size(width = 58.dp, height = 28.dp)
                    )
                }
            }

            if (!hasFinished) {
                Text(
                    text = "你发起了默契考验，等待Ta的作答",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF7F7F7F),
                        textAlign = TextAlign.Right,
                    ),
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .align(Alignment.End)
                )
            }
        }
    }
}

@Composable
private fun TacitTestLaunchCard(
    contact: Contact?,
    modifier: Modifier = Modifier,
    isFriend: Boolean = false,
    inPreview: Boolean = false,
    onClickAvatar: () -> Unit = {},
    messageCard: @Composable RowScope.() -> Unit,
) {
    Row(modifier = modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(10.dp)) {
        if (isFriend) {
            Avatar(avatar = getFriendAvatar(contact), onClick = onClickAvatar)
            messageCard()
        } else {
            messageCard()
            Avatar(avatar = getMyAvatar(inPreview = inPreview), onClick = onClickAvatar)
        }
    }
}

@Composable
private fun Avatar(
    avatar: String?,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Box(
        modifier = modifier
            .padding(top = 24.dp)
            .size(40.dp)
            .noRippleClickable(onClick = onClick)
    ) {
        if (avatar.isNullOrEmpty()) {
            Image(
                painter = painterResource(id = R.mipmap.common_default_avatar),
                contentDescription = "image description",
                modifier = modifier.size(40.dp),
            )
        } else {
            OImageView2(
                imageUrl = avatar,
                modifier = modifier
                    .clip(CircleShape)
                    .size(40.dp)
            )
        }
    }
}

@Composable
private fun TacitTestResultCard(
    conversation: Conversation = Conversation(),
    contact: Contact?,
    card: TacitTestCard,
    inPreview: Boolean = false
) {
    ConstraintLayout(
        modifier = Modifier
            .wrapContentWidth()
            .width(width = 314.dp)
    ) {
        val (bg, titleBg, title, desc, score, avatars, gloveBg, glove) = createRefs()

        Image(
            painter = painterResource(id = R.mipmap.chat_ic_tacit_test_result_bg),
            contentDescription = "image description",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.constrainAs(bg) {
                start.linkTo(parent.start)
                end.linkTo(parent.end)
                top.linkTo(parent.top, margin = 26.dp)
                bottom.linkTo(parent.bottom)

                width = Dimension.fillToConstraints
                height = Dimension.fillToConstraints
            }
        )

        if (inPreview) {
            Image(
                painter = painterResource(id = R.mipmap.chat_ic_tacit_test_result_title),
                contentDescription = "image description",
                modifier = Modifier
                    .size(width = 134.dp, height = 56.dp)
                    .constrainAs(titleBg) {
                        start.linkTo(anchor = bg.start, margin = 8.dp)
                        top.linkTo(parent.top)
                    }
            )
        }

        val titleCpComposition by rememberLottieComposition(
            spec = LottieCompositionSpec.Asset(
                "tacit/result/title_cp.json"
            ),
        )
        val titleCpProgress by animateLottieCompositionAsState(composition = titleCpComposition)
        LottieAnimation(
            composition = titleCpComposition,
            progress = { if (inAnimating(card)) titleCpProgress else 1f },
            modifier = Modifier
                .size(width = 134.dp, height = 56.dp)
                .constrainAs(title) {
                    start.linkTo(anchor = bg.start, margin = 8.dp)
                    top.linkTo(parent.top)
                }
        )

        // 先男后女
        Row(
            Modifier
                .width(216.dp)
                .constrainAs(desc) {
                    start.linkTo(bg.start, 20.dp)
                    top.linkTo(title.bottom, margin = 22.dp)
                    end.linkTo(bg.end, 20.dp)
                },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            Text(
                text = "@${
                    getMaleNickname(contact = contact, inPreview = inPreview)
                }",
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 16.761469.sp,
                    fontWeight = FontWeight(700),
                    color = Color(0xFF191919),
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center,
                modifier = Modifier.widthIn(max = 65.dp)
            )

            Text(
                text = "和",
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 16.761469.sp,
                    fontWeight = FontWeight(700),
                    color = Color(0xFF191919),
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center,
                modifier = Modifier
            )

            Text(
                text = "@${
                    getFemaleNickname(contact = contact, inPreview = inPreview)
                }",
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 16.761469.sp,
                    fontWeight = FontWeight(700),
                    color = Color(0xFF191919),
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center,
                modifier = Modifier.widthIn(max = 65.dp)
            )
        }

        ConstraintLayout(
            modifier = Modifier
                .wrapContentWidth()
                .noRippleClickable {
                    openH5(conversation, card.firstJumpUrl)
                }
                .padding(end = 22.dp)
                .constrainAs(score) {
                    top.linkTo(desc.bottom, margin = 12.dp)
                    start.linkTo(bg.start)
                    end.linkTo(bg.end)
                }
                .graphicsLayer {
                    clip = false
                },
        ) {
            val (dot, leftAvatar, sc, rightAvatar) = createRefs()

            var dotVisible by remember { mutableStateOf(inPreview) }
            LaunchedEffect(Unit) {
                if (inAnimating(card)) {
                    delay(600)
                }
                dotVisible = true
            }
            AnimatedVisibility(
                visible = dotVisible,
                enter = fadeIn() + expandIn(
                    expandFrom = Alignment.TopStart,
                ),
                modifier = Modifier
                    .constrainAs(dot) {
                        top.linkTo(leftAvatar.top, (-9).dp)
                        end.linkTo(leftAvatar.start, (-9).dp)
                    }
            ) {
                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_tacit_test_result_dot),
                    contentDescription = "image description",
                    modifier = Modifier
                        .size(width = 22.dp, height = 28.dp)
                )
            }

            // 先男后女
            Box(
                modifier = Modifier
                    .clip(CircleShape)
                    .size(50.dp)
                    .constrainAs(leftAvatar) {
                        end.linkTo(sc.start)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
            ) {
                OImageView2(
                    imageUrl = getMaleAvatar(contact = contact, inPreview = inPreview),
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(46.dp)
                        .clip(CircleShape)
                        .background(colorResource(id = R.color.common_color_000000_5))
                )

                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_avatar_border),
                    contentDescription = "image description",
                    modifier = Modifier
                        .size(50.dp)
                )
            }

            var scoreVisible by remember { mutableStateOf(inPreview) }
            var matchScore by remember { mutableIntStateOf(0) }
            LaunchedEffect(Unit) {
                scoreVisible = true

                val ms = card.matchScore ?: 0 // 86
                if (inAnimating(card)) {// 限制最多 1000ms 内走完数字动画
                    val interval = 50
                    val maxTime = 1000
                    if (ms * interval > maxTime) {
                        matchScore = ms - maxTime / interval
                    }
                    if (ms > 0) {
                        while (matchScore < ms) {
                            matchScore++
                            delay(50)
                        }
                    }
                } else {
                    matchScore = ms
                }
            }
            AnimatedVisibility(
                visible = scoreVisible,
                modifier = Modifier.constrainAs(sc) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                },
                enter =
                    if (inAnimating(card)) scaleIn() + fadeIn() + expandIn(expandFrom = Alignment.Center)
                    else fadeIn(),
            ) {
                Text(
                    text = buildAnnotatedString {
                        withStyle(
                            style = SpanStyle(
                                fontSize = 36.sp,
                                fontWeight = FontWeight(800),
                            )
                        ) {
                            append("$matchScore")
                        }

                        withStyle(
                            style = SpanStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight(400),
                            )
                        ) {
                            append("%")
                        }
                    },
                    style = TextStyle(
                        color = colorResource(R.color.common_color_191919),
                        fontFamily = FontFamily(Font(resId = R.font.source_han_serif_cn_bold)),
                    ),
                    modifier = Modifier
                        .padding(horizontal = 6.dp)
                )
            }

            Box(
                modifier = Modifier
                    .clip(CircleShape)
                    .size(50.dp)
                    .constrainAs(rightAvatar) {
                        start.linkTo(sc.end)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
            ) {
                OImageView2(
                    imageUrl = getFemaleAvatar(contact = contact, inPreview = inPreview),
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(46.dp)
                        .clip(CircleShape)
                        .background(colorResource(id = R.color.common_color_000000_5))
                        .conditional(inPreview) {
                            background(Color.LightGray)
                        }
                )

                Image(
                    painter = painterResource(id = R.mipmap.chat_ic_avatar_border),
                    contentDescription = "image description",
                    modifier = Modifier
                        .size(50.dp)
                )
            }
        }

        Row(
            modifier = Modifier
                .padding(horizontal = 20.dp)
                .constrainAs(avatars) {
                    top.linkTo(score.bottom, margin = 20.dp)
                    start.linkTo(bg.start)
                    end.linkTo(bg.end)
                    bottom.linkTo(parent.bottom, 15.dp)
                },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            Text(
                text = "查看详情",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF191919),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .noRippleClickable {
                        openH5(conversation, card.firstJumpUrl)
                    }
                    .background(color = Color(0xFFD7F0F5), shape = RoundedCornerShape(size = 20.dp))
                    .padding(10.dp)
                    .weight(1f)
            )

            Text(
                text = "再玩一次",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF191919),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .noRippleClickable {
                        openH5(conversation, card.secondJumpUrl)
                    }
                    .background(color = Color(0xFF54F2BB), shape = RoundedCornerShape(size = 20.dp))
                    .padding(10.dp)
                    .weight(1f)
            )
        }

        if (inPreview) {
            Image(
                painter = painterResource(id = R.mipmap.chat_ic_tacit_test_result_blink),
                contentDescription = "image description",
                modifier = Modifier
                    .size(width = 120.dp, height = 60.dp)
                    .constrainAs(gloveBg) {
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = 12.dp)
                    }
            )
        }
        val cp2Composition by rememberLottieComposition(
            spec = LottieCompositionSpec.Asset(
                "tacit/result/cp2.json"
            ),
        )
        val cp2Progress by animateLottieCompositionAsState(composition = cp2Composition)
        LottieAnimation(
            composition = cp2Composition,
            progress = { if (inAnimating(card)) cp2Progress else 1f },
            modifier = Modifier
                .size(width = 120.dp, height = 60.dp)
                .constrainAs(glove) {
                    end.linkTo(parent.end)
                    top.linkTo(parent.top, margin = 12.dp)
                }
        )
    }
}
//endregion

//region UserInfo
private fun getMaleAvatar(contact: Contact?, inPreview: Boolean = false): String =
    if (isMyGenderMale(inPreview = inPreview)) getMyAvatar(inPreview = inPreview) else getFriendAvatar(
        contact = contact
    )

private fun getFemaleAvatar(contact: Contact?, inPreview: Boolean = false): String =
    if (isMyGenderMale(inPreview = inPreview)) getFriendAvatar(contact = contact) else getMyAvatar(
        inPreview = inPreview
    )

private fun getMaleNickname(contact: Contact?, inPreview: Boolean = false): String =
    if (isMyGenderMale(inPreview = inPreview)) getMyNickName(inPreview = inPreview) else getFriendNickName(
        contact = contact
    )

private fun getFemaleNickname(contact: Contact?, inPreview: Boolean = false): String =
    if (isMyGenderMale(inPreview = inPreview)) getFriendNickName(contact = contact) else getMyNickName(
        inPreview = inPreview
    )

private fun isMyGenderMale(inPreview: Boolean = false): Boolean =
    if (inPreview) true else if (AccountHelper.getInstance().isLogin()) {
        val userInfo = AccountHelper.getInstance().getUserInfo()
        userInfo?.gender == 1
    } else {
        false
    }

private fun getFriendNickName(contact: Contact?): String =
    (contact?.nickName ?: "").ifEmpty { contact?.nickNamePy ?: "" }

private fun getMyNickName(inPreview: Boolean = false): String =
    if (inPreview) "一二三四五六七八九十" else if (AccountHelper.getInstance().isLogin()) {
        val userInfo = AccountHelper.getInstance().getUserInfo()
        (userInfo?.nickName ?: "").ifEmpty { userInfo?.nickNamePy ?: "" }
    } else {
        ""
    }
//endregion

//region Preview
@Preview(showBackground = true, widthDp = 480)
@Composable
fun PreviewTacitTestCard() {
    TacitTestCard(message = MessageForTacitTestCard().apply {
        tacitTestCard = TacitTestCard(cardType = TYPE_TACIT_TEST_CARD_UNLOCK).apply {
            inAnimating = true
        }
    }, inPreview = true)
}

@Preview(showBackground = true, widthDp = 480)
@Composable
fun PreviewTacitTestLaunchFromCard() {
    TacitTestCard(message = MessageForTacitTestCard().apply {
        tacitTestCard = TacitTestCard(cardType = TYPE_TACIT_TEST_CARD_LAUNCH).apply {
            inAnimating = true
        }
    }, isFriend = true, inPreview = true)
}

@Preview(showBackground = true, widthDp = 480)
@Composable
fun PreviewTacitTestLaunchFromCardFinished() {
    TacitTestCard(message = MessageForTacitTestCard().apply {
        tacitTestCard = TacitTestCard(cardType = TYPE_TACIT_TEST_CARD_LAUNCH).apply {
            replyStatus = 2
            inAnimating = true
        }
    }, isFriend = true, inPreview = true)
}

@Preview(showBackground = true, widthDp = 480)
@Composable
fun PreviewTacitTestLaunchToCard() {
    TacitTestCard(message = MessageForTacitTestCard().apply {
        tacitTestCard = TacitTestCard(cardType = TYPE_TACIT_TEST_CARD_LAUNCH).apply {
            inAnimating = true
        }
    }, isFriend = false, inPreview = true)
}

@Preview(showBackground = true, widthDp = 480)
@Composable
fun PreviewTacitTestLaunchToCardFinished() {
    TacitTestCard(message = MessageForTacitTestCard().apply {
        tacitTestCard = TacitTestCard(cardType = TYPE_TACIT_TEST_CARD_LAUNCH).apply {
            replyStatus = 2
            inAnimating = true
        }
    }, isFriend = false, inPreview = true)
}

@Preview(showBackground = true, widthDp = 480)
@Composable
fun PreviewTacitTestResultCard() {
    TacitTestCard(
        contact = Contact().apply {
            nickName =
                "壹贰叁肆伍陆柒捌玖拾"
        },
        message = MessageForTacitTestCard().apply {
            tacitTestCard = TacitTestCard(cardType = TYPE_TACIT_TEST_CARD_RESULT).apply {
                matchScore = 86
                inAnimating = true
            }
        },
        inPreview = true
    )
}
//endregion