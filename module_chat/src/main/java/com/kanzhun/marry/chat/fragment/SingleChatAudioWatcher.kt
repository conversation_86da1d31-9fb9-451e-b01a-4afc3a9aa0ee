package com.kanzhun.marry.chat.fragment

import android.text.Editable
import android.text.TextWatcher
import com.kanzhun.foundation.kotlin.ktx.simplePost

class SingleChatAudioWatcher(val chatId: String) {

    private var lastRequestTime = 0L
    
    fun onAudioChanged(status:Int) {
        val currentTime = System.currentTimeMillis()
        // Rate limit: only send request if more than 1000ms (1 second) has passed since last request
        if (currentTime - lastRequestTime > 1000) {
            "orange/chat/reportSendStatus".simplePost(map = mapOf("type" to "2",  "chatId" to chatId))
            lastRequestTime = currentTime
        }
    }


}