package com.kanzhun.marry.chat.activity

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.sp
import com.kanzhun.common.util.LDate
import com.kanzhun.foundation.model.message.MessageForTime

@Composable
fun Time(
    messageForTime: MessageForTime,
    textColor: Color = Color(0xFFF7F7F7),
    modifier: Modifier = Modifier
) {
    Text(
        text = getTime(messageForTime.time),
        style = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight(400),
            color = textColor,
        ),
        textAlign = TextAlign.Center,
        modifier = modifier.fillMaxWidth()
    )
}

private fun getTime(time: Long): String {
    if (time == 0L) {
        return ""
    }
    if (LDate.isToday(time)) {
        return LDate.getDate(time, "HH:mm")
    }
    if (LDate.isYesterday(time)) {
        return "昨天 " + LDate.getDate(time, "HH:mm")
    }
    if (LDate.isSameYear(time)) {
        return LDate.getDate(time, "MM月dd日 HH:mm")
    }

    return LDate.getDate(time, "yyyy年MM月dd日 HH:mm")
}