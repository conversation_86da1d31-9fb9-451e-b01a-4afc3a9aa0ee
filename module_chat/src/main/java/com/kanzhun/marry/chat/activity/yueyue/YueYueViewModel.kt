package com.kanzhun.marry.chat.activity.yueyue

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.toMutableStateList
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.sendRequestChatListEvent
import com.kanzhun.foundation.isQaDebugUser
import com.kanzhun.foundation.model.message.CertInfo
import com.kanzhun.foundation.model.message.CertTag
import com.kanzhun.foundation.model.message.ChatMessage
import com.kanzhun.foundation.model.message.MessageForRecommendOpenChat
import com.kanzhun.foundation.model.message.MessageForRecommendUserCard
import com.kanzhun.foundation.model.message.MessageForRecommendWelcome
import com.kanzhun.foundation.model.message.MessageForText
import com.kanzhun.foundation.model.message.MessageForTime
import com.kanzhun.foundation.model.message.RecommendOpenChat
import com.kanzhun.foundation.model.message.RecommendUserCard
import com.kanzhun.foundation.model.message.RecommendWelcome
import com.kanzhun.foundation.model.message.UserCard
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.api.KChatApi
import com.kanzhun.marry.chat.api.response.YueYueRecMsgResponse
import com.kanzhun.marry.chat.api.response.YueYueTopCardResponse
import com.kanzhun.utils.T
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class YueYueViewModel : BaseViewModel() {
    private val _yueyueTopCardStateFlow = MutableStateFlow(YueYueTopCardResponse())
    val yueyueTopCardStateFlow: StateFlow<YueYueTopCardResponse> = _yueyueTopCardStateFlow

    private val _yueYueMessageStateFlow = MutableStateFlow(
        YueYueMessageState(isRefreshing = false)
    )
    val yueYueMessageStateFlow: StateFlow<YueYueMessageState> = _yueYueMessageStateFlow

    // State for recommendation phrases
    private val _recMsgStateFlow = MutableStateFlow(YueYueRecMsgState()) // Made public for preview
    val recMsgStateFlow: StateFlow<YueYueRecMsgState> = _recMsgStateFlow

    /**
     * Fetch YueYue top card data from the server
     */
    fun getYueYueTopCard() {
        HttpExecutor.execute(
            RetrofitManager.getInstance().createApi(KChatApi::class.java).getYueYueTopCard(),
            object : BaseRequestCallback<YueYueTopCardResponse?>(true) {
                override fun onSuccess(data: YueYueTopCardResponse?) {
                    _yueyueTopCardStateFlow.update { data ?: YueYueTopCardResponse() }
                }

                override fun dealFail(reason: ErrorReason?) {
                    // Handle error if needed

                    if (isQaDebugUser()) {
                        T.ss(reason?.errReason)

                        viewModelScope.launch {
                            delay(10000)

                            sendRequestChatListEvent(LivedataKeyCommon.EVENT_KEY_REQUEST_YUEYUE_CARD)
                        }
                    }
                }
            })
    }

    /**
     * Trigger pull-to-refresh action
     */
    fun inPullToRequestMessages() {
        _yueYueMessageStateFlow.update { state ->
            state.copy(isPullToRefreshData = true, isRefreshing = true)
        }
    }

    /**
     * Update chat messages and reset loading state
     */
    fun updateChatMessages(chatMessages: List<ChatMessage>) {
        viewModelScope.launch {
            if (isQaDebugUser() && _yueYueMessageStateFlow.value.isRefreshing) {
                delay(1000)
            }

            _yueYueMessageStateFlow.update { state ->
                state.copy(
                    chatMessages = chatMessages.toMutableStateList(),
                    isRefreshing = false,
                )
            }
        }
    }

    fun mockYueYueMessages() {
        _yueYueMessageStateFlow.update {
            it.copy(
                chatMessages = mutableStateListOf(
                    // Welcome message
                    MessageForRecommendWelcome().apply {
                        recommendWelcome = RecommendWelcome(
                            content = "你好，我是小红娘月月，很高兴为你服务！我可以帮你找到合适的对象，给你提供约会建议，解答你的恋爱困惑。",
                            suggestMsgList = listOf(
                                "我要找男生：28-35岁 / 北京 / 有房车 / 温柔爱宠物 / 本科以上",
                                "我要找女生：28-35岁 / 北京 / 有房车 / 温柔爱宠物 / 本科以上",
                                "我想找一个靠谱的对象",
                                "给我推荐一个女生",
                            )
                        )
                    },

                    // User message
                    MessageForText().apply {
                        content = "你好，我想找一个靠谱的对象"
                        sender = "1234567890" // Simulate current user
                    },

                    // Bot response
                    MessageForText().apply {
                        content = "请问你有什么特别的要求吗？"
                    },

                    MessageForTime().apply { time = 1733219306491L },

                    // User message
                    MessageForText().apply {
                        content = "白富美"
                        sender = "1234567890" // Simulate current user
                    },

                    // Recommended user card
                    MessageForRecommendUserCard().apply {
                        recommendUserCard = RecommendUserCard(
                            recommendUserId = "12345",
                            securityId = "sec123",
                            userInfo = UserCard(
                                avatar = "https://img.bosszhipin.com/static/file/2023/d6jjcpkw1692952254758.png",
                                tinyAvatar = "https://img.bosszhipin.com/static/file/2023/d6jjcpkw1692952254758.png",
                                nickName = "张三",
                                age = 28,
                                gender = 1,
                                school = "清华大学",
                                degree = 50,
                                degreeInfo = "本科",
                                addressCode = "110105",
                                addressLevel1 = "北京",
                                addressLevel2 = "朝阳区",
                                height = 180,
                                industryCode = "123",
                                industry = "互联网",
                                careerCode = "234",
                                career = "产品经理",
                                intro = "喜欢旅行、摄影和美食，性格开朗，希望找一个志同道合的伴侣一起探索世界。喜欢旅行、摄影和美食，性格开朗，希望找一个志同道合的伴侣一起探索世界。喜欢旅行、摄影和美食，性格开朗，希望找一个志同道合的伴侣一起探索世界。",
                                certInfo = CertInfo(
                                    certPassCount = 3,
                                    certTagList = listOf(
                                        CertTag(showIcon = 1, content = "温柔体贴", certType = 1),
                                        CertTag(showIcon = 1, content = "喜欢旅行", certType = 2),
                                        CertTag(showIcon = 1, content = "有上进心", certType = 3),
                                        CertTag(showIcon = 1, content = "性格开朗", certType = 4)
                                    )
                                )
                            )
                        )
                    },

                    // Open chat recommendation
                    MessageForRecommendOpenChat().apply {
                        recommendOpenChat = RecommendOpenChat(
                            recommendUserId = "12345",
                            content = "Hi~想不想和我聊一下？",
                            relationStatus = 0
                        )
                    },
                ),
                isRefreshing = false,
                isPullToRefreshData = false
            )
        }
    }

    /**
     * Fetch YueYue recommendation messages from the server
     */
    fun getYueYueRecMsg() {
        // Update state to show loading
        _recMsgStateFlow.update { it.copy(isLoading = true, error = null) }

        HttpExecutor.execute(
            RetrofitManager.getInstance().createApi(KChatApi::class.java).getYueYueRecMsg(),
            object : BaseRequestCallback<YueYueRecMsgResponse?>(true) {
                override fun onSuccess(data: YueYueRecMsgResponse?) {
                    _recMsgStateFlow.update {
                        it.copy(
                            recMessages = data?.rows ?: emptyList(),
                            isLoading = false
                        )
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                    _recMsgStateFlow.update {
                        it.copy(
                            isLoading = false,
                            error = reason?.errReason
                        )
                    }
                }
            })
    }

    fun mockGetYueYueRecMsg() {
        val recMessages = listOf(
            "你能干啥呢吗",
            "给我推荐一个男生",
            "我想找一个温柔的女生",
            "介绍一下你自己"
        )

        _recMsgStateFlow.update {
            it.copy(
                recMessages = recMessages,
                isLoading = false
            )
        }
    }

    /**
     * Refresh recommend user message when user clicks "看ta主页"
     * @param msgId Message ID of the recommendation
     */
    fun refreshRecommendUserMsg(msgId: String) {
        HttpExecutor.execute(
            RetrofitManager.getInstance().createApi(KChatApi::class.java)
                .refreshRecommendUserMsg(msgId),
            object : BaseRequestCallback<Any>(true) {
                override fun onSuccess(data: Any?) {
                    // Success, no action needed as this is just a tracking API
                }

                override fun dealFail(reason: ErrorReason?) {
                    // Silently fail, as this is just a tracking API and shouldn't affect user experience
                }
            }
        )
    }
}

data class YueYueMessageState(
    var chatMessages: List<ChatMessage> = mutableStateListOf(),
    var isPullToRefreshData: Boolean = false,
    var isRefreshing: Boolean = false,
)