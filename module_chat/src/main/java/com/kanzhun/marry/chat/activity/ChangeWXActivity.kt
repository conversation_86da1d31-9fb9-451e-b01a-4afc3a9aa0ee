package com.kanzhun.marry.chat.activity

import android.R.attr.data
import android.R.attr.description
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.FragmentActivity
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.SecurityNoticeManager.dialog
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.viewmodel.ChangeWXViewModel
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [ChatPageRouter.CHANGE_WX_ACTIVITY])
class ChangeWXActivity : BaseComposeActivity() {

    override fun enableSafeDrawingPadding() = false

    private val viewModel: ChangeWXViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val chatId = intent.getStringExtra(BundleConstants.BUNDLE_CHAT_ID) ?: ""
        viewModel.initialize(chatId)
        immersionBar {
            // 设置状态栏透明
//            transparentStatusBar()
            // 如果状态栏文字是深色的，可以用：
            // statusBarDarkFont(true)
        }
    }


    @Composable
    override fun OnSetContent() {
        val uiState = viewModel.uiState

        changeWxComposeView(uiState, onSubmit = {
            viewModel.submitWechatId(this@ChangeWXActivity) {
                finish()
            }
        }, onEdit = {
            if (uiState.remainingEdits > 0){
                ComposeDialogFragment.Companion.shouldShow(this@ChangeWXActivity) { dialogFragment ->
                    var error = remember { mutableStateOf("") }
                    editWxDialog(
                        wxName = uiState.wechatId,
                        error = error.value,
                        onSubmit = {
                            viewModel.updateWechatId(it, onSuccess = {
                                dialogFragment.dismiss()
                            }, onError = { str ->
                                error.value = str
                            })

                        },
                        onDismiss = {
                            dialogFragment.dismiss()
                        })
                }
            }

        })
    }

    @Preview
    @Composable
    private fun PreviewEditWxDialog() {
        editWxDialog("123")
    }

    @Composable
    private fun editWxDialog(
        wxName: String = "",
        error: String = "",
        onSubmit: (str: String) -> Unit = {},
        onDismiss: () -> Unit = {},
    ) {
        var text = remember { mutableStateOf(wxName) }
        Box(contentAlignment = Alignment.Center) {
            Column(
                modifier = Modifier
                    .padding(horizontal = 32.dp)
                    .fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(32.dp))
                    .padding(24.dp)
            ) {
                Text(
                    text = "你的微信号",
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF292929),
                        textAlign = TextAlign.Center
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(18.dp))

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                ) {

                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        androidx.compose.material3.TextField(
                            value = text.value,
                            onValueChange = {
                                if (it.length <= 20) {
                                    text.value = it
                                }
                            },
                            placeholder = {
                                Text(
                                    "请填写",
                                    style = TextStyle(
                                        color = Color(0xFFCCCCCC),
                                        fontSize = 16.sp
                                    )
                                )
                            },
                            singleLine = true,
                            colors = androidx.compose.material3.TextFieldDefaults.colors(
                                focusedContainerColor = Color.Transparent,
                                unfocusedContainerColor = Color.Transparent,
                                focusedIndicatorColor = Color.Transparent,
                                unfocusedIndicatorColor = Color.Transparent
                            ),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(end = 40.dp),
                            textStyle = TextStyle(
                                fontSize = 16.sp,
                                color = Color(0xFF191919)
                            )
                        )

                        Text(
                            text = "${text.value.length}/20",
                            style = TextStyle(
                                fontSize = 14.sp,
                                color = Color(0xFFCCCCCC)
                            ),
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .padding(end = 12.dp)
                        )

                        HorizontalDivider(
                            Modifier
                                .fillMaxWidth()
                                .background(color = Color.Black)
                                .height(1.dp)
                                .align(alignment = Alignment.BottomStart),
                            color = Color.Black,
                        )
                    }
                }

                if (error.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = error,
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFF3F4B),
                        )
                    )
                    Spacer(modifier = Modifier.height(18.dp))
                } else {
                    Spacer(modifier = Modifier.height(42.dp))
                }


                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 8.dp)
                            .height(48.dp)
                            .border(
                                width = 1.dp,
                                color = Color(0xFF292929),
                                shape = RoundedCornerShape(25.dp)
                            )
                            .clip(RoundedCornerShape(25.dp))
                            .noRippleClickable { onDismiss() },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "取消",
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF292929),
                                textAlign = TextAlign.Center
                            )
                        )
                    }

                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 8.dp)
                            .height(48.dp)
                            .background(
                                color = if (text.value.isEmpty()) Color(0xFFCCCCCC) else Color(0xFF191919),
                                shape = RoundedCornerShape(25.dp)
                            )
                            .clip(RoundedCornerShape(25.dp))
                            .noRippleClickable { onSubmit(text.value) },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "保存",
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFFFFFFFF),
                                textAlign = TextAlign.Center,
                            )
                        )
                    }
                }
            }
        }

    }

    @Composable
    private fun changeWxComposeView(
        uiState: ChangeWXViewModel.WechatExchangeUiState,
        onSubmit: () -> Unit = {},
        onEdit: () -> Unit = {},
    ) {
        // Background color for the entire screen
        ConstraintLayout(
            modifier = Modifier
                .background(Color.White)
                .fillMaxSize()
        ) {

            val (
                refSubmitButton,
                refLine,
                refLogo,
                refCenter,
            ) = createRefs()

            Image(
                painter = painterResource(id = R.drawable.image_change_wx_bg),
                contentDescription = "Wechat",
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(375 / 300f)
            )

            Image(
                painter = painterResource(id = R.drawable.image_change_wx_logo),
                contentDescription = "Wechat",
                modifier = Modifier
                    .width(298.dp)
                    .aspectRatio(298 / 196f)
                    .constrainAs(refLogo) {
                        top.linkTo(parent.top, margin = 72.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        width = androidx.constraintlayout.compose.Dimension.wrapContent
                    }
            )

            // Close button at the top
            Icon(
                painter = painterResource(id = R.drawable.common_black_close),
                contentDescription = "Close",
                modifier = Modifier
                    .statusBarsPadding()
                    .padding(16.dp)
                    .noRippleClickable { finish() }
                    .size(24.dp),
            )

            // Loading indicator
//            if (uiState.isLoading) {
//                CircularProgressIndicator(
//                    modifier = Modifier,
//                    color = Color(0xFFFF632E)
//                )
//            }

            // Success message if submission was successful
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 28.dp)
                    .constrainAs(refCenter) {
                        top.linkTo(refLogo.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    }
            ) {
                Text(
                    text = "交换微信号",
                    style = TextStyle(
                        fontSize = 40.sp,
                        fontFamily = boldFontFamily(),
                        color = Color(0xFF191919)
                    ),
                )
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = "聊天达到一定进度后，可以发送微信号给Ta\n请注意谨慎甄别，保护好个人隐私安全哦~",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF191919),
                    ),
                )

                Spacer(modifier = Modifier.height(28.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    Color(0xFFFFEDE4),
                                    Color(0xFFFFFAF8)
                                )
                            ), shape = RoundedCornerShape(12.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 10.dp)
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.image_change_wx_tips),
                        contentDescription = "Wechat",
                        modifier = Modifier
                            .size(24.dp)
                    )

                    Spacer(modifier = Modifier.width(4.dp))

                    //中间颜色字段，通过特殊符号隔开，参考官方消息里的点击跳转按钮，开始字符："\u200b"，结束字符："\u2060"
                    val description = uiState.description
                    val parts = uiState.parts

                    // Split text into parts based on special markers
                    val textParts = description.split("\u200b").flatMap { it.split("\u2060") }
                    val clickableRanges = mutableListOf<IntRange>()
                    var currentPosition = 0

                    ClickableText(
                        text = buildAnnotatedString {
                            textParts.forEachIndexed { index, part ->
                                if (index % 2 == 0) {
                                    // Regular text
                                    withStyle(
                                        style = SpanStyle(
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight(600),
                                            color = Color(0xFF8C2D00),
                                        )
                                    ) {
                                        append(part)
                                    }
                                    currentPosition += part.length
                                } else {
                                    // Clickable text
                                    withStyle(
                                        style = SpanStyle(
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight(600),
                                            color = Color(0xFF4F90FF),
                                        )
                                    ) {
                                        append(part)
                                        clickableRanges.add(currentPosition until (currentPosition + part.length))
                                        currentPosition += part.length
                                    }
                                }
                            }
                        },
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFF8C2D00),
                        ),
                        onClick = { offset ->
                            // Find which clickable range was clicked
                            clickableRanges.forEachIndexed { index, range ->
                                if (offset in range && index < (parts?.size ?: 0)) {
                                    // Get corresponding protocol from parts list
                                    parts?.get(index)?.let { part ->
                                        ProtocolHelper.parseProtocol(part.protocol)
                                    }
                                }
                            }
                        }
                    )
                }


            }

            if (uiState.status == 1) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp)
                        .constrainAs(
                            refLine
                        ) {
                            bottom.linkTo(refSubmitButton.top)
                            start.linkTo(parent.start, 28.dp)
                            end.linkTo(parent.end, 28.dp)
                            width = Dimension.fillToConstraints
                        }
                ) {
                    // Progress background
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(8.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(Color(0xFFEEEEEE))
                    )

                    // Progress fill
                    val progress =
                        (uiState.msgCount.toFloat() / uiState.maxCount).coerceIn(0f, 1f)
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(progress)
                            .height(8.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(
                                Brush.horizontalGradient(
                                    listOf(
                                        Color(0xFF7BF18F),
                                        Color(0xFF56B9DD),
                                        Color(0xFF5165FF),
                                    )
                                )
                            )
                    )

                    Text(
                        text = "与TA聊天达${uiState.maxCount}句解锁",
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 25.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF7F7F7F),
                        ),
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .align(Alignment.BottomStart)
                    )


                    // Progress text
                    Text(
                        text = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight(700),
                                    color = Color(0xFF191919),
                                )
                            ) {
                                append("${uiState.msgCount}")
                            }

                            withStyle(
                                style = SpanStyle(
                                    fontSize = 13.sp,
                                    fontWeight = FontWeight(700),
                                    color = Color(0xFFCCCCCC),
                                )
                            ) {
                                append("/${uiState.maxCount}")
                            }
                        },
                        style = TextStyle(
                            fontSize = 13.sp,
                            fontFamily = boldFontFamily(),
                            fontWeight = FontWeight(700),
                            color = Color(0xFF191919),
                        ),
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .align(Alignment.BottomEnd)
                    )
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp)
                        .noRippleClickable {
                            onEdit()
                        }
                        .constrainAs(
                            refLine
                        ) {
                            bottom.linkTo(refSubmitButton.top)
                            start.linkTo(parent.start, 28.dp)
                            end.linkTo(parent.end, 28.dp)
                            width = Dimension.fillToConstraints
                        },

                    ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = "你的微信号：",
                            style = TextStyle(
                                fontSize = 18.sp,
                                fontWeight = FontWeight(600),
                                color = Color(0xFF191919),
                            )
                        )
                        if (uiState.wechatId.isNotEmpty()) {
                            Text(
                                text = uiState.wechatId,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFF191919),
                                )
                            )
                        } else {
                            Text(
                                text = "请填写",
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFFB2B2B2),
                                )
                            )

                        }

                        Spacer(modifier = Modifier.width(12.dp))


                        if(uiState.wechatId.isEmpty() || uiState.remainingEdits > 0){
                            Image(
                                painter = painterResource(id = R.drawable.image_change_wx_edit),
                                contentDescription = "image description",
                                contentScale = ContentScale.None,
                                modifier = Modifier
                                    .size(18.dp)
                            )
                        }

                    }

                    if (uiState.wechatId.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(12.dp))

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    color = Color(0xFFF5F5F5),
                                    shape = RoundedCornerShape(8.dp)
                                )
                                .padding(10.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.common_tip_bar_warn),
                                contentDescription = "image description",
                                tint = Color(0xFF292929),
                                modifier = Modifier
                                    .size(18.dp)
                            )

                            Spacer(modifier = Modifier.width(4.dp))

                            Text(
                                text = "每个自然月可修改${uiState.totalEdits}次",
                                style = TextStyle(
                                    fontSize = 13.sp,
                                    lineHeight = 22.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF292929),
                                )
                            )


                        }
                    }


                }
            }


            Text(
                text = if (uiState.status == 2) "发送我的微信号" else "继续聊天",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFFFFFFFF),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        if ((uiState.status == 2 && uiState.wechatId.isNotEmpty()) || uiState.status == 1) Color(0xFF191919) else Color(
                            0xFFCCCCCC
                        ),
                        shape = RoundedCornerShape(25.dp)
                    )
                    .padding(vertical = 12.dp)
                    .noRippleClickable {
                        if (uiState.status == 2 && uiState.wechatId.isNotEmpty()) {
                            onSubmit()
                        } else if (uiState.status == 2 && (uiState.wechatId.isEmpty() ||  uiState.remainingEdits > 0)) {
                            onEdit()
                        } else {
                            finish()
                        }
                    }
                    .constrainAs(refSubmitButton) {
                        bottom.linkTo(parent.bottom, 50.dp)
                        start.linkTo(parent.start, 20.dp)
                        end.linkTo(parent.end, 20.dp)
                        width = Dimension.fillToConstraints
                    }
            )

        }
    }

    @Preview
    @Composable
    private fun PreviewChangeWxComposeView() {
        changeWxComposeView(
            ChangeWXViewModel.WechatExchangeUiState(
                maxCount = 100,
                status = 1,
                msgCount = 15
            )
        )
    }

    @Preview
    @Composable
    private fun PreviewChangeWxComposeView2() {
        changeWxComposeView(
            ChangeWXViewModel.WechatExchangeUiState(
                maxCount = 100,
                status = 2,
                msgCount = 15,
                wechatId = "123"
            )
        )
    }

    @Preview
    @Composable
    private fun PreviewChangeWxComposeView3() {
        changeWxComposeView(
            ChangeWXViewModel.WechatExchangeUiState(
                maxCount = 100,
                status = 2,
                msgCount = 15,
                wechatId = ""
            )
        )
    }
}