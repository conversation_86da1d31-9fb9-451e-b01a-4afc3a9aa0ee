<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.ChatCallback" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/idChatBottomEdiLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_color_F4F4F6"
        android:paddingHorizontal="12dp">

        <View
            android:id="@+id/view_top_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="8dp"
            app:layout_constraintBottom_toTopOf="@+id/cl_input_area" />

        <ImageView
            android:id="@+id/iv_voice"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:onClick="@{v->callback.clickSwitchVoice()}"
            android:src="@drawable/chat_ic_voice"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input_area"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginVertical="8dp"
            android:background="@drawable/chat_bg_input"
            android:paddingHorizontal="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/cl_send_area"
            app:layout_constraintStart_toEndOf="@+id/iv_voice"
            app:layout_constraintTop_toTopOf="parent">

            <com.kanzhun.marry.chat.emotion.DeleEmotionEditText
                android:id="@+id/et_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:background="@color/common_color_000000"
                android:focusable="true"
                android:hint="@string/chat_msg_send_hint"
                android:imeOptions="actionSend"
                android:inputType="textMultiLine"
                android:maxLines="7"
                android:paddingVertical="10dp"
                android:text="@={viewModel.inputField}"
                android:textColor="@color/common_black"
                android:textColorHint="@color/common_color_CCCCCC"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/iv_ai"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage"
                tools:text="@string/common_long_placeholder"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_ai"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@mipmap/chat_ic_ai"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_voice_record"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingVertical="8dp"
                android:soundEffectsEnabled="true"
                android:text="@string/chat_record_voice"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_16"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="SpUsage"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_send_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="RtlSymmetry">

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_send"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="@{()->callback.clickTvSend()}"
                android:paddingHorizontal="16dp"
                android:paddingVertical="8dp"
                android:text="@string/chat_send"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_14"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_backgroundColor="@color/common_color_292929"
                app:qmui_radius="12dp"
                app:visibleGone="@{viewModel.inputField.trim().length() > 0 &amp;&amp; !viewModel.voiceMode}"
                tools:ignore="SpUsage"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/iv_more"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="12dp"
                android:src="@drawable/chat_ic_add"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btn_send"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginEnd="0dp"
                app:visibleGone="@{viewModel.inputField.trim().length() &lt;= 0 || viewModel.voiceMode}"
                tools:ignore="ContentDescription"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/ivEmoji"
                android:layout_width="26dp"
                android:layout_height="26dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="12dp"
                android:src="@drawable/chat_ic_icon_chat_emotion"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_more"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="ContentDescription"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/vClickBlock"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:onClick="@{()->callback.clickBlock()}"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>