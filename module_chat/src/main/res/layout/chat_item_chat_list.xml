<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ChatHistoryListActivity">

    <data>

        <import type="android.text.TextUtils" />

        <import type="com.kanzhun.common.util.LDate" />

        <import type="com.kanzhun.foundation.model.message.MessageConstants" />

        <import type="com.kanzhun.foundation.kernel.account.Account" />

        <variable
            name="item"
            type="com.kanzhun.foundation.model.Conversation" />

        <variable
            name="gender"
            type="androidx.databinding.ObservableInt" />

        <variable
            name="selfAvatar"
            type="androidx.databinding.ObservableField&lt;String>" />

    </data>

    <com.kanzhun.marry.chat.views.SwipeLayout
        android:id="@+id/swipe_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_chat_item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@{item.relationStatus == MessageConstants.MATCHING_STATUS_DATING || item.relationStatus == MessageConstants.MATCHING_STATUS_LOVERS ? @color/common_color_E9EEFF : @color/common_translate}">

            <FrameLayout
                android:id="@+id/ll_avatar"
                android:layout_width="57dp"
                android:layout_height="57dp"
                android:layout_marginStart="14.5dp"
                android:layout_marginTop="12.5dp"
                android:layout_marginBottom="12.5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <FrameLayout
                    android:id="@+id/ll_date_avatar"
                    visibleGone="@{item.relationStatus == MessageConstants.MATCHING_STATUS_DATING || item.relationStatus == MessageConstants.MATCHING_STATUS_LOVERS}"
                    android:layout_width="57dp"
                    android:layout_height="53dp"
                    android:visibility="gone">

                    <FrameLayout
                        android:layout_width="57dp"
                        android:layout_height="53dp"
                        android:layout_gravity="end"
                        app:dateMainAvatarBackground="@{gender}">

                        <com.facebook.drawee.view.SimpleDraweeView
                            imageUrlNew="@{item.avatar}"
                            android:layout_width="37dp"
                            android:layout_height="37dp"
                            android:layout_marginStart="12.5dp"
                            android:layout_marginTop="8dp"
                            app:roundAsCircle="true" />
                    </FrameLayout>

                    <FrameLayout
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_gravity="bottom"
                        app:dateSubAvatarBackground="@{gender}">

                        <com.facebook.drawee.view.SimpleDraweeView
                            imageUrlNew="@{selfAvatar}"
                            android:layout_width="17dp"
                            android:layout_height="17dp"
                            android:layout_gravity="center"
                            app:roundAsCircle="true" />
                    </FrameLayout>

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="bottom"
                        android:layout_marginStart="16dp"
                        android:layout_marginBottom="3dp"
                        android:visibility="visible"
                        app:dateHeart="@{gender}"
                        tools:ignore="ContentDescription" />

                </FrameLayout>

                <com.facebook.drawee.view.SimpleDraweeView
                    android:id="@+id/ov_photo"
                    imageUrlNew="@{item.avatar}"
                    visibleGone="@{item.relationStatus != MessageConstants.MATCHING_STATUS_DATING &amp;&amp; item.relationStatus != MessageConstants.MATCHING_STATUS_LOVERS}"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginStart="1.5dp"
                    android:layout_marginTop="3.5dp"
                    android:layout_marginBottom="3.5dp"
                    app:roundAsCircle="true"
                    tools:background="@color/common_color_005EFF" />

            </FrameLayout>

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="@color/common_color_B7B7B7"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constraintBottom_toBottomOf="@id/ll_layout_contact_info"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/ll_layout_contact_info"
                app:setLastTime="@{item.lastTime}"
                tools:ignore="SpUsage"
                tools:text="星期三上午九点" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ll_layout_contact_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="7dp"
                android:layout_marginTop="5dp"
                android:orientation="horizontal"
                android:paddingEnd="6dp"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toStartOf="@+id/tv_time"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@+id/ll_avatar"
                app:layout_constraintTop_toTopOf="@+id/ll_avatar"
                tools:ignore="RtlSymmetry">

                <LinearLayout
                    android:id="@+id/fl_tags"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <include
                        android:id="@+id/clNoticeAvatars"
                        layout="@layout/chat_item_notice_avatars"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <com.kanzhun.common.views.image.OImageView
                        android:id="@+id/ivMood"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="4dp"
                        android:alpha="0.4"
                        android:visibility="gone"
                        app:imageUrl="@{item.moodIcon}"
                        app:visibleGone="@{!TextUtils.isEmpty(item.moodIcon)}"
                        tools:src="@drawable/icon_task_todo"
                        tools:visibility="gone" />

                    <com.kanzhun.common.views.image.OImageView
                        android:id="@+id/ivLoveStatus"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="5dp"
                        android:visibility="gone"
                        app:showChatListLoveStatus="@{item}"
                        tools:src="@drawable/icon_task_todo"
                        tools:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_tags"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/chat_bg_corner_9_color_4c9cf8"
                        android:gravity="center"
                        android:maxLines="1"
                        android:paddingLeft="6dp"
                        android:paddingTop="1dp"
                        android:paddingRight="6dp"
                        android:paddingBottom="1dp"
                        android:textColor="@color/common_white"
                        android:textSize="@dimen/common_text_sp_10"
                        app:conversationTag="@{item.tags}"
                        tools:ignore="SpUsage"
                        tools:text="官方"
                        tools:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_meetup_tags"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/chat_bg_corner_9_color_d9a877"
                        android:gravity="center"
                        android:maxLines="1"
                        android:paddingLeft="6dp"
                        android:paddingTop="1dp"
                        android:paddingRight="6dp"
                        android:paddingBottom="1dp"
                        android:text="见面计划"
                        android:textColor="#7C2800"
                        android:textSize="@dimen/common_text_sp_10"
                        android:visibility="gone"
                        tools:ignore="HardcodedText,SpUsage"
                        tools:visibility="gone" />

                    <TextView
                        android:id="@+id/tv_meet_plan_meet"
                        android:layout_width="wrap_content"
                        android:layout_height="18dp"
                        android:background="@drawable/common_bg_corner_10_color_e7f0ff"
                        android:gravity="center"
                        android:maxLines="1"
                        android:paddingLeft="5dp"
                        android:paddingTop="1dp"
                        android:paddingRight="5dp"
                        android:paddingBottom="1dp"
                        android:text="已见面"
                        android:textColor="@color/common_color_005EFF"
                        android:textSize="@dimen/common_text_sp_10"
                        android:visibility="gone"
                        app:qmui_radius="9dp"
                        tools:ignore="HardcodedText,SpUsage"
                        tools:visibility="gone" />

                </LinearLayout>

                <com.kanzhun.common.views.textview.BoldTextView
                    android:id="@+id/tv_nick_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="6dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{item.nickName}"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_16"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/fl_tags"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage"
                    tools:text="@string/common_long_placeholder" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constrainedWidth="true"
                app:layout_constraintEnd_toEndOf="@id/tv_time"
                app:layout_constraintStart_toStartOf="@id/ll_layout_contact_info"
                app:layout_constraintTop_toBottomOf="@id/ll_layout_contact_info">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingEnd="13dp"
                    tools:ignore="RtlSymmetry">

                    <FrameLayout
                        android:id="@+id/fl_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_last_msg"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/tv_last_msg">

                        <ImageView
                            android:id="@+id/iv_status_send_msg_fail"
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginEnd="6dp"
                            android:src="@mipmap/chat_icon_status_send_msg_fail"
                            app:visibleGone="@{item.lastMsgStatus == MessageConstants.MSG_STATE_FAILURE}"
                            tools:ignore="ContentDescription" />

                        <com.airbnb.lottie.LottieAnimationView
                            android:id="@+id/iv_status_sending_msg"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="6dp"
                            app:lottie_autoPlay="true"
                            app:lottie_fileName="chat_loading.json"
                            app:lottie_loop="true"
                            app:visibleGone="@{item.lastMsgStatus == MessageConstants.MSG_STATE_SENDING}" />

                    </FrameLayout>

                    <TextView
                        android:id="@+id/tv_last_msg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/common_color_7F7F7F"
                        android:textSize="@dimen/common_text_sp_14"
                        app:conversationText="@{item}"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toRightOf="@+id/fl_status"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="SpUsage"
                        tools:text="@string/common_long_placeholder" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_message_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/chat_bg_corner_9_color_fd6666"
                    android:gravity="center"
                    android:minWidth="17dp"
                    android:minHeight="17dp"
                    android:padding="2dp"
                    android:text='@{item.unreadCount > 99 ? "99+" : String.valueOf(item.unreadCount)}'
                    android:textColor="@color/common_white"
                    android:textSize="@dimen/common_text_sp_10"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="SpUsage"
                    tools:text="99+" />

                <com.qmuiteam.qmui.widget.QMUIRadiusImageView2
                    android:id="@+id/idRedPoint"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:layout_gravity="center_vertical"
                    android:background="@color/common_color_FE5252"
                    android:visibility="gone"
                    app:qmui_is_circle="true"
                    tools:visibility="visible" />

                <!-- 置顶、屏蔽情况下的各种样式（置顶、屏蔽、气泡红点） -->
                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/statusComposeView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    tools:composableName="com.kanzhun.marry.chat.util.ChatListItemStatusKt.StatusPreview" />

            </LinearLayout>

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/idLottieAnimationView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1:1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/swipe_menu"
            layout="@layout/chat_item_swipe_menu"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />

    </com.kanzhun.marry.chat.views.SwipeLayout>

</layout>
