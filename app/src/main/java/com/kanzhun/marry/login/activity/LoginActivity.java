package com.kanzhun.marry.login.activity;

import android.os.Bundle;
import android.text.TextUtils;
import androidx.navigation.NavController;
import androidx.navigation.NavGraph;
import androidx.navigation.Navigation;
import com.geetest.sdk.GT3ConfigBean;
import com.geetest.sdk.GT3ErrorBean;
import com.geetest.sdk.GT3GeetestUtils;
import com.geetest.sdk.GT3Listener;
import com.hpbr.apm.event.ApmAnalyzer;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.LoginUtil;
import com.kanzhun.foundation.router.LoginPageRouter;
import com.kanzhun.marry.BR;
import com.kanzhun.marry.R;
import com.kanzhun.marry.databinding.ActivityLoginBinding;
import com.kanzhun.marry.login.viewmodel.LoginViewModel;
import com.kanzhun.utils.L;
import com.kanzhun.utils.T;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.techwolf.lib.tlog.TLog;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/10
 */
@RouterUri(path = LoginPageRouter.APP_LOGIN_ACTIVITY)
public class LoginActivity extends FoundationVMActivity<ActivityLoginBinding, LoginViewModel> {
    public static final String ACTION_GEETEST = "action_geetest";

    private GT3GeetestUtils gt3GeetestUtils;
    private GT3ConfigBean gt3ConfigBean;

    @Override
    public int getContentLayoutId() {
        return R.layout.activity_login;
    }

    @Override
    public int getCallbackVariable() {
        return 0;
    }

    @Override
    public Object getCallback() {
        return null;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        getViewModel().dispose();
    }

    private void initView() {
        if (getIntent() != null) {
            //微信code
            getViewModel().setWechatCode(getIntent().getStringExtra(BundleConstants.BUNDLE_STRING_1));
            getViewModel().type = getIntent().getIntExtra(BundleConstants.BUNDLE_DATA_INT_1, 0);
            NavController navController = Navigation.findNavController(LoginActivity.this, R.id.fragment);
            NavGraph navGraph = navController.getNavInflater().inflate(R.navigation.login_navigation);
            if (getViewModel().type == 1) {
                navGraph.setStartDestination(R.id.selectModelFragment);
            } else if (getViewModel().type == 2) {
                navGraph.setStartDestination(R.id.inputInviteCodeFragment);
            } else {
                navGraph.setStartDestination(R.id.inputNumberFragment);
            }

            navController.setGraph(navGraph, getIntent().getExtras());
        } else {
            NavController navController = Navigation.findNavController(LoginActivity.this, R.id.fragment);
            NavGraph navGraph = navController.getNavInflater().inflate(R.navigation.login_navigation);
            navGraph.setStartDestination(R.id.inputNumberFragment);
            navController.setGraph(navGraph);
        }
        geeInit();

        getViewModel().getGt3GeetestStartLivaData().observe(this, s -> {
            if (!TextUtils.isEmpty(s)) {
                // 开启验证
                gt3GeetestUtils.startCustomFlow();
            }
        });
        getViewModel().getLoginSuccessLivaData().observe(this, phase -> {
            LoginUtil.goFirstEditOrMain(LoginActivity.this);
            AppUtil.finishActivityDelay(LoginActivity.this);
        });
    }

    private void geeInit() {
        gt3GeetestUtils = new GT3GeetestUtils(this);
        // 配置bean文件，也可在oncreate初始化
        gt3ConfigBean = new GT3ConfigBean();
        // 设置验证模式，1：bind，2：unbind
        gt3ConfigBean.setPattern(1);
        // 设置点击灰色区域是否消失，默认不消息
        gt3ConfigBean.setCanceledOnTouchOutside(false);
        // 设置debug模式，开代理可调试
//        gt3ConfigBean.setDebug(BuildConfig.DEBUG);
        // 设置语言，如果为null则使用系统默认语言
        gt3ConfigBean.setLang(null);
        // 设置加载webview超时时间，单位毫秒，默认10000，仅且webview加载静态文件超时，不包括之前的http请求
        gt3ConfigBean.setTimeout(10000);
        // 设置webview请求超时(用户点选或滑动完成，前端请求后端接口)，单位毫秒，默认10000
        gt3ConfigBean.setWebviewTimeout(10000);
        // 设置回调监听
        gt3ConfigBean.setListener(new GT3Listener() {

            /**
             * 验证码加载完成
             * @param duration 加载时间和版本等信息，为json格式
             */
            @Override
            public void onDialogReady(String duration) {
                TLog.info("LoginActivity", "onDialogReady :" + duration);

                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onDialogReady")
                        .p2(duration)
                        .report();
            }

            /**
             * 图形验证结果回调
             * @param code 1为正常 0为失败
             */
            @Override
            public void onReceiveCaptchaCode(int code) {
                TLog.info("LoginActivity", "onReceiveCaptchaCode :" + code);

                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onReceiveCaptchaCode")
                        .p2(String.valueOf(code))
                        .report();
            }

            /**
             * 验证结果
             */
            @Override
            public void onDialogResult(String result) {
                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onDialogResult")
                        .p2(result)
                        .report();

                // {"geetest_challenge":"287d89d178c8f1a30f23fa2ec8d08270c5","geetest_seccode":"f644c4335bfcf3cb69136ec837f86969|jordan","geetest_validate":"f644c4335bfcf3cb69136ec837f86969"}
                if (TextUtils.isEmpty(result)) {
                    T.ss("参数出错，请重试");
                    return;
                }
                // 通知成功
                gt3GeetestUtils.dismissGeetestDialog();// 验证成功，隐藏极验弹框
                L.e("TAG", result);
                getViewModel().gt3GeetestSuccess(result);
            }


            /**
             * 统计信息，参考接入文档
             */
            @Override
            public void onStatistics(String result) {
                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onStatistics")
                        .p2(result)
                        .report();

                TLog.info("LoginActivity", "onStatistics :" + result);
            }

            /**
             * 验证码被关闭
             * @param num 1 点击验证码的关闭按钮来关闭验证码, 2 点击屏幕关闭验证码, 3 点击返回键关闭验证码
             */
            @Override
            public void onClosed(int num) {
                TLog.info("LoginActivity", "onClosed :" + num);
                getViewModel().geetestonClosedLiveData.setValue(num);

                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onClosed")
                        .p2(String.valueOf(num))
                        .report();
            }

            /**
             * 验证成功回调
             */
            @Override
            public void onSuccess(String result) {
                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onSuccess")
                        .p2(result)
                        .report();
            }

            /**
             * 验证失败回调
             * @param errorBean 版本号，错误码，错误描述等信息
             */
            @Override
            public void onFailed(GT3ErrorBean errorBean) {
                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onFailed")
                        .p2(errorBean.toString())
                        .report();

                TLog.info("LoginActivity", "onFailed :" + errorBean);
            }

            /**
             * api1回调
             */
            @Override
            public void onButtonClick() {
                ApmAnalyzer.create().action(ACTION_GEETEST, "type_onButtonClick")
                        .report();

                try {
                    gt3ConfigBean.setApi1Json(new JSONObject(getViewModel().getGt3GeetestStartLivaData().getValue()));
                    // 继续api验证
                } catch (JSONException ignored) {
                    T.ss("参数出错，请重试");
                    gt3ConfigBean.setApi1Json(null);
                }
                gt3GeetestUtils.getGeetest();
            }
        });
        gt3GeetestUtils.init(gt3ConfigBean);

    }
}
