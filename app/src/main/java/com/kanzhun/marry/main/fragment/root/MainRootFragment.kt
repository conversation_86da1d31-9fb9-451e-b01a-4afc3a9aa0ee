package com.kanzhun.marry.main.fragment.root

import android.os.Bundle
import androidx.viewpager2.widget.ViewPager2
import com.kanzhun.common.kotlin.ext.sendIntLiveEvent
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.service.IMeRouterService
import com.kanzhun.foundation.utils.AppTheme
import com.kanzhun.foundation.utils.THEME
import com.kanzhun.marry.R
import com.kanzhun.marry.databinding.FragmentMainRootBinding
import com.kanzhun.marry.main.fragment.tab.ITabFragmentProvider
import com.kanzhun.marry.main.fragment.tab.MainTabFragmentProvider
import com.kanzhun.marry.main.performance.MainGuidePerformance
import com.kanzhun.marry.main.performance.MainNotifyLikedDialogPerformance
import com.kanzhun.marry.main.performance.MainTabChangePerformance
import com.kanzhun.marry.main.viewmodel.MainRootViewModel
import com.kanzhun.utils.L
import com.sankuai.waimai.router.Router
import com.techwolf.lib.tlog.TLog
import net.lucode.hackware.magicindicator.MagicIndicator

class MainRootFragment : BaseMainRootFragment<FragmentMainRootBinding, MainRootViewModel>() {
    val messageTabIndex = 2
    var interactTabIndex = 1


    //功能尽量都用AbsPerformance实现，不要在fragment中写逻辑，方便后期维护，避免fragment代码行数爆炸
    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    override fun getTabFragmentProvider(): ITabFragmentProvider = MainTabFragmentProvider()


    override fun getMainTabBeans(): List<MainTab> {
        if(AppTheme.getTheme() == THEME.CHRISTMAS){
            return mutableListOf(
                MainTab(title = resources.getString(R.string.app_main_tab_explore),
                    lottieName = "christmas/f1/data.json",
                    imageAssetsFolder = "christmas/f1/images"),

                MainTab(title = resources.getString(R.string.app_main_tab_interact),
                    lottieName = "christmas/f2/data.json",
                    imageAssetsFolder = "christmas/f2/images"),

                MainTab(title = resources.getString(R.string.app_main_tab_chat),
                    lottieName = "christmas/f3/data.json",
                    imageAssetsFolder = "christmas/f3/images"),

                MainTab(title = resources.getString(R.string.app_main_tab_me),
                    lottieName = "christmas/f4/data.json",
                    imageAssetsFolder = "christmas/f4/images")
            )
        }
        return mutableListOf(
            MainTab(title = resources.getString(R.string.app_main_tab_explore),
                lottieName = "main_icon_f1/explor.json",
                imageAssetsFolder = "main_icon_f1/images"),

            MainTab(title = resources.getString(R.string.app_main_tab_interact),
                lottieName = "main_icon_f2/interact.json",
                imageAssetsFolder = "main_icon_f2/images"),

            MainTab(title = resources.getString(R.string.app_main_tab_chat),
                lottieName = "main_icon_f3/chat.json",
                imageAssetsFolder = "main_icon_f3/images"),

            MainTab(title = resources.getString(R.string.app_main_tab_me),
                lottieName = "main_icon_f4/mine.json",
                imageAssetsFolder = "main_icon_f4/images")
        )
    }

    override fun getViewPager2(): ViewPager2 = mBinding.viewPager

    override fun getTabLayout(): MagicIndicator = mBinding.magicIndicator

    override fun preInit(arguments: Bundle) {
    }

    override fun initView() {
        //必须调用super的方法
        super.initView()
        //正式用户，默认选中第一个tab
        mBinding.viewPager.setCurrentItem(0, false)
        registerPerformance()
    }

    private fun registerPerformance() {
        //tab切换/红点相关功能
        performManager.addPerformance(MainTabChangePerformance(this))
        //蒙层引导功能
        performManager.addPerformance(MainGuidePerformance(this))
        performManager.addPerformance(MainNotifyLikedDialogPerformance(mActivity!!))

    }

    override fun initData() {
        // 获取灰度信息
        Router.getService(IMeRouterService::class.java, MePageRouter.ME_SERVICE)?.getAdviceGray()
    }

    override fun onRetry() {
    }

    override fun onTabClick(position: Int) {
        TLog.info("MainRootFragment","onTabClick position = $position")
        sendIntLiveEvent(Constants.POST_TAG_MAIN_TAB_SELECTED_INDEX,position)
    }

}