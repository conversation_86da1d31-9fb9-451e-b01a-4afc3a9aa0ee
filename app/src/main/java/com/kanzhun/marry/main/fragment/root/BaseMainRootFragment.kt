package com.kanzhun.marry.main.fragment.root

import android.content.Context
import android.view.LayoutInflater
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.viewbinding.ViewBinding
import androidx.viewpager2.widget.ViewPager2
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.sendBooleanLiveEvent
import com.kanzhun.common.kotlin.ext.to99PlusString
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.views.IPagerView
import com.kanzhun.foundation.views.ViewPager2Helper
import com.kanzhun.marry.R
import com.kanzhun.marry.chat.fragment.ChatListFragment
import com.kanzhun.marry.databinding.ItemMainBottomTabBinding
import com.kanzhun.marry.main.fragment.tab.ITabFragmentProvider
import com.kanzhun.marry.matching.fragment.MatchingF2Fragment
import com.techwolf.lib.tlog.TLog
import net.lucode.hackware.magicindicator.MagicIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView.OnPagerTitleChangeListener

abstract class BaseMainRootFragment<VB : ViewBinding, M : BaseViewModel> :
    BaseBindingFragment<VB, M>(), IPagerView {

    private lateinit var tabFragmentProvider: ITabFragmentProvider

    private lateinit var mFragmentAdapter: ViewPagerFragmentAdapter

    private val tabHolder by lazy {
        TabHolder()
    }

    private val tabResList = mutableListOf<MainTab>()

    override fun getStateLayout(): StateLayout? = null


    override fun initView() {
        tabFragmentProvider = getTabFragmentProvider()
        initViewPagerByFragments(tabFragmentProvider)
        initMagicIndicator()
    }

    /**
     * 初始化ViewPager和fragment
     */
    private fun initViewPagerByFragments(tabFragmentProvider: ITabFragmentProvider) {
        mFragmentAdapter = ViewPagerFragmentAdapter(this)
        mFragmentAdapter.setNewData(tabFragmentProvider.getTabList())
        getViewPager2().apply {
            adapter = mFragmentAdapter
            offscreenPageLimit = adapter!!.itemCount
            isUserInputEnabled = false
            registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    TLog.info("BaseMainRootFragment", "onPageSelected index = $position")

                    onTabClick(position)
                }
            })
        }
    }

    open fun createTabBinding(context: Context): ItemMainBottomTabBinding {
        return ItemMainBottomTabBinding.inflate(LayoutInflater.from(context))
    }

    private fun initMagicIndicator() {
        tabResList.clear()
        tabResList.addAll(getMainTabBeans())
        if (tabResList.size != tabFragmentProvider.getTabCount()) {
            throw IllegalArgumentException("bottom tab size does not equals fragments size")
        }
        getTabLayout().apply {
            val commonNavigator = CommonNavigator(requireContext())
            commonNavigator.isAdjustMode = true
            commonNavigator.adapter = object : CommonNavigatorAdapter() {
                override fun getCount(): Int {
                    return tabResList.size
                }

                override fun getTitleView(context: Context, index: Int): IPagerTitleView {
                    val tabBean = tabResList[index]
                    val commonPagerTitleView = CommonPagerTitleView(context)
                    val tabBinding = createTabBinding(context)
                    tabBinding.tvTitle.text = tabBean.title
                    setTabIcon(tabBean, tabBinding, false, true)
                    //记录下tab对应位置的binding，方便操作未读数
                    tabHolder.addTabView(index, tabBinding)
                    commonPagerTitleView.setContentView(tabBinding.root)
                    commonPagerTitleView.onPagerTitleChangeListener =
                        object : OnPagerTitleChangeListener {
                            override fun onSelected(index: Int, totalCount: Int) {
                                if (tabFragmentProvider.getTabFragment(index) is ChatListFragment) {
                                    (tabFragmentProvider.getTabFragment(index) as ChatListFragment).clearExpFlag()
                                }
                                setTabIcon(tabBean, tabBinding, true)
                                tabBinding.tvTitle.setTextColor(
                                    ContextCompat.getColor(
                                        requireContext(),
                                        R.color.common_color_292929
                                    )
                                )
                                if (tabFragmentProvider.getTabFragment(index) is MatchingF2Fragment) {
                                    sendBooleanLiveEvent(
                                        LivedataKeyMe.MATCHING_F2_FRAGMENT_ON_SELECTED,
                                        true
                                    )
                                }
                            }

                            override fun onDeselected(index: Int, totalCount: Int) {
                                setTabIcon(tabBean, tabBinding, false)
                                tabBinding.tvTitle.setTextColor(
                                    ContextCompat.getColor(
                                        requireContext(),
                                        R.color.common_color_B8B8B8
                                    )
                                )
                            }

                            override fun onLeave(
                                index: Int,
                                totalCount: Int,
                                leavePercent: Float,
                                leftToRight: Boolean
                            ) {
                            }

                            override fun onEnter(
                                index: Int,
                                totalCount: Int,
                                enterPercent: Float,
                                leftToRight: Boolean
                            ) {
                            }
                        }
                    commonPagerTitleView.setOnClickListener {
                        getViewPager2().setCurrentItem(index, false)
                    }
                    return commonPagerTitleView
                }

                override fun getIndicator(context: Context): IPagerIndicator? {
                    return null
                }
            }
            this.navigator = commonNavigator
            ViewPager2Helper.bind(getTabLayout(), getViewPager2())
        }
    }

    private fun setTabIcon(
        tabBean: MainTab,
        tabBinding: ItemMainBottomTabBinding,
        select: Boolean,
        reset: Boolean = false
    ) {
        if (tabBean.lottieName?.isNotEmpty() == true) {
            if (reset) {
                tabBinding.titleImg.imageAssetsFolder = tabBean.imageAssetsFolder
                tabBinding.titleImg.setAnimation(tabBean.lottieName)
            }
            if (select) {
                if (!tabBinding.titleImg.isAnimating) {
                    tabBinding.titleImg.playAnimation()
                } else {
                    if (tabBinding.titleImg.isAnimating) {
                        tabBinding.titleImg.pauseAnimation()
                    }
                    tabBinding.titleImg.progress = 1f
                }
            } else {
//                if(tabBinding.titleImg.isAnimating){
                tabBinding.titleImg.pauseAnimation()
//                }
                tabBinding.titleImg.progress = 0f
            }
        } else {
            if (select) {
                tabBinding.titleImg.setImageResource(tabBean.selectedIcon)
            } else {
                tabBinding.titleImg.setImageResource(tabBean.icon)
            }
        }
    }

    open fun onTabClick(position: Int) {

    }

    /**
     * 设置消息Tab未读数
     */
    fun setUnreadMessageCount(position: Int, count: Int) {
        tabHolder.setUnreadMessageCount(position, count)
        if (mFragmentAdapter.fragments?.get(position) is ChatListFragment) {
            (mFragmentAdapter.fragments?.get(position) as ChatListFragment).setUnreadCount(count)
        }
    }

    /**
     * 设置互动Tab未读数
     */
    fun setUnreadInteractCount(position: Int, count: Int) {
        tabHolder.setUnreadInteractCount(position, count)
        if (mFragmentAdapter.fragments?.get(position) is ChatListFragment) {
            (mFragmentAdapter.fragments?.get(position) as ChatListFragment).setUnreadCount(count)
        }
    }

    /**
     * 切换tab
     */
    fun setCurrentItem(position: Int) {
        TLog.info("BaseMainRootFragment", "setCurrentItem position = $position")
        if (position in 0 until tabFragmentProvider.getTabCount()) {
            getViewPager2().setCurrentItem(position, false)
        } else {
            getViewPager2().setCurrentItem(0, false)
        }

    }

    override fun getCurrentIndex(): Int {
        return getViewPager2().currentItem
    }

    override fun getCurrentFragment(): Fragment {
        return tabFragmentProvider.getTabFragment(getCurrentIndex())
    }

    fun getTabView(position: Int): ItemMainBottomTabBinding? {
        return tabHolder.getTabView(position)
    }

    abstract fun getTabFragmentProvider(): ITabFragmentProvider

    abstract fun getMainTabBeans(): List<MainTab>

    abstract fun getViewPager2(): ViewPager2

    abstract fun getTabLayout(): MagicIndicator

    //------------------- class ------------------------------------------

    data class MainTab(
        val title: String,
        val icon: Int = R.drawable.icon_main_tab_explore,
        val selectedIcon: Int = R.drawable.icon_main_tab_explore_sel,
        val lottieName: String? = null,
        val imageAssetsFolder: String? = null
    )

    private class TabHolder {
        private val tabsView = mutableMapOf<Int, ItemMainBottomTabBinding>()

        fun addTabView(position: Int, binding: ItemMainBottomTabBinding) {
            tabsView.put(position, binding)
        }

        fun getTabView(position: Int): ItemMainBottomTabBinding? {
            return tabsView[position]
        }

        /**
         * 规则
         * @param count=-1 展示红点，count=0 不展示，count>0 展示数字
         */
        fun setUnreadMessageCount(position: Int, count: Int) {
            val tabBinding = tabsView[position]
            tabBinding?.run {
                viewHasNewMsg.visible(count == -1) // -1 展示红点
                if (count > 0) { // 展示未读气泡数
                    tvCount.text = count.to99PlusString()
                }
                tvCount.visible(count > 0)
            }
        }

        /**
         * 规则
         * @param count <0 不展示，count=0 展示红点，count>0 展示数字
         */
        fun setUnreadInteractCount(position: Int, count: Int) {
            val tabBinding = tabsView[position]
            tabBinding?.run {
                viewHasNewMsg.visible(count == 0)
                if (count >= 1) {
                    tvCount.text = count.to99PlusString()
                }
                tvCount.visible(count >= 1)
            }
        }
    }

}


