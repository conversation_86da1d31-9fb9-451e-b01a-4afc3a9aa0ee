package com.kanzhun.marry.main.performance

import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.constant.HomeTabType
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.marry.kotlin.ext.finishToMainActivity
import com.kanzhun.marry.main.fragment.root.MainRootFragment
import com.kanzhun.utils.rxbus.RxBus
import com.techwolf.lib.tlog.TLog

/**
 * 首页tab切换功能、未读数显示功能（注意，家长模式下不在这里控制）
 */
class MainTabChangePerformance(val fragment: MainRootFragment) : AbsPerformance() {

    private var unReadMessageCount: Int = 0


    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        subscribeRxBus()
        //监听事件
        observeLiveEventBus()
        //监听点赞数
        observeLikeCount()
        //监听未读消息数
        observeUnreadMessageCount()
    }



    private fun subscribeRxBus() {
        RxBus.getInstance().subscribe<String>(fragment, Constants.POST_TAG_MATCHING_REQUIREMENT, object : RxBus.Callback<String?>() {
            override fun onEvent(s: String?) {
                //切换到我的界面
                finishToMainTab(3)
            }
        })
        RxBus.getInstance().subscribe<String>(this, Constants.POST_TAG_JUMP_TO_MATCHING, object : RxBus.Callback<String?>() {
            override fun onEvent(s: String?) {
                //切换到匹配界面
                finishToMainTab(0)
            }
        })
        RxBus.getInstance().subscribe<String>(this, Constants.POST_TAG_CHAT_REQUIREMENT, object : RxBus.Callback<String?>() {
            override fun onEvent(s: String?) {
                //切换到会话列表界面
                finishToMainTab(fragment.messageTabIndex)
            }
        })
        //认证成功
        RxBus.getInstance().subscribe<String>(this, CertificationIdentifySource.CERTIFY_SUCCESS_GO_MAIN_TAB, object : RxBus.Callback<String?>() {
            override fun onEvent(s: String?) {
                //切换到我的界面
                finishToMainActivity()
            }
        })
        RxBus.getInstance().subscribe<String>(this, CertificationIdentifySource.F2, object : RxBus.Callback<String?>() {
            override fun onEvent(s: String?) {
                finishToMainTab(1)
            }
        })

        RxBus.getInstance().subscribe<Int>(this, Constants.POST_TAG_GO_MAIN_TAB, object : RxBus.Callback<Int?>() {
            override fun onEvent(index: Int?) {
                //切换到我的界面
                finishToMainTab(index ?: 0)
            }
        })
    }

    private fun observeLiveEventBus() {
        fragment.liveEventObserve(LivedataKeyCommon.EVENT_KEY_MAIN_TAB) { it: HomeTabType ->
            when (it) {
                HomeTabType.EXPOSE -> {
                    finishToMainTab(0)
                    TLog.print("zl_log", "MainTabChangePerformance: 收到通知=%s", "切换F1")
                }

                HomeTabType.LIKE_ME -> {
                    finishToMainTab(1)

                }

                HomeTabType.SEE_ME -> {
                    finishToMainTab(1)

                }

                HomeTabType.I_LIKE -> {
                    finishToMainTab(1)

                }
                HomeTabType.PARENT_RECOMMEND->{
                    finishToMainTab(1)
                }

                HomeTabType.MESSAGE -> {
                    finishToMainTab(2)

                }

                HomeTabType.ME -> {
                    finishToMainTab(3)
                }
                else->{

                }
            }
        }
    }

    /**
     * 监听点赞数
     */
    private fun observeLikeCount() {
        ServiceManager.getInstance().conversationService?.interactTabSummaryInfo?.observe(fragment) {
            updateInteractTabBadge(it.getTabCount())
        }
    }

    private fun updateInteractTabBadge(interactCount:Int) {
        fragment.setUnreadInteractCount(fragment.interactTabIndex, interactCount)
    }


    /**
     * 监听未读消息数
     */
    private fun observeUnreadMessageCount() {
        ServiceManager.getInstance().conversationService?.unReadCountLiveData?.observe(fragment) {
            unReadMessageCount = it ?: 0
            updateMessageTabBadge()
        }
    }

    /**
     * 更新消息tab的未读数
     */
    private fun updateMessageTabBadge() {
        fragment.setUnreadMessageCount(fragment.messageTabIndex, unReadMessageCount)
    }


    private fun finishToMainTab(position: Int) {
        finishToMainActivity()
        fragment.setCurrentItem(position)
    }
}