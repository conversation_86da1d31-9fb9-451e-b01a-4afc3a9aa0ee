package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.decode
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.ParentPageRouter
import com.kanzhun.marry.me.identify.activity.AvatarAuthActivity
import com.techwolf.lib.tlog.TLog

class MeInfoProtocolAction :IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        if(AccountHelper.getInstance().isParent){
            ParentPageRouter.jumpToParentPreviewUserInfo(context,params["userId"], source = PageSource.PROTOCOL)
        }else{
            var from = params[ProtocolHelper.SOURCE]
            if(from?.isNotEmpty() == true){
                from = from.decode()
            }
           MePageRouter.jumpToInfoPreviewActivity(context,params["userId"],"",pageSource = PageSource.PROTOCOL,params["thirdId"],params["thirdType"]
               ,params["sourceType"], securityId = params["securityId"],protocolFrom = from?:"")
        }
    }
}