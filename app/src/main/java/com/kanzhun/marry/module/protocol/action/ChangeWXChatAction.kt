package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.utils.base.LText
import com.kanzhun.utils.rxbus.RxBus


class ChangeWXChatAction() : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val chatId = params["chatId"]
        ChatPageRouter.jumpToChangeWxActivity(context, chatId)

    }
}