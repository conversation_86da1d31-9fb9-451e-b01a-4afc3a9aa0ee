package com.kanzhun.marry.module.protocol

import com.kanzhun.foundation.Constants
import com.kanzhun.marry.module.protocol.action.AbroadGraduateAction
import com.kanzhun.marry.module.protocol.action.AccountAppealAction
import com.kanzhun.marry.module.protocol.action.AuthCenterProtocolAction
import com.kanzhun.marry.module.protocol.action.AvatarCertProtocolAction
import com.kanzhun.marry.module.protocol.action.CarCertProtocolAction
import com.kanzhun.marry.module.protocol.action.ChangeWXChatAction
import com.kanzhun.marry.module.protocol.action.ChristmasAction
import com.kanzhun.marry.module.protocol.action.ChristmasWelcomeAction
import com.kanzhun.marry.module.protocol.action.ComCertProtocolAction
import com.kanzhun.marry.module.protocol.action.EduCertProtocolAction
import com.kanzhun.marry.module.protocol.action.FILMListAction
import com.kanzhun.marry.module.protocol.action.FaceCertProtocolAction
import com.kanzhun.marry.module.protocol.action.FilmDialogAction
import com.kanzhun.marry.module.protocol.action.GraduateCodeAction
import com.kanzhun.marry.module.protocol.action.GraduatePicAction
import com.kanzhun.marry.module.protocol.action.HouseCertProtocolAction
import com.kanzhun.marry.module.protocol.action.IProtocolAction
import com.kanzhun.marry.module.protocol.action.LeveOneFinishAction
import com.kanzhun.marry.module.protocol.action.LikeListAction
import com.kanzhun.marry.module.protocol.action.LikeMeProtocolAction
import com.kanzhun.marry.module.protocol.action.LiveActivityAction
import com.kanzhun.marry.module.protocol.action.MBTITestProtocolAction
import com.kanzhun.marry.module.protocol.action.MainTabAction
import com.kanzhun.marry.module.protocol.action.MarryCertAction
import com.kanzhun.marry.module.protocol.action.MatchingFilmAction
import com.kanzhun.marry.module.protocol.action.MatchingGuestAction
import com.kanzhun.marry.module.protocol.action.MeBaseInfoEditAction
import com.kanzhun.marry.module.protocol.action.MeInfoABAction
import com.kanzhun.marry.module.protocol.action.MeInfoABAddAction
import com.kanzhun.marry.module.protocol.action.MeInfoAboutMeAction
import com.kanzhun.marry.module.protocol.action.MeInfoAnswerAction
import com.kanzhun.marry.module.protocol.action.MeInfoBeautyAction
import com.kanzhun.marry.module.protocol.action.MeInfoCertProtocolAction
import com.kanzhun.marry.module.protocol.action.MeInfoFamilyIntroAction
import com.kanzhun.marry.module.protocol.action.MeInfoLabelAction
import com.kanzhun.marry.module.protocol.action.MeInfoLikeAction
import com.kanzhun.marry.module.protocol.action.MeInfoLikeLabelAction
import com.kanzhun.marry.module.protocol.action.MeInfoProtocolAction
import com.kanzhun.marry.module.protocol.action.MeInfoSingleAction
import com.kanzhun.marry.module.protocol.action.MeInfoStoryAddAction
import com.kanzhun.marry.module.protocol.action.MeInfoVoiceAction
import com.kanzhun.marry.module.protocol.action.MeetingPlanAction
import com.kanzhun.marry.module.protocol.action.MomentDetailAction
import com.kanzhun.marry.module.protocol.action.MomentSubmitProtocolAction
import com.kanzhun.marry.module.protocol.action.MoodEditAction
import com.kanzhun.marry.module.protocol.action.MyInfoEditAction
import com.kanzhun.marry.module.protocol.action.MyMoodDetailAction
import com.kanzhun.marry.module.protocol.action.NoSupportAction
import com.kanzhun.marry.module.protocol.action.ReportAndHelpAction
import com.kanzhun.marry.module.protocol.action.ReportSecondPageAction
import com.kanzhun.marry.module.protocol.action.RevenueCertProtocolAction
import com.kanzhun.marry.module.protocol.action.SeeMeProtocolAction
import com.kanzhun.marry.module.protocol.action.SingleChatAction
import com.kanzhun.marry.module.protocol.action.SocialNotifyProtocolAction
import com.kanzhun.marry.module.protocol.action.UserNameEditAction
import com.kanzhun.marry.module.protocol.action.WebProtocolAction
import com.kanzhun.marry.module.protocol.action.XuexinCodeAction
import com.kanzhun.marry.module.protocol.action.XuexinWebviewAction

object ProtocolActionFactory {
    fun getAction(type: String?): IProtocolAction? {
        if (type == null) {
            return null
        }
        return when (type) {
            Constants.CHRISTMAS_DIALOG -> {
                ChristmasAction()
            }
            Constants.CHRISTMAS_WELCOME_DIALOG -> {
                ChristmasWelcomeAction()
            }
            Constants.TO_WEB_VIEW_URL -> {
                WebProtocolAction()
            }
            Constants.TO_MATCHING_PAGE->{//到匹配页面
                MainTabAction(0)
            }
            Constants.TO_MY_TAB->{//到我的页面
                MainTabAction(3)
            }
            Constants.TO_CHAT_LIST->{//聊天好友列表
                MainTabAction(2)
            }
            Constants.WECHAT_EXCHANGE->{//交换微信
                ChangeWXChatAction()
            }
            Constants.TO_CHAT_PAGE->{//到单聊页面
                SingleChatAction()
            }
            Constants.TO_PROFILE_EDIT -> {//编辑个人信息
                MyInfoEditAction()
            }
            Constants.TO_CERT_LIST -> {//认证列表
                AuthCenterProtocolAction()
            }
            Constants.TO_LIKE_ME_PAGE->{//喜欢我的页面
                LikeMeProtocolAction()
            }
            Constants.TO_SOCIAL_NOTIFY->{//社交圈通知
                SocialNotifyProtocolAction()
            }
            Constants.TO_MOMENT_SUBMIT->{//发布动态
                MomentSubmitProtocolAction()
            }
            Constants.TO_MAIN_SEE_ME_308->{//互动-看过我
                SeeMeProtocolAction()
            }
            Constants.TO_FILM_PROCESS_ID_309->{//胶片详情
                FilmDialogAction()
            }
            Constants.TO_MOMENT_DETAIL_310->{//动态详情页
                MomentDetailAction()
            }
            Constants.TO_AVATAR_CERT_401->{//头像认证页面
                AvatarCertProtocolAction()
            }
            Constants.TO_EDU_CERT_402->{//学历认证页面
                EduCertProtocolAction()
            }
            Constants.TO_COM_CERT_403->{//工作认证页面
                ComCertProtocolAction()
            }
            Constants.TO_HOUSE_CERT_404->{//房产认证页面
                HouseCertProtocolAction()
            }
            Constants.TO_CAR_CERT_405->{//车产认证页面
                CarCertProtocolAction()
            }
            Constants.TO_FACE_CERT_406->{//实名认证 人脸认证
                FaceCertProtocolAction()
            }
            Constants.TO_ME_INFO_CERT_407->{//个人完善信息
                MeInfoCertProtocolAction()
            }
            Constants.TO_ME_INFO_CERT_408->{//个人主页
                MeInfoProtocolAction()
            }
            Constants.TO_ME_INFO_CERT_409->{//MBTI测试页
                MBTITestProtocolAction()
            }
            Constants.TO_ME_REVENUE_CERT_410->{//收入认证
                RevenueCertProtocolAction()
            }
//            Constants.BEGINNER_GUIDE_COMPLETE ->{//"205" 新手引导-完善个人资料
//                NewUserTaskAction()
//            }
            Constants.NICKNAME_EDIT ->{//"206" 昵称编辑页
                UserNameEditAction()
            }
            Constants.BASE_INFO_EDIT ->{//"207";//基本信息编辑页
                MeBaseInfoEditAction()
            }
            Constants.ABOUT_ME_EDIT ->{//"208";//关于我编辑页
                MeInfoAboutMeAction()
            }
//            Constants.MY_LIFE_EDIT ->{//"209";//我的生活编辑页
//                MeStoryAction()
//            }
            Constants.INTEREST_EDIT ->{//"210";//我的兴趣爱好编辑页
                MeInfoLikeAction()
            }
            Constants.FAMILY_EDIT ->{//"211";//家庭介绍编辑页
                MeInfoFamilyIntroAction()
            }
            Constants.SINGLE_REASON_EDIT ->{//"212";//单身原因编辑页
                MeInfoSingleAction()
            }
            Constants.IDEAL_PARTNER_EDIT ->{//"213";//我的理想型编辑页
                MeInfoBeautyAction()
            }
            Constants.AB_FACE_EDIT ->{//"214";//AB 面编辑页
                MeInfoABAction()
            }
            Constants.MY_VOICE_EDIT ->{//"215";//我的声音编辑页-添加
                MeInfoVoiceAction()
            }
            Constants.MY_TEXT_EDIT ->{//"216";//我的问答编辑页
                MeInfoAnswerAction()
            }
            Constants.INTEREST_TAG_EDIT ->{//"217";//兴趣爱好标签页
                MeInfoLikeLabelAction()
            }
            Constants.USER_TAG_EDIT ->{//"218";//我的个性标签页
                MeInfoLabelAction()
            }
            Constants.USER_STROY_CAMERA ->{//"219";//我的生活跳转相册
                MeInfoStoryAddAction()
            }
            Constants.AB_FACE_ADD_LIST ->{//220 ab面新增列表选择页
                MeInfoABAddAction()
            }
            Constants.USER_REPORT ->{//221 举报二级页面
                ReportSecondPageAction()
            }
            Constants.USER_SETTING_HELP->{//222 帮助反馈
                ReportAndHelpAction()
            }
            Constants.MOOD_EDIT->{//223 心情编辑页
                MoodEditAction()
            }
            Constants.MOOD_DETAIL->{//224 主态心情详情页
                MyMoodDetailAction()
            }
            Constants.LIKE_LIST_ACTIVITY->{//226 个人页点赞列表
                LikeListAction()
            }
            Constants.FILM_LIST_ACTIVITY->{//227 冲洗我列表
                FILMListAction(0)
            }
            Constants.FILM_LIST_TAB_2_ACTIVITY->{//228 我冲洗列表
                FILMListAction(1)
            }
            Constants.TO_MATCHING_GUEST_ACTIVITY_411->{//411 活动嘉宾
                MatchingGuestAction()
            }
            Constants.TO_FILM_ACTIVITY_412->{//412 冲洗房
                MatchingFilmAction()
            }
            Constants.TO_MARRY_CERT_ACTIVITY_413->{//412 冲洗房
                MarryCertAction()
            }
            Constants.TO_ACCOUNT_APPEAL_ACTIVITY_414-> {//414 账号申诉
                AccountAppealAction()
            }
            Constants.TO_XUE_XIN_WEBVIEW_421->{//421 学信网在线验证
                XuexinWebviewAction()
            }
            Constants.TO_XUE_XIN_CODE_422->{//422 学信网在线验证码
                XuexinCodeAction()
            }
            Constants.TO_GRADUATE_CODE_423->{//423 学信网在线验证
                GraduateCodeAction()
            }
            Constants.TO_GRADUATE_PIC_424->{//424 毕业证/学位证编号
                GraduatePicAction()
            }
            Constants.TO_ABROAD_GRADUATE_425->{//425 教留服证书编号
                AbroadGraduateAction()
            }
            Constants.TO_LIVE_ACTIVITY_452->{//4752 现场活动
                LiveActivityAction()
            }
            Constants.TO_LEVEL_ONE_FINISH_229->{//229 刷新f1权限弹窗
                LeveOneFinishAction()
            }
            Constants.TO_MEETING_PLAN_470->{//470 见面计划模式跳转
                MeetingPlanAction()
            }
            else -> {//不支持类型
                NoSupportAction()
            }
        }

    }
}