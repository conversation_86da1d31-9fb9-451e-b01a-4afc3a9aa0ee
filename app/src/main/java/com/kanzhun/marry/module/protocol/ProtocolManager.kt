package com.kanzhun.marry.module.protocol

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import com.kanzhun.common.base.BaseApplication
import com.kanzhun.marry.module.protocol.action.WebViewProtocolAction
import com.kanzhun.utils.URLUtils
import com.techwolf.lib.tlog.TLog

class ProtocolManager {

    companion object {
        private val TAG = ProtocolManager::class.java.simpleName

        @JvmStatic
        fun parseProtocol(protocol: String?) {
            if (!protocol.isNullOrBlank()) {
                handleProtocol(protocol)
            }
        }

        @JvmStatic
        fun parseProtocol(context: Context, protocol: String?) {
            if (!protocol.isNullOrBlank()) {
                handleProtocol(protocol,context)
            }
        }

        @JvmStatic
        fun userProtocolCoreByLocal(type: String?,params: Map<String, String>) {
            if (!type.isNullOrBlank()) {
                startProtocolCore(type,params)
            }
        }


        private fun handleProtocol(protocol: String?,context:Context? = null) {
            try {
                if (protocol!!.startsWith("og")) {
                    val params = URLUtils.parseUrlParam(protocol)
                    if (params != null && !TextUtils.isEmpty(params["type"])) {
                        val type = params["type"]
                        startProtocolCore(type,params,context)
                    }
                } else if (protocol!!.startsWith("http")) {
                    if(context is Activity){
                        WebViewProtocolAction(protocol).startAction(context, mapOf())
                    }else{
                        WebViewProtocolAction(protocol).startAction(BaseApplication.getApplication().topContext, mapOf())
                    }
                }
                TLog.info(TAG, "handleProtocol : %s", protocol.toString())
            } catch (e: Exception) {
                TLog.error(TAG, "Push click error : %s", e.toString());
            }

        }

        private fun startProtocolCore(type: String?,params: Map<String, String>,context:Context? = null){
            if(context != null){
                ProtocolActionFactory.getAction(type)
                    ?.startAction(context, params)
            }else{
                ProtocolActionFactory.getAction(type)
                    ?.startAction(BaseApplication.getApplication().topContext, params)
            }
        }


    }
}