<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idRootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_color_000000_70"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    android:visibility="gone"
    tools:visibility="visible">

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/idLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/common_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_hideRadiusSide="bottom"
        app:qmui_radius="38dp"
        >

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@mipmap/matching_send_like_dialog_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="80dp">

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    app:common_circle="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:src="@mipmap/common_default_avatar" />

                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="20dp"
                    android:src="@drawable/common_black_close"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/iv_avatar" />

                <com.kanzhun.common.views.textview.BoldTextView
                    android:id="@+id/tvUserName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="10dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_28"
                    app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
                    app:layout_constraintEnd_toStartOf="@id/iv_close"
                    app:layout_constraintStart_toEndOf="@id/iv_avatar"
                    app:layout_constraintTop_toTopOf="@id/iv_avatar"
                    tools:text="@string/common_long_placeholder" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.core.widget.NestedScrollView
                android:id="@+id/idScrollView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="90dp"
                android:fillViewport="true"
                app:layout_constraintBottom_toTopOf="@+id/bottomContainer"
                app:layout_constraintTop_toBottomOf="@+id/iv_avatar">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="20dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/clEditContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/common_bg_conor_12_color_f5f5f5"
                        android:padding="12dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">


                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/et_text"
                            android:layout_width="match_parent"
                            android:layout_height="81dp"
                            android:background="@color/common_translate"
                            android:gravity="start|top"
                            android:hint="@string/matching_send_like_hint"
                            android:textColor="@color/common_color_4C4C4C"
                            android:textColorHint="@color/common_color_B2B2B2"
                            android:textSize="16sp"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="@string/common_long_placeholder" />

                        <TextView
                            android:id="@+id/tv_text_limit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:text="/50"
                            android:textColor="@color/common_color_B2B2B2"
                            android:textSize="@dimen/common_text_sp_12"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/et_text" />

                        <TextView
                            android:id="@+id/tv_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="5dp"
                            android:text="0"
                            android:textColor="@color/common_color_292929"
                            android:textSize="@dimen/common_text_sp_12"
                            app:layout_constraintEnd_toStartOf="@id/tv_text_limit"
                            app:layout_constraintTop_toBottomOf="@id/et_text" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        android:id="@+id/tvTemplate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="20dp"
                        android:drawableStart="@drawable/matching_icon_send_like_heart"
                        android:drawablePadding="4dp"
                        android:text="@string/matching_send_like_template"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_14"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/clEditContent"
                        tools:visibility="gone" />

                    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                        android:id="@+id/ll_bottom_float2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/app_layout_page_left_padding"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="@dimen/app_layout_page_right_padding"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/match_send_like_ai_bg"
                        android:orientation="vertical"
                        android:paddingHorizontal="12dp"
                        android:paddingTop="12dp"
                        android:visibility="gone"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvTemplate"
                        app:qmui_borderWidth="1dp"
                        app:qmui_radius="12dp"
                        tools:visibility="visible">

                        <ImageView
                            android:id="@+id/idIcon"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@mipmap/foundation_ic_ai_chat"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/idBottomTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:text="试试这样说："
                            android:textColor="@color/common_color_191919"
                            android:textSize="16dp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="@+id/idIcon"
                            app:layout_constraintStart_toEndOf="@+id/idIcon"
                            app:layout_constraintTop_toTopOf="@+id/idIcon" />

                        <TextView
                            android:id="@+id/idChange"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableLeft="@drawable/common_ic_refresh_right"
                            android:drawablePadding="2dp"
                            android:gravity="center"
                            android:text="换一换"
                            android:textColor="@color/common_color_292929"
                            android:textSize="14dp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="@+id/idBottomTitle"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/idBottomTitle" />

                        <com.qmuiteam.qmui.layout.QMUIFrameLayout
                            android:id="@+id/idCenterContent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="#F7FDFF"
                            android:minHeight="84dp"
                            android:padding="12dp"
                            app:layout_constraintTop_toBottomOf="@+id/idIcon"
                            app:qmui_borderWidth="1dp"
                            app:qmui_radius="12dp">

                            <TextView
                                android:id="@+id/idTVTryWrite"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:textColor="@color/common_color_292929"
                                android:textSize="14dp"
                                app:layout_constraintTop_toBottomOf="@+id/idChange"
                                tools:text="fdjaklfjdlkafjadsklfdjaklfjdlfjdlkafjadskl" />
                        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="0dp"
                            android:layout_marginStart="8dp"
                            android:gravity="center"
                            android:text="AI生成，仅供参考"
                            android:textColor="#62A4BE"
                            android:textSize="13dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/idCenterContent" />

                        <com.qmuiteam.qmui.layout.QMUIFrameLayout
                            android:id="@+id/idAutoEdit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="6dp"
                            android:layout_marginBottom="6dp"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/idCenterContent"
                            app:qmui_borderColor="@color/common_color_292929"
                            app:qmui_borderWidth="1.2dp"
                            app:qmui_radius="8dp"
                            tools:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="一键填充"
                                android:textColor="@color/common_color_292929"
                                android:textSize="12dp" />


                        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

                    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

                </LinearLayout>
            </androidx.core.widget.NestedScrollView>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/bottomContainer"
            android:layout_width="match_parent"
            android:layout_gravity="bottom"
            android:layout_height="80dp"
            android:background="@color/color_white"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.coorchice.library.SuperTextView
                android:id="@+id/btn_send_like"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:text="@string/send"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_16"
                app:stv_corner="25dp"
                app:stv_solid="@color/common_color_CCCCCC" />
        </FrameLayout>


    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

</FrameLayout>