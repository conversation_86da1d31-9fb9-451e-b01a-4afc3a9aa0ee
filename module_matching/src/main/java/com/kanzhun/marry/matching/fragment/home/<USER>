package com.kanzhun.marry.matching.fragment.home

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.marry.matching.model.ActivityLiveInfo
import com.kanzhun.marry.matching.model.AiMatchInfo
import com.kanzhun.marry.matching.model.GuestInfo
import com.kanzhun.marry.matching.repository.MatchRepository
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import com.techwolf.lib.tlog.TLog

class MatchGuestsPagerNewViewModel : ViewModel() {
    
    private val repository = MatchRepository()
    
    // UI状态
    private val _uiState: MutableState<MatchGuestsUiState> = mutableStateOf(MatchGuestsUiState())
    val uiState: State<MatchGuestsUiState> = _uiState
    
    // 加载状态
    private val _isLoading: MutableState<Boolean> = mutableStateOf(false)
    val isLoading: State<Boolean> = _isLoading
    
    // 错误状态
    private val _errorMessage: MutableState<String?> = mutableStateOf(null)
    val errorMessage: State<String?> = _errorMessage
    
    // 用于存储男女用户分类数据
    private val _maleGuests: MutableState<List<GuestInfo>> = mutableStateOf(emptyList())
    val maleGuests: State<List<GuestInfo>> = _maleGuests
    
    private val _femaleGuests: MutableState<List<GuestInfo>> = mutableStateOf(emptyList())
    val femaleGuests: State<List<GuestInfo>> = _femaleGuests
    
    // 当前选择的性别过滤器
    private val _selectedGender: MutableState<Int> = mutableStateOf(
        // 默认显示异性：男性用户(1)默认显示女生(1)，女性用户(2)默认显示男生(0)
        if (AccountHelper.getInstance().userInfo?.gender == 2) 0 else 1
    )
    val selectedGender: State<Int> = _selectedGender
    
    // 当前选中的tab索引
    private var currentTabIndex = 0
    
    // 设置当前选中的tab索引
    fun setCurrentTabIndex(index: Int) {
        currentTabIndex = index
    }
    
    // 获取当前选中的tab索引
    fun getCurrentTabIndex(): Int {
        return currentTabIndex
    }
    
    // 加载数据
    fun loadData(activityId: String) {
        val userGender = AccountHelper.getInstance().userInfo?.gender?:1
        // 确保初始化时依然显示异性
        _selectedGender.value = if (userGender == 2) 0 else 1
        
        _isLoading.value = true
        _errorMessage.value = null
        
        viewModelScope.launch {
            try {
                // 并行请求三个接口
                val maleDataDeferred = async { repository.getActivityLive(activityId, 1) }
                val femaleDataDeferred = async { repository.getActivityLive(activityId, 2) }
                val aiMatchDeferred = async { repository.getAiMatch(activityId) }
                
                // 等待所有请求完成
                val maleDataResult = maleDataDeferred.await()
                val femaleDataResult = femaleDataDeferred.await()
                val aiMatchResult = aiMatchDeferred.await()

                // 处理男生数据和活动信息
                maleDataResult.onSuccess { activityLiveInfo ->
                    com.techwolf.lib.tlog.TLog.info("MatchGuests", "Male ActivityLiveInfo from API: $activityLiveInfo")
                    
                    // 更新UI状态
                    val currentState = _uiState.value
                    _uiState.value = currentState.copy(
                        activityInfo = activityLiveInfo.activityInfo,
                        receiveLikeCount = activityLiveInfo.receiveLikeCount,
                        matchShowStatus = activityLiveInfo.matchShowStatus,
                        likeModelJumpUrl = activityLiveInfo.likeModelJumpUrl,
                        // 保持原有的AI相关状态
                        aiMatchTitle = currentState.aiMatchTitle,
                        aiMatchType = currentState.aiMatchType,
                        aiMatchList = currentState.aiMatchList?:emptyList()
                    )
                    
                    // 保存男生数据
                    val maleList = activityLiveInfo.userList?.filter { it.baseInfo.gender == 1 } ?: emptyList()
                    _maleGuests.value = maleList
                }.onFailure { error ->
                    com.techwolf.lib.tlog.TLog.error("MatchGuests", "Failed to load male data: ${error.message}")
                    _errorMessage.value = error.message ?: "Failed to load male information"
                }

                // 处理女生数据
                femaleDataResult.onSuccess { activityLiveInfo ->
                    com.techwolf.lib.tlog.TLog.info("MatchGuests", "Female ActivityLiveInfo from API: $activityLiveInfo")
                    val femaleList = activityLiveInfo.userList?.filter { it.baseInfo.gender == 2 } ?: emptyList()
                    _femaleGuests.value = femaleList
                }.onFailure { error ->
                    com.techwolf.lib.tlog.TLog.error("MatchGuests", "Failed to load female data: ${error.message}")
                    _errorMessage.value = error.message ?: "Failed to load female information"
                }
                
                // 处理AI匹配信息
                aiMatchResult.onSuccess { aiMatchInfo ->
                    com.techwolf.lib.tlog.TLog.info("MatchGuests", "AiMatchInfo from API: $aiMatchInfo")
                    val currentState = _uiState.value
                    _uiState.value = currentState.copy(
                        aiMatchType = aiMatchInfo.matchType,
                        aiMatchTitle = aiMatchInfo.title?:"",
                        aiMatchList = aiMatchInfo.matchList ?: emptyList(),
                        // 保持其他状态不变
                        activityInfo = currentState.activityInfo,
                        receiveLikeCount = currentState.receiveLikeCount,
                        matchShowStatus = currentState.matchShowStatus,
                        likeModelJumpUrl = currentState.likeModelJumpUrl
                    )
                    com.techwolf.lib.tlog.TLog.info("MatchGuests", "After AiMatchInfo update - aiMatchTitle: ${_uiState.value.aiMatchTitle}")
                }.onFailure { error ->
                    com.techwolf.lib.tlog.TLog.error("MatchGuests", "Failed to load AI match data: ${error.message}")
                    _errorMessage.value = error.message ?: "Failed to load AI match information"
                }
                
            } catch (e: Exception) {
                com.techwolf.lib.tlog.TLog.error("MatchGuests", "General error: ${e.message}")
                _errorMessage.value = e.message ?: "An unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    // 切换性别过滤器
    fun switchGenderFilter(gender: Int) {
        _selectedGender.value = gender
    }
    
    // 刷新数据，保持当前tab和列表位置
    fun refreshData(activityId: String) {
        viewModelScope.launch {
            try {
                // 记录当前tab索引和性别选择
                val currentGender = _selectedGender.value
                val tabIndex = currentTabIndex

                // 加载数据
                loadData(activityId)
                
                // 恢复之前的性别选择
                _selectedGender.value = currentGender
            } catch (e: Exception) {
                TLog.print("MatchGuestsPagerNewViewModel", "refreshData error: ${e.message}")
            }
        }
    }
    
    // 显示当前选择性别的用户列表
    fun getCurrentGenderGuests(): List<GuestInfo> {
        return if (_selectedGender.value == 0) {
            _maleGuests.value
        } else {
            _femaleGuests.value
        }
    }

    fun updateSendLikeStatus(uid: String?, relationShowStatus: Int) {
        uid?.let { id ->
            // Update male guests list if the user is found
            _maleGuests.value = _maleGuests.value.map { guest ->
                if (guest.baseInfo.encUserId == id) {
                    // Create a copy with updated relationShowStatus and preserve recommendTag
                    guest.copy(relationShowStatus = relationShowStatus).apply {
                        this.recommendTag = guest.recommendTag
                    }
                } else {
                    guest
                }
            }
            
            // Update female guests list if the user is found
            _femaleGuests.value = _femaleGuests.value.map { guest ->
                if (guest.baseInfo.encUserId == id) {
                    // Create a copy with updated relationShowStatus and preserve recommendTag
                    guest.copy(relationShowStatus = relationShowStatus).apply {
                        this.recommendTag = guest.recommendTag
                    }
                } else {
                    guest
                }
            }
        }
    }

    // UI 状态数据类
    data class MatchGuestsUiState(
        val activityInfo: ActivityLiveInfo.ActivityInfo? = null,
        val receiveLikeCount: Int = 0,
        val matchShowStatus: Int = 0,
        val aiMatchTitle: String = "",
        val aiMatchType: Int = 1,// 1 1v1 模式 2 1v多模式
        val likeModelJumpUrl: String = "",
        val aiMatchList: List<AiMatchInfo.MatchItem> = emptyList()
    )
    
    companion object {
        // 预览数据
        val previewData = MatchGuestsUiState(
            activityInfo = ActivityLiveInfo.ActivityInfo(
                showImages = listOf("https://via.placeholder.com/300"),
                title = "而立首届心动大会",
                tags = listOf("线上参与", "限时活动"),
                timeStr = "周六，6月24日 13:30",
                address = "北京奥加美术馆酒店"
            ),
            receiveLikeCount = 2,
            matchShowStatus = 1,
            likeModelJumpUrl = "",
            aiMatchType = 2,
            aiMatchTitle = "Deepseek官配CP大揭秘——追爱time",
            aiMatchList = listOf(
                AiMatchInfo.MatchItem(
                    male = AiMatchInfo.UserInfo(
                        securityId = "xxx",
                        nickName = "50 号男生",
                        avatar = "https://via.placeholder.com/300",
                        tinyAvatar = "https://via.placeholder.com/60",
                        signInNum = 50,
                        intro = "intro",
                        recommendTag = listOf<String>("123","456","789","123","456","789")
                    ),
                    female = AiMatchInfo.UserInfo(
                        securityId = "xxx",
                        nickName = "299 号女生",
                        avatar = "https://via.placeholder.com/300",
                        tinyAvatar = "https://via.placeholder.com/60",
                        signInNum = 299,
                        intro = "intro",
                        recommendTag = listOf<String>("123","456","789","123","456","789")

                    ),
                    reason = "推荐理由推荐理由推荐理由推荐理由推荐理由推荐理由推荐理由",
                    relationStatus = 20
                ), AiMatchInfo.MatchItem(
                    male = AiMatchInfo.UserInfo(
                        securityId = "xxx",
                        nickName = "50 号男生",
                        avatar = "https://via.placeholder.com/300",
                        tinyAvatar = "https://via.placeholder.com/60",
                        signInNum = 50,
                        intro = "intro",
                        recommendTag = listOf<String>("123","456","789","123","456","789")
                    ),
                    female = AiMatchInfo.UserInfo(
                        securityId = "xxx",
                        nickName = "299 号女生",
                        avatar = "https://via.placeholder.com/300",
                        tinyAvatar = "https://via.placeholder.com/60",
                        signInNum = 299,
                        intro = "intro",
                        recommendTag = listOf<String>("123","456","789","123","456","789")
                    ),
                    reason = "推荐理由推荐理由推荐理由推荐理由推荐理由推荐理由推荐理由",
                    relationStatus = 20
                )
            )
        )
    }
} 