package com.kanzhun.marry.matching.fragment.home

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.invisible
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.statusbar.addStatusMargin
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.kotlin.common.performance.PerformManager
import com.kanzhun.foundation.permission.PermissionManager
import com.kanzhun.foundation.screenShot.IScreenShotCallback
import com.kanzhun.foundation.screenShot.ScreenShotSetting
import com.kanzhun.foundation.utils.AppTheme
import com.kanzhun.foundation.utils.THEME
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.activity.MatchingRequirementActivity
import com.kanzhun.marry.matching.adpter.home.MatchingRecommendPageAdapter
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.databinding.MatchingFragmentRecommendListBinding
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHideSelfModePerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeExposePerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeNoviceGuidePerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeNoviceSubmitEventPerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeUpdateDataPerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingHomeUserGuidePerformance
import com.kanzhun.marry.matching.fragment.home.performance.MatchingLikeEventPerformance
import com.kanzhun.marry.matching.viewmodel.MatchingRecommendListViewModel
import com.kanzhun.utils.L
import com.kanzhun.utils.rxbus.RxBus
import com.kanzhun.utils.ui.ActivityUtils
import com.techwolf.lib.tlog.TLog
import com.youth.banner.Banner
import com.youth.banner.transformer.AlphaPageTransformer
import java.lang.reflect.Field

class MatchHomePagerFragment :
    BaseBindingFragment<MatchingFragmentRecommendListBinding, MatchingRecommendListViewModel>() {

    val mPageAdapter: MatchingRecommendPageAdapter by lazy {
        MatchingRecommendPageAdapter(emptyList())
    }

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    //数据曝光埋点
    private val matchingHomeExposePerformance: MatchingHomeExposePerformance by lazy {
        MatchingHomeExposePerformance(this)
    }


    override fun preInit(arguments: Bundle) {

    }

    override fun initView() {
        initTheme()
        mBinding.flTitle.addStatusMargin()
        mBinding.ivRight.setOnClickListener {
            Intent(requireActivity(), MatchingRequirementActivity::class.java).apply {
                requireActivity().startActivity(this)
            }
        }
        //隐私卡片点击透传问题
        mBinding.blocking.clHide.setOnClickListener {}
        initStateLayout()
        //卡片配置
        initViewPager()
        //数据更新相关逻辑
        performManager.addPerformance(MatchingHomeUpdateDataPerformance(this))
        //监听喜欢消息
        performManager.addPerformance(MatchingLikeEventPerformance(this))
        //「不让任何人看到我」模式相关逻辑
        performManager.addPerformance(MatchingHideSelfModePerformance(this, mBinding))
        //新手引导蒙层相关逻辑
        performManager.addPerformance(MatchingHomeUserGuidePerformance(this, mBinding))
        //新手引导顶部引导条相关逻辑
        performManager.addPerformance(MatchingHomeNoviceGuidePerformance(this, mBinding))
        //新手引导提交事件相关逻辑（最后一个提交弹出toast）
        performManager.addPerformance(MatchingHomeNoviceSubmitEventPerformance(this, mViewModel))
        //曝光上报埋点相关逻辑
        performManager.addPerformance(matchingHomeExposePerformance)
        initScreenShot()
    }

    private fun initScreenShot() {
        if (PermissionManager.checkAllSelfPermissions(
                requireContext(),
                getMediaPermission()
            )
        ) {
            val screenShotSetting = ScreenShotSetting(requireActivity())
            screenShotSetting.setScreenShotCallback(object : IScreenShotCallback {
                override fun screenShot(path: String?, uri: Uri?) {
                    if (uri.toString().isNotEmpty() == true) {
                        reportPoint("app-screenshot"){
                            source = "F1"
                            peer_id =
                                nowPosition?.let { val item = mViewModel.recommendList.value?.list?.get(it)
                                    if(item is RecommendUser){
                                        item.baseInfo?.encUserId
                                    }else{
                                        ""
                                    }
                                }.toString()
                        }
                    }
                }
            })
            lifecycle.addObserver(screenShotSetting)
        }
    }

    private fun getMediaPermission(): List<String> {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return listOf(
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO
            )
        } else {
            return listOf(
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE
            );
        }
    }

    private fun initTheme() {
        when(AppTheme.getTheme()){
            THEME.NORMAL -> {
                mBinding.ivBg.setBackgroundResource(R.mipmap.common_bg_theme_3)
            }
            THEME.CHRISTMAS -> {
                mBinding.ivBg.setBackgroundResource(R.mipmap.theme_christmas_mask)
            }
        }
    }

    /**
     * 卡片相关配置
     */
    private fun initViewPager() {
        mBinding.viewPager.addPageTransformer(AlphaPageTransformer(0.8F))
        mBinding.viewPager.setBannerGalleryEffect(12, 8, 0.90F)
        try {
            val recyclerViewField: Field = ViewPager2::class.java.getDeclaredField("mRecyclerView")
            recyclerViewField.isAccessible = true
            val recyclerView =
                recyclerViewField.get(mBinding.viewPager.getViewPager2()) as RecyclerView
            val touchSlopField: Field = RecyclerView::class.java.getDeclaredField("mTouchSlop")
            touchSlopField.isAccessible = true
            val touchSlop = touchSlopField.get(recyclerView) as Int
            touchSlopField.set(recyclerView, touchSlop * 2)
        } catch (ignore: java.lang.Exception) {

        }

        val banner = mBinding.viewPager as Banner<IPageBean, MatchingRecommendPageAdapter>
        banner.apply {
            setAdapter(mPageAdapter)
        }
    }

    fun updateBg(position: Int) {
        val showPinkBg = mViewModel.recommendList.value?.list?.get(position)?.showPinkBg() ?: false
        if (showPinkBg) {
            mBinding.ivBg.gone()
            mBinding.ivBg2.visible()
        }else{
            mBinding.ivBg.visible()
            mBinding.ivBg2.gone()
        }

    }

    var nowPosition:Int? = null
    fun updateNum(position: Int) {
        nowPosition = position
        val iPageBean = mViewModel.recommendList.value?.list?.getOrNull(position)
        if(iPageBean?.topString?.isNotEmpty() == true){
            mBinding.clNum.visible()
            mBinding.tvNum.text = iPageBean.topString
        }else{
            mBinding.clNum.gone()
        }
        mBinding.flTitle.visibility = if(iPageBean?.showTitle() == true)  View.VISIBLE else View.INVISIBLE
        mBinding.smartRefreshLayout.setEnableRefresh(iPageBean?.enableFresh()?:true)
    }

    private fun initStateLayout() {
        mBinding.stateLayout.setRetryIds(R.id.btErrorRetry)
        mViewModel.showLoading()
    }

    override fun initData() {
        mViewModel.getRecommendData()
        mViewModel.recommendList.observe(this) {
            resetLoadingState()
            if (it.newData) {
                if (mPageAdapter.itemCount > 0 && mBinding.viewPager.currentItem > 0) {
                    mBinding.viewPager.setCurrentItem(0, false)
                }
                mPageAdapter.setDatas(it.list)
                //第0个需要手动通知下
                matchingHomeExposePerformance.exposeCard(0)
            } else {
                mPageAdapter.updateList(it.list)
                matchingHomeExposePerformance.exposeCard(mBinding.viewPager.currentItem)
            }

            updateNum(mBinding.viewPager.currentItem)
            AppThreadFactory.getMainHandler().postDelayed({
                if(ActivityUtils.isValid(mActivity)) {
                    if (mBinding.viewPager.viewPager2.beginFakeDrag()) {
                        mBinding.viewPager.viewPager2.fakeDragBy(-0.5f) // 负值表示向右滑动，正值表示向左滑动
                        mBinding.viewPager.viewPager2.fakeDragBy(0.5f) // 负值表示向右滑动，正值表示向左滑动
                        mBinding.viewPager.viewPager2.endFakeDrag()
                    }
                }
            },100)
        }
        liveEventBusObserve(LivedataKeyMe.MATCHING_F2_FRAGMENT_ON_SELECTED) { _: Boolean ->
            matchingHomeExposePerformance.exposeCard(mBinding.viewPager.currentItem)
        }

        liveEventBusObserve(Constants.POST_TAG_REFRESH_F1) { t: Boolean ->
            if (t) {
                TLog.print("zl_log", "MatchHomePagerFragment: RxBus: 刷新F1数据")
                mViewModel.getRecommendData()
            }
        }

        liveEventBusObserve(Constants.POST_F1_SCROLL_TO) { t: Int ->
            mBinding.viewPager.currentItem = t
        }

        RxBus.getInstance().subscribe(
            this,
            Constants.POST_TAG_MATCH_TAB_SELECT_INDEX,
            object : RxBus.Callback<Int>() {
                override fun onEvent(index: Int) {
                    mBinding.viewPager.viewPager2.currentItem = index
                }
            })
    }

    private fun resetLoadingState() {
        if (mBinding.smartRefreshLayout.isRefreshing) {
            mBinding.smartRefreshLayout.finishRefresh()
        }
    }

    override fun onRetry() {
        mViewModel.getRecommendData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        performManager.unRegister()
    }

    override fun getStateLayout() = mBinding.stateLayout


}