package com.kanzhun.marry.matching.activity

import android.R.attr.fragment
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.kotlin.ui.statelayout.StateLayout
import com.kanzhun.common.kotlin.ui.statusbar.fullScreenAndBlackText
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.bean.SerializableMap
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.databinding.MatchingGuestsActivityBinding
import com.kanzhun.marry.matching.fragment.home.MatchGuestsPagerFragment
import com.kanzhun.marry.matching.fragment.home.MatchGuestsTestFragment
import com.kanzhun.marry.matching.fragment.home.MatchHomePagerFragment
import com.kanzhun.marry.matching.viewmodel.MatchingGuestsActivityViewModel
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [MatchingPageRouter.MATCHING_GUEST_ACTIVITY])
class GuestsActivity:  BaseBindingActivity<MatchingGuestsActivityBinding, MatchingGuestsActivityViewModel>() {
    override fun preInit(intent: Intent) {
    }

    //首页是白底黑状态栏的全面屏
    override var setStatusBar = {
        fullScreenAndBlackText()
    }

    override fun initView() {
        initMatchFragment()
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    private fun initMatchFragment() {
        val fragment: Fragment
        if(intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT,0) == 1){
            fragment = MatchGuestsTestFragment()
        }else{
            fragment = MatchGuestsPagerFragment()
        }
        val b = Bundle()
        b.putSerializable(BundleConstants.BUNDLE_DATA_SERIALIZABLE,intent.getSerializableExtra(BundleConstants.BUNDLE_DATA_SERIALIZABLE))
        fragment.arguments = b
        supportFragmentManager.beginTransaction().add(R.id.container,
            fragment,
            Constants.MAIN_FRAGMENT_GUEST)
            .commit()
    }

    override fun getStateLayout(): StateLayout = mBinding.idStateLayout
}